'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDocuments, useUsers } from '@/lib/hooks';
import { supabaseClient } from '@/lib/supabase/client';
import { DocumentActivity } from '@/lib/types/database-modules';
import {
  CheckCircle2,
  Copy,
  Edit,
  Eye,
  Mail,
  MessageSquare,
  Trash as TrashIcon,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { ActivityList } from './ActivityList';

interface Collaborator {
  id: string | undefined;
  name: string;
  email: string;
  avatarUrl?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'active' | 'pending';
  lastActive?: string;
}

interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatarUrl?: string;
  content: string;
  timestamp: string;
  resolved: boolean;
}

interface CollaborationPanelProps {
  documentId: string;
  documentTitle: string;
  currentUserId: string;
}

export function CollaborationPanel({
  documentId,
  // documentTitle is not used but kept for API compatibility
  documentTitle: _documentTitle,
  currentUserId,
}: CollaborationPanelProps) {
  // Get document functions from useDocuments hook
  const {
    shareDocumentWithUser,
    getDocumentCollaborators,
    removeCollaborator,
    updateCollaboratorPermission,
    addDocumentActivity,
    fetchActivities: fetchDocumentActivities,
    getDocumentComments,
    addDocumentComment,
    updateDocumentComment,
    deleteDocumentComment,
    getById,
  } = useDocuments();

  // Get user functions from useUsers hook
  const { getProfile } = useUsers();
  // Using the imported supabaseClient directly
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [activities, setActivities] = useState<DocumentActivity[]>([]);
  const [shareLink, setShareLink] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);
  const [isLoadingComments, setIsLoadingComments] = useState(true);

  // Function to fetch document activities using the hook
  const fetchActivities = async () => {
    if (!documentId) return;

    setIsLoadingActivities(true);
    try {
      // Use the fetchActivities function from the useDocuments hook
      const documentActivities = await fetchDocumentActivities(documentId);

      // Set the activities state
      setActivities(documentActivities);
      console.log('Activities fetched:', documentActivities.length);
    } catch (error) {
      console.error('Error fetching document activities:', error);
      setActivities([]);
    } finally {
      setIsLoadingActivities(false);
    }
  };

  // Function to fetch document comments using the hook
  const fetchComments = async () => {
    if (!documentId) return;

    setIsLoadingComments(true);
    try {
      // Use the getDocumentComments function from the useDocuments hook
      const documentComments = await getDocumentComments(documentId);

      // Check if documentComments is an array and set the comments state
      if (Array.isArray(documentComments)) {
        setComments(documentComments);
        console.log('Comments fetched:', documentComments.length);
      } else {
        console.error(
          'Expected array of comments but got:',
          typeof documentComments
        );
        setComments([]);
      }
    } catch (error) {
      console.error('Error fetching document comments:', error);
      setComments([]);
    } finally {
      setIsLoadingComments(false);
    }
  };

  useEffect(() => {
    const fetchCollaborationData = async () => {
      setIsLoading(true);
      try {
        // Get the current user's profile using the hook
        const userProfile = await getProfile();

        // Create the owner collaborator from real user data
        const ownerCollaborator: Collaborator = {
          id: currentUserId || '',
          name: userProfile?.full_name || 'You (Owner)',
          email: userProfile?.email || '<EMAIL>',
          avatarUrl: `https://ui-avatars.com/api/?name=${encodeURIComponent(userProfile?.full_name || 'You')}`,
          role: 'owner' as const,
          status: 'active' as const,
          lastActive: 'Just now',
        };

        // Only try to fetch collaborators if we have a valid document ID
        if (documentId) {
          try {
            // Get document details to check if current user is the owner
            const document = await getById(documentId);

            const isOwner = document?.owner_id === currentUserId;

            // Get real collaborators from the database
            console.log('Fetching collaborators for document:', documentId);
            const dbCollaborators = await getDocumentCollaborators(documentId);
            console.log('Collaborators fetched:', dbCollaborators);

            if (dbCollaborators && dbCollaborators.length > 0) {
              // Transform the data to match our Collaborator interface
              const transformedCollaborators: Collaborator[] =
                dbCollaborators.map((collab) => {
                  // Extract user info from the joined user record
                  const userInfo = collab.user as {
                    id?: string;
                    email?: string;
                    full_name?: string;
                    avatar_url?: string;
                  } | null;

                  // Ensure we have valid data with fallbacks
                  const id = collab.id || '';
                  // We don't need userId here as we're using the id from the collaboration record
                  const name =
                    userInfo?.full_name ||
                    userInfo?.email?.split('@')[0] ||
                    'Unknown User';
                  const email = userInfo?.email || '<EMAIL>';
                  const role =
                    collab.permission === 'edit'
                      ? ('editor' as const)
                      : ('viewer' as const);

                  return {
                    id,
                    name,
                    email,
                    avatarUrl:
                      userInfo?.avatar_url ||
                      `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`,
                    role,
                    status: 'active' as const,
                    lastActive: collab.created_at
                      ? new Date(collab.created_at).toLocaleString()
                      : 'Unknown',
                  } as Collaborator;
                });

              // Add the current user as owner if they are the owner and not already in the list
              if (
                isOwner &&
                !transformedCollaborators.some((c) => c.id === currentUserId)
              ) {
                transformedCollaborators.unshift(ownerCollaborator);
              }

              setCollaborators(transformedCollaborators);
            } else if (isOwner) {
              // No collaborators found, but user is the owner
              setCollaborators([ownerCollaborator]);
            } else {
              // No collaborators and user is not the owner (shouldn't happen)
              setCollaborators([]);
            }

            // Generate a real share link
            const baseUrl = window.location.origin;
            const shareLink = `${baseUrl}/share/${documentId}`;
            setShareLink(shareLink);

            // Fetch document comments using the hook
            fetchComments();
          } catch (err) {
            console.error('Error fetching document collaborators:', err);
            console.error('Error details:', JSON.stringify(err, null, 2));
            // Fallback to owner only if there's an error
            setCollaborators([ownerCollaborator]);
            toast.error('Error fetching collaborators', {
              description: 'Using default collaborator information',
            });
          }
        } else {
          // No document ID, just use the owner
          setCollaborators([ownerCollaborator]);
          setComments([]);
          setShareLink('');
        }
      } catch (error) {
        console.error('Error in collaboration data fetch:', error);
        toast.error('Error loading collaboration data');
        // Set empty data as fallback
        setCollaborators([]);
        setComments([]);
        setShareLink('');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCollaborationData();
  }, [
    documentId,
    getDocumentCollaborators,
    currentUserId,
    getById,
    getProfile,
  ]);

  // Fetch activities when document changes
  useEffect(() => {
    if (documentId) {
      fetchActivities();
      fetchComments();
    }
  }, [documentId]);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareLink);
    toast.success('Link copied to clipboard');
  };

  const handleInviteUser = async (email: string, role: 'editor' | 'viewer') => {
    // Show loading toast
    const toastId = toast.loading('Inviting user...');

    try {
      // Use the shareDocumentWithUser function to share the document with the user
      const permission = role === 'editor' ? 'edit' : 'view';
      const result = await shareDocumentWithUser(documentId, email, permission);

      if (result) {
        // Track the share activity
        if (documentId) {
          try {
            await addDocumentActivity(documentId, 'share', {
              shared_with: email,
              role,
            });
          } catch (activityError) {
            console.error('Error tracking share activity:', activityError);
          }
        }

        // Fetch the user profile if available
        let userProfile = null;
        try {
          // Try to get the user profile by email
          const { data, error } = await supabaseClient
            .from('profiles')
            .select('id, full_name, email')
            .eq('email', email)
            .maybeSingle();

          if (!error && data) {
            userProfile = data;
          }
        } catch (profileError) {
          console.error('Error fetching user profile:', profileError);
        }

        // Add the new collaborator to the list
        const newCollaborator: Collaborator = {
          id: result.id,
          name: userProfile?.full_name || email.split('@')[0],
          email,
          avatarUrl: `https://ui-avatars.com/api/?name=${encodeURIComponent(userProfile?.full_name || email.split('@')[0])}`,
          role,
          status: 'pending',
          lastActive: new Date().toLocaleString(),
        };

        setCollaborators([...collaborators, newCollaborator]);

        // Update toast to success
        toast.success('Invitation sent', {
          id: toastId,
          description: `Invitation sent to ${email}`,
        });

        // Refresh the collaborator list to get the latest data
        setTimeout(async () => {
          const refreshedCollaborators =
            await getDocumentCollaborators(documentId);
          if (refreshedCollaborators && refreshedCollaborators.length > 0) {
            const transformedCollaborators = refreshedCollaborators.map(
              (collab) => {
                // Extract user info from the joined user record
                const userInfo = collab.user as {
                  id?: string;
                  email?: string;
                  full_name?: string;
                  avatar_url?: string;
                } | null;

                return {
                  id: collab.id || '',
                  name:
                    userInfo?.full_name ||
                    userInfo?.email?.split('@')[0] ||
                    'Unknown User',
                  email: userInfo?.email || '<EMAIL>',
                  avatarUrl:
                    userInfo?.avatar_url ||
                    `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo?.full_name || userInfo?.email?.split('@')[0] || 'Unknown')}`,
                  role:
                    collab.permission === 'edit'
                      ? ('editor' as const)
                      : ('viewer' as const),
                  status: 'active' as const,
                  lastActive: new Date(collab.created_at).toLocaleString(),
                };
              }
            );
            setCollaborators(transformedCollaborators);
          }
        }, 1000); // Wait a second for the database to update
      }
    } catch (error) {
      console.error('Error inviting user:', error);
      // Update toast to error
      toast.error('Failed to invite user', {
        id: toastId,
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const handleRemoveCollaborator = async (id: string | undefined) => {
    if (!id) {
      toast.error('Invalid collaborator ID');
      return;
    }

    // Show loading toast
    const toastId = toast.loading('Removing collaborator...');

    try {
      // Use the removeCollaborator function to remove the collaborator
      const success = await removeCollaborator(id);

      if (success) {
        // Remove the collaborator from the list
        setCollaborators(collaborators.filter((c) => c.id !== id));

        // Update toast to success
        toast.success('Collaborator removed', {
          id: toastId,
          description: 'The collaborator has been removed from this document.',
        });

        // Track the activity
        if (documentId) {
          try {
            await addDocumentActivity(documentId, 'share', {
              action: 'remove_collaborator',
              collaborator_id: id,
            });
          } catch (activityError) {
            console.error(
              'Error tracking remove collaborator activity:',
              activityError
            );
          }
        }
      } else {
        // Update toast to error
        toast.error('Failed to remove collaborator', {
          id: toastId,
          description: 'An error occurred while removing the collaborator.',
        });
      }
    } catch (error) {
      console.error('Error removing collaborator:', error);
      // Update toast to error
      toast.error('Failed to remove collaborator', {
        id: toastId,
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const handleChangeRole = async (
    id: string | undefined,
    newRole: 'editor' | 'viewer'
  ) => {
    if (!id) {
      toast.error('Invalid collaborator ID');
      return;
    }

    // Show loading toast
    const toastId = toast.loading('Updating role...');

    try {
      // Find the collaborator to update
      const collaborator = collaborators.find((c) => c.id === id);
      if (!collaborator) {
        toast.error('Collaborator not found', { id: toastId });
        return;
      }

      // Convert the role to a permission value
      const permission = newRole === 'editor' ? 'edit' : 'view';

      // Use the updateCollaboratorPermission function from useDocuments
      const success = await updateCollaboratorPermission(id, permission);

      if (success) {
        // Update the local state
        setCollaborators(
          collaborators.map((c) => (c.id === id ? { ...c, role: newRole } : c))
        );

        // Update toast to success
        toast.success('Role updated', {
          id: toastId,
          description: `Collaborator role has been updated to ${newRole}.`,
        });

        // Track the activity
        if (documentId) {
          try {
            await addDocumentActivity(documentId, 'share', {
              action: 'update_permission',
              collaborator_id: id,
              new_role: newRole,
            });
          } catch (activityError) {
            console.error(
              'Error tracking update role activity:',
              activityError
            );
          }
        }

        // Refresh the collaborator list
        setTimeout(async () => {
          const refreshedCollaborators =
            await getDocumentCollaborators(documentId);
          if (refreshedCollaborators && refreshedCollaborators.length > 0) {
            const transformedCollaborators = refreshedCollaborators.map(
              (collab) => {
                // Extract user info from the joined user record
                const userInfo = collab.user as {
                  id?: string;
                  email?: string;
                  full_name?: string;
                  avatar_url?: string;
                } | null;

                return {
                  id: collab.id || '',
                  name:
                    userInfo?.full_name ||
                    userInfo?.email?.split('@')[0] ||
                    'Unknown User',
                  email: userInfo?.email || '<EMAIL>',
                  avatarUrl:
                    userInfo?.avatar_url ||
                    `https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo?.full_name || userInfo?.email?.split('@')[0] || 'Unknown')}`,
                  role:
                    collab.permission === 'edit'
                      ? ('editor' as const)
                      : ('viewer' as const),
                  status: 'active' as const,
                  lastActive: new Date(collab.created_at).toLocaleString(),
                };
              }
            );
            setCollaborators(transformedCollaborators);
          }
        }, 1000);
      } else {
        // Update toast to error
        toast.error('Failed to update role', {
          id: toastId,
          description:
            'An error occurred while updating the collaborator role.',
        });
      }
    } catch (error) {
      console.error('Error updating collaborator role:', error);
      // Update toast to error
      toast.error('Failed to update role', {
        id: toastId,
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const handleResolveComment = async (id: string) => {
    try {
      // Use the updateDocumentComment function from the useDocuments hook
      await updateDocumentComment(id, { resolved: true }, documentId);

      // Update the local state
      setComments(
        comments.map((c) => (c.id === id ? { ...c, resolved: true } : c))
      );
    } catch (error) {
      console.error('Error resolving comment:', error);
    }
  };

  const handleDeleteComment = async (id: string) => {
    try {
      // Use the deleteDocumentComment function from the useDocuments hook
      await deleteDocumentComment(id, documentId);

      // Remove the comment from the local state
      setComments(comments.filter((c) => c.id !== id));
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };

  const handleAddComment = async (content: string) => {
    if (!content.trim() || !documentId) {
      toast.error('Please enter a comment');
      return;
    }

    try {
      // Use the addDocumentComment function from the useDocuments hook
      await addDocumentComment(documentId, content, currentUserId);

      // Clear the comment input
      const commentInput = document.getElementById(
        'new-comment'
      ) as HTMLTextAreaElement;
      if (commentInput) {
        commentInput.value = '';
      }

      // Refresh all comments to ensure we have the latest data
      fetchComments();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Collaboration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <div className="animate-pulse flex flex-col items-center">
              <div className="h-8 w-32 bg-neutral-200 rounded mb-4"></div>
              <div className="h-4 w-48 bg-neutral-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          Collaboration
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="sharing">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="sharing">Sharing</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="sharing" className="space-y-4 pt-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Share Link</h3>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-2 bg-neutral-50 rounded-md text-sm truncate">
                  {shareLink}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="shrink-0"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Invite People</h3>
              <div className="flex items-center gap-2">
                <input
                  type="email"
                  placeholder="Email address"
                  className="flex-1 p-2 border rounded-md text-sm"
                  id="invite-email"
                />
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const email = (
                        document.getElementById(
                          'invite-email'
                        ) as HTMLInputElement
                      ).value;
                      if (email) handleInviteUser(email, 'viewer');
                    }}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Viewer
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => {
                      const email = (
                        document.getElementById(
                          'invite-email'
                        ) as HTMLInputElement
                      ).value;
                      if (email) handleInviteUser(email, 'editor');
                    }}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Editor
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">People with Access</h3>
              <ScrollArea className="h-[200px]">
                <div className="space-y-2">
                  {collaborators.map((collaborator) => (
                    <div
                      key={collaborator.id}
                      className="flex items-center justify-between p-2 hover:bg-neutral-50 rounded-md"
                    >
                      <div className="flex items-center gap-2">
                        <div className="relative h-8 w-8 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center text-sm font-medium">
                          {collaborator.avatarUrl ? (
                            <Image
                              src={collaborator.avatarUrl}
                              alt={collaborator.name}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <span>{collaborator.name.charAt(0)}</span>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm flex items-center gap-2">
                            {collaborator.name}
                            {collaborator.status === 'pending' && (
                              <Badge variant="outline" className="text-xs">
                                Pending
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-neutral-500 flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {collaborator.email}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            collaborator.role === 'owner'
                              ? 'default'
                              : 'outline'
                          }
                          className="text-xs"
                        >
                          {collaborator.role === 'editor' && (
                            <Edit className="h-3 w-3 mr-1" />
                          )}
                          {collaborator.role === 'viewer' && (
                            <Eye className="h-3 w-3 mr-1" />
                          )}
                          {collaborator.role.charAt(0).toUpperCase() +
                            collaborator.role.slice(1)}
                        </Badge>

                        {collaborator.role !== 'owner' && (
                          <div className="relative group">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2"
                            >
                              •••
                            </Button>
                            <div className="absolute right-0 top-full mt-1 w-32 bg-white shadow-lg rounded-md overflow-hidden z-10 hidden group-hover:block">
                              <button
                                className="w-full text-left px-3 py-2 text-sm hover:bg-neutral-50"
                                onClick={() =>
                                  handleChangeRole(
                                    collaborator.id,
                                    collaborator.role === 'editor'
                                      ? 'viewer'
                                      : 'editor'
                                  )
                                }
                              >
                                Change to{' '}
                                {collaborator.role === 'editor'
                                  ? 'Viewer'
                                  : 'Editor'}
                              </button>
                              <button
                                className="w-full text-left px-3 py-2 text-sm text-red-500 hover:bg-red-50"
                                onClick={() =>
                                  handleRemoveCollaborator(collaborator.id)
                                }
                              >
                                Remove
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="comments" className="space-y-4 pt-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Comments</h3>
              <ScrollArea className="h-[250px]">
                {isLoadingComments ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-pulse flex flex-col items-center">
                      <div className="h-8 w-32 bg-neutral-200 rounded mb-4"></div>
                      <div className="h-4 w-48 bg-neutral-200 rounded"></div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {comments.map((comment) => (
                      <div
                        key={comment.id}
                        className={`p-3 rounded-md ${comment.resolved ? 'bg-neutral-50' : 'bg-accent-10/5 border border-accent-10/10'}`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-2">
                            <div className="relative h-6 w-6 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center text-xs font-medium">
                              {comment.userAvatarUrl ? (
                                <Image
                                  src={comment.userAvatarUrl}
                                  alt={comment.userName}
                                  fill
                                  className="object-cover"
                                />
                              ) : (
                                <span>{comment.userName.charAt(0)}</span>
                              )}
                            </div>
                            <div>
                              <div className="font-medium text-sm">
                                {comment.userName}
                              </div>
                              <div className="text-xs text-neutral-500">
                                {comment.timestamp}
                              </div>
                            </div>
                          </div>

                          <div className="flex">
                            {!comment.resolved && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-7 px-2"
                                onClick={() => handleResolveComment(comment.id)}
                                title="Resolve comment"
                              >
                                <CheckCircle2 className="h-4 w-4 text-green-500" />
                              </Button>
                            )}
                            {comment.userId === currentUserId && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-7 px-2 text-red-500 hover:text-red-600"
                                onClick={() => handleDeleteComment(comment.id)}
                                title="Delete comment"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>

                        <div
                          className={`text-sm ${comment.resolved ? 'text-neutral-500' : ''}`}
                        >
                          {comment.content}
                        </div>

                        {comment.resolved && (
                          <div className="mt-2 text-xs text-green-600 flex items-center">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Resolved
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Add Comment</h3>
              <div className="flex items-start gap-2">
                <textarea
                  placeholder="Type your comment here..."
                  className="flex-1 p-2 border rounded-md text-sm min-h-[80px]"
                  id="new-comment"
                ></textarea>
                <Button
                  onClick={() => {
                    const content = (
                      document.getElementById(
                        'new-comment'
                      ) as HTMLTextAreaElement
                    ).value;
                    if (content) {
                      handleAddComment(content);
                      (
                        document.getElementById(
                          'new-comment'
                        ) as HTMLTextAreaElement
                      ).value = '';
                    }
                  }}
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Comment
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4 pt-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Recent Activity</h3>
              <ActivityList
                activities={activities}
                isLoading={isLoadingActivities}
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
