'use client';

import { marked } from 'marked';
import {
  LegalTemplate,
  LegalTemplateSchema,
} from '../constants/schemas/template';

export interface TemplateContext {
  legalRequirements: string[];
  sections: {
    title: string;
    items: string[];
  }[];
  requiredClauses: string[];
  formattingGuidelines: string[];
  officialRequirements: string[];
  rawContent: string;
}

export interface TemplateVersion {
  version: string;
  path: string;
  timestamp: Date;
}

export async function loadTemplateContext(
  jurisdiction: string,
  formType: string,
  category: string,
  version?: string
): Promise<TemplateContext> {
  try {
    console.log('Template loader params:', {
      jurisdiction,
      formType,
      category,
      version,
    });

    // Normalize the path segments
    const normalizedJurisdiction = jurisdiction.toLowerCase();
    const normalizedCategory = category.toLowerCase();
    const normalizedFormType = formType.toLowerCase();

    const templatePath = getTemplatePath(
      normalizedJurisdiction,
      normalizedCategory,
      normalizedFormType,
      version
    );

    console.log('Attempting to load template from:', templatePath);

    const response = await fetch(templatePath);
    if (!response.ok) {
      throw new Error(`Failed to load template: ${response.statusText}`);
    }

    const content = await response.text();
    console.log('Template file read successfully');

    const parsedContent = parseTemplateContent(content);
    console.log('Template parsed successfully');

    return parsedContent;
  } catch (error) {
    console.error(`Error loading template:`, error);
    throw new Error(
      `Template not found for ${jurisdiction}/${category}/${formType}${
        version ? ` version ${version}` : ''
      }`
    );
  }
}

function getTemplatePath(
  jurisdiction: string,
  category: string,
  formType: string,
  version?: string
): string {
  const basePath = `/api/templates/${jurisdiction}/${category}`;

  if (!version) {
    // Return the latest version
    return `${basePath}/${formType}`;
  }

  // Check for specific version
  return `${basePath}/versions/${formType}-${version}`;
}

export async function getTemplateVersions(
  jurisdiction: string,
  category: string,
  formType: string
): Promise<TemplateVersion[]> {
  try {
    const response = await fetch(
      `/api/templates/${jurisdiction}/${category}/${formType}/versions`
    );
    if (!response.ok) {
      throw new Error(
        `Failed to get template versions: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting template versions:', error);
    return [];
  }
}

export async function saveTemplateVersion(
  jurisdiction: string,
  category: string,
  formType: string,
  template: LegalTemplate,
  version: string
): Promise<void> {
  try {
    // Validate template
    LegalTemplateSchema.parse(template);

    const response = await fetch(
      `/api/templates/${jurisdiction}/${category}/${formType}/versions/${version}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(template),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to save template version: ${response.statusText}`
      );
    }
  } catch (error: unknown) {
    console.error('Error saving template version:', error);
    throw new Error(
      `Failed to save template version: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

function parseTemplateContent(content: string): TemplateContext {
  // Try to parse as JSON first
  try {
    const jsonTemplate = JSON.parse(content) as LegalTemplate;

    // Map from LegalTemplate to TemplateContext format
    return {
      legalRequirements: jsonTemplate.legalRequirements || [],
      sections: jsonTemplate.sections
        ? jsonTemplate.sections.map((section) => ({
            title: section.title,
            items: Array.isArray(section.content)
              ? section.content
              : [section.content],
          }))
        : [],
      requiredClauses: [], // Extract from legalRequirements or set empty
      formattingGuidelines: jsonTemplate.formattingGuidelines || [],
      officialRequirements: [], // Not in the schema, set empty
      rawContent: content,
    };
  } catch (e) {
    // If not JSON, try to parse as Markdown
    return parseMarkdownTemplate(content);
  }
}

function parseMarkdownTemplate(content: string): TemplateContext {
  const tokens = marked.lexer(content);
  const context: TemplateContext = {
    legalRequirements: [],
    sections: [],
    requiredClauses: [],
    formattingGuidelines: [],
    officialRequirements: [],
    rawContent: content,
  };

  let currentSection: {
    title: string;
    items: string[];
  } | null = null;

  for (const token of tokens) {
    if (token.type === 'heading') {
      if (token.depth === 2) {
        // Start a new section
        if (currentSection) {
          context.sections.push(currentSection);
        }
        currentSection = {
          title: token.text,
          items: [],
        };
      }
    } else if (token.type === 'list' && currentSection) {
      const items = token.items.map((item: any) => item.text);

      switch (currentSection.title) {
        case 'Legal Requirements':
          context.legalRequirements = items;
          break;
        case 'Required Clauses':
          context.requiredClauses = items;
          break;
        case 'Formatting Guidelines':
          context.formattingGuidelines = items;
          break;
        case 'Official Requirements':
          context.officialRequirements = items;
          break;
        default:
          currentSection.items = items;
          break;
      }
    }
  }

  if (currentSection) {
    context.sections.push(currentSection);
  }

  return context;
}

export async function getAllTemplates(): Promise<
  Array<{
    jurisdiction: string;
    category: string;
    type: string;
    path: string;
  }>
> {
  try {
    const response = await fetch('/api/templates');
    if (!response.ok) {
      throw new Error(`Failed to get all templates: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}
