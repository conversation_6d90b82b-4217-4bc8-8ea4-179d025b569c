'use client';

import { useEffect, useState } from 'react';
import { userStore } from '@/lib/store/user';
import Notifications from './notifications';
import { Skeleton } from '@/components/ui/skeleton';

export default function NotificationsWrapper() {
  const { user } = userStore();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    // Check if user is loaded
    if (user) {
      setIsLoading(false);
    } else {
      // Set a timeout to prevent infinite loading
      const timeout = setTimeout(() => {
        setIsLoading(false);
      }, 3000);
      
      return () => clearTimeout(timeout);
    }
  }, [user]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-10 w-10">
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
    );
  }

  if (hasError || !user) {
    // Return a simple bell icon without functionality
    return (
      <div className="flex items-center justify-center h-10 w-10 text-muted-foreground">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
          <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
        </svg>
      </div>
    );
  }

  // If user is loaded and no error, render the actual notifications component
  return <Notifications />;
}
