import { AVATARS, RND_USER_AVATARS } from '@/lib/constants/avatars';

/**
 * Get a random avatar from the available avatars
 */
export function getRandomAvatar() {
  const randomIndex = Math.floor(Math.random() * RND_USER_AVATARS.length);
  return RND_USER_AVATARS[randomIndex];
}

/**
 * Get an avatar by name (uses a deterministic mapping based on the name)
 * @param name The name to get an avatar for
 */
export function getAvatarByName(name: string): string {
  // Use a simple hash function to map names to avatars
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  
  // Get all avatars as a flat array
  const allAvatars = [
    ...Object.values(AVATARS.male),
    ...Object.values(AVATARS.female)
  ];
  
  // Use the hash to select an avatar
  const index = hash % allAvatars.length;
  return allAvatars[index];
}

/**
 * Map specific names to specific avatars for consistency
 * @param name The name to get an avatar for
 */
export function getMappedAvatar(name: string): string {
  const nameMap: Record<string, string> = {
    '<PERSON>': AVATARS.female.FM1,
    '<PERSON> <PERSON>': AVATARS.male.M2,
    'Elena Rodriguez': AVATARS.female.FM3,
    'James Wilson': AVATARS.male.M4,
    'You': AVATARS.male.M1,
  };
  
  return nameMap[name] || getAvatarByName(name);
}
