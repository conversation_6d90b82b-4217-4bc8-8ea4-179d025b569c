'use client';

import * as Textarea from '@/components/ui/textarea';
import { useEffect, useState } from 'react';

interface TextareaWithCounterProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  maxLength?: number;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

/**
 * A textarea component with a character counter.
 * This is a simple wrapper around the Textarea.Root component.
 */
export function TextareaWithCounter({
  maxLength = 500,
  value = '',
  onChange,
  ...props
}: TextareaWithCounterProps) {
  const [content, setContent] = useState(value);

  useEffect(() => {
    setContent(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);

    if (onChange) {
      onChange(e);
    }
  };

  return (
    <Textarea.Root value={content} onChange={handleChange} {...props}>
      <Textarea.CharCounter current={content.length} max={maxLength} />
    </Textarea.Root>
  );
}
