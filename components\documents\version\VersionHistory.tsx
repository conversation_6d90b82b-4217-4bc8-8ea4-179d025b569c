'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useDocuments, useUsers } from '@/lib/hooks';
import { format, formatDistanceToNow } from 'date-fns';
import { Check, Clock, Eye, History, RotateCcw, User, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import {
  DocumentVersion as DbDocumentVersion,
  Json,
} from '@/lib/types/database-modules';

// Extend the database DocumentVersion type with UI-specific fields
interface DocumentVersion extends DbDocumentVersion {
  created_by_name: string;
}

interface VersionHistoryProps {
  documentId: string;
  currentVersion: number;
  onRestoreVersion?: (version: DocumentVersion) => void;
}

export function VersionHistory({
  documentId,
  currentVersion,
  onRestoreVersion,
}: VersionHistoryProps) {
  const { getDocumentVersions, restoreDocumentVersion } = useDocuments();

  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVersion, setSelectedVersion] =
    useState<DocumentVersion | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  const { getByUsername } = useUsers();

  useEffect(() => {
    const fetchVersions = async () => {
      setLoading(true);

      try {
        // Show loading toast
        toast.loading('Loading version history...');

        // Fetch versions directly
        const versionsData = await getDocumentVersions(documentId);

        // Dismiss loading toast and show success
        toast.dismiss();
        toast.success('Version history loaded');

        if (!Array.isArray(versionsData)) {
          console.error('Expected array of versions but got:', versionsData);
          setVersions([]);
          return;
        }

        // Fetch user names for each version creator
        const versionsWithNames = await Promise.all(
          versionsData.map(async (version: DbDocumentVersion) => {
            try {
              // Try to get user profile for the creator
              const userProfile = await getByUsername(version.created_by);
              return {
                ...version,
                created_by_name: userProfile?.full_name || 'Unknown User',
              };
            } catch (error) {
              console.error('Error fetching user profile:', error);
              return {
                ...version,
                created_by_name: 'Unknown User',
              };
            }
          })
        );

        setVersions(versionsWithNames);
      } catch (error) {
        console.error('Error fetching document versions:', error);
        // Dismiss loading toast and show error
        toast.dismiss();
        toast.error('Failed to load version history');
      } finally {
        setLoading(false);
      }
    };

    fetchVersions();
  }, [documentId, currentVersion, getDocumentVersions, getByUsername]);

  const handlePreviewVersion = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setShowPreviewDialog(true);
  };

  const handleRestoreVersion = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setShowRestoreDialog(true);
  };

  const confirmRestore = async () => {
    if (!selectedVersion) return;

    setIsRestoring(true);

    try {
      // Show loading toast
      toast.loading('Restoring document version...');

      // Restore version directly
      const result = await restoreDocumentVersion(
        documentId,
        selectedVersion.id
      );

      // Dismiss loading toast and show success
      toast.dismiss();
      toast.success(`Document restored to version ${selectedVersion.version}`);

      if (result && onRestoreVersion) {
        onRestoreVersion(selectedVersion);
      }

      setShowRestoreDialog(false);
    } catch (error) {
      console.error('Error restoring document version:', error);
      // Dismiss loading toast and show error
      toast.dismiss();
      toast.error('Failed to restore document version');
    } finally {
      setIsRestoring(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'MMM d, yyyy h:mm a');
  };

  // Helper function to safely access content sections
  const getContentSections = (content: Json | null) => {
    if (!content) return [];

    try {
      // Try to access sections if they exist
      const contentObj = content as {
        sections?: Array<{ title?: string; content?: string }>;
      };
      return contentObj.sections || [];
    } catch (error) {
      console.error('Error parsing content sections:', error);
      return [];
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <History className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-medium">Version History</h3>
        </div>
        <Badge variant="outline" className="text-xs">
          {versions.length} versions
        </Badge>
      </div>

      {loading ? (
        <div className="space-y-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-16 rounded-md" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : versions.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <History className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <h4 className="text-lg font-medium mb-1">No version history</h4>
            <p className="text-muted-foreground">
              This document has no previous versions.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {versions.map((version) => (
            <Card
              key={version.id}
              className={
                version.version === currentVersion ? 'border-accent-300' : ''
              }
            >
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                  <div className="flex items-start gap-3">
                    <div className="bg-muted rounded-full h-9 w-9 flex items-center justify-center flex-shrink-0">
                      {version.version === currentVersion ? (
                        <Check className="h-5 w-5 text-accent-300" />
                      ) : (
                        <Clock className="h-5 w-5 text-muted-foreground" />
                      )}
                    </div>

                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          Version {version.version}
                        </span>
                        {version.version === currentVersion && (
                          <Badge className="bg-accent-300/10 text-accent-300 hover:bg-accent-300/20 border-accent-300/20">
                            Current
                          </Badge>
                        )}
                      </div>

                      <div className="text-sm text-muted-foreground mt-1">
                        {version.change_summary}
                      </div>

                      <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>{version.created_by_name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span title={formatDate(version.created_at)}>
                            {formatDistanceToNow(new Date(version.created_at), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8"
                      onClick={() => handlePreviewVersion(version)}
                    >
                      <Eye className="h-3.5 w-3.5 mr-1" />
                      Preview
                    </Button>

                    {version.version !== currentVersion && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8"
                        onClick={() => handleRestoreVersion(version)}
                      >
                        <RotateCcw className="h-3.5 w-3.5 mr-1" />
                        Restore
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Version Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Version {selectedVersion?.version} Preview
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-4 py-4">
              {selectedVersion && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span>{selectedVersion.created_by_name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span>{formatDate(selectedVersion.created_at)}</span>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    {selectedVersion &&
                    getContentSections(selectedVersion.content).length > 0 ? (
                      getContentSections(selectedVersion.content).map(
                        (
                          section: { title?: string; content?: string },
                          index: number
                        ) => (
                          <div key={index} className="mb-6">
                            {section.title && (
                              <h4 className="text-lg font-medium mb-2">
                                {section.title}
                              </h4>
                            )}
                            <div
                              className="prose max-w-none"
                              dangerouslySetInnerHTML={{
                                __html: section.content || '',
                              }}
                            />
                          </div>
                        )
                      )
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        No content available for this version
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPreviewDialog(false)}
            >
              Close
            </Button>
            {selectedVersion && selectedVersion.version !== currentVersion && (
              <Button
                onClick={() => {
                  setShowPreviewDialog(false);
                  handleRestoreVersion(selectedVersion);
                }}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Restore This Version
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Restore Confirmation Dialog */}
      <Dialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Restore Version {selectedVersion?.version}?
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <p className="mb-2">
              Are you sure you want to restore the document to version{' '}
              {selectedVersion?.version}?
            </p>
            <p className="text-muted-foreground text-sm">
              This will create a new version based on version{' '}
              {selectedVersion?.version}. Your current version will still be
              available in the version history.
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRestoreDialog(false)}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={confirmRestore} disabled={isRestoring}>
              <RotateCcw className="h-4 w-4 mr-2" />
              {isRestoring ? 'Restoring...' : 'Restore Version'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
