import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Sheet } from '@/components/ui/sheet';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AIService,
  TemplateRecommendation,
  UserContext,
} from '@/lib/services/AIService';
import { ChevronRight, Sparkles, Star } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface AITemplateRecommenderProps {
  userContext: UserContext;
  open: boolean;
  onClose: () => void;
  onSelectTemplate: (templateId: string) => void;
}

export function AITemplateRecommender({
  userContext,
  open,
  onClose,
  onSelectTemplate,
}: AITemplateRecommenderProps) {
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<
    TemplateRecommendation[]
  >([]);
  const [error, setError] = useState<string | null>(null);
  const [requirements, setRequirements] = useState<string[]>([]);
  const [requirementInput, setRequirementInput] = useState('');

  const aiService = AIService.getInstance();

  useEffect(() => {
    if (
      open &&
      requirements.length > 0 &&
      !loading &&
      recommendations.length === 0
    ) {
      loadRecommendations();
    }
  }, [open, requirements]);

  const loadRecommendations = async () => {
    if (requirements.length === 0) {
      setError('Please add at least one requirement');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await aiService.recommendTemplate(
        userContext,
        requirements
      );
      setRecommendations(result);
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError('Failed to load recommendations');
    } finally {
      setLoading(false);
    }
  };

  const addRequirement = () => {
    if (requirementInput.trim()) {
      setRequirements([...requirements, requirementInput.trim()]);
      setRequirementInput('');

      // Clear recommendations when requirements change
      setRecommendations([]);
    }
  };

  const removeRequirement = (index: number) => {
    const newRequirements = [...requirements];
    newRequirements.splice(index, 1);
    setRequirements(newRequirements);

    // Clear recommendations when requirements change
    setRecommendations([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addRequirement();
    }
  };

  const getConfidenceStars = (confidence: number) => {
    const stars = [];
    const fullStars = Math.round(confidence * 5);

    for (let i = 0; i < 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`h-4 w-4 ${
            i < fullStars ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
          }`}
        />
      );
    }

    return stars;
  };

  return (
    <Sheet open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <Sheet.Title className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              AI Template Recommendations
            </Sheet.Title>
            <Sheet.Description className="text-sm text-muted-foreground">
              Get smart template recommendations based on your needs
            </Sheet.Description>
          </div>
        </div>

        <div className="py-6 space-y-6">
          <div className="space-y-2">
            <h3 className="text-sm font-medium">Your Requirements</h3>
            <div className="flex space-x-2">
              <Input
                placeholder="Enter a requirement..."
                value={requirementInput}
                onChange={(e) => setRequirementInput(e.target.value)}
                onKeyDown={handleKeyPress}
              />
              <Button onClick={addRequirement} variant="outline">
                Add
              </Button>
            </div>

            {requirements.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {requirements.map((req, index) => (
                  <div
                    key={index}
                    className="bg-muted text-sm px-3 py-1 rounded-full flex items-center gap-1"
                  >
                    <span className="truncate max-w-[160px]">{req}</span>
                    <button
                      onClick={() => removeRequirement(index)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      &times;
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {requirements.length > 0 && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">Recommendations</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={loadRecommendations}
                  disabled={loading}
                >
                  {loading ? 'Loading...' : 'Refresh'}
                </Button>
              </div>

              {loading ? (
                <div className="space-y-3">
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-24 w-full" />
                </div>
              ) : error ? (
                <div className="text-sm text-red-500 p-4 text-center">
                  {error}
                </div>
              ) : recommendations.length > 0 ? (
                <div className="space-y-3">
                  {recommendations.map((recommendation, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 hover:border-primary transition-colors cursor-pointer"
                      onClick={() =>
                        onSelectTemplate(recommendation.templateId)
                      }
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{recommendation.name}</h4>
                        <div className="flex">
                          {getConfidenceStars(recommendation.confidence)}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {recommendation.description}
                      </p>

                      {recommendation.suitabilityReasons.length > 0 && (
                        <div className="mt-2">
                          <h5 className="text-xs text-muted-foreground mb-1">
                            Why this template:
                          </h5>
                          <ul className="text-xs list-disc list-inside space-y-1 text-muted-foreground">
                            {recommendation.suitabilityReasons.map(
                              (reason, i) => (
                                <li key={i}>{reason}</li>
                              )
                            )}
                          </ul>
                        </div>
                      )}

                      <div className="flex justify-end mt-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-primary flex items-center gap-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            onSelectTemplate(recommendation.templateId);
                          }}
                        >
                          Select <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-center text-muted-foreground py-8">
                  Click refresh to get recommendations based on your
                  requirements
                </div>
              )}
            </div>
          )}
        </div>

        <div className="pt-4 border-t flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button
            variant="default"
            disabled={requirements.length === 0 || loading}
            onClick={loadRecommendations}
          >
            Get Recommendations
          </Button>
        </div>
      </div>
    </Sheet>
  );
}
