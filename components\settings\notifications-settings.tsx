'use client';

import { useState } from 'react';
import { useSettings } from '@/lib/hooks';
import { UserSettings } from '@/lib/supabase/db';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface NotificationsSettingsProps {
  settings: UserSettings | null;
}

export function NotificationsSettings({ settings }: NotificationsSettingsProps) {
  const { loading, updateSettings } = useSettings();
  
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(
    settings?.notifications_enabled !== false
  );
  
  const [emailNotifications, setEmailNotifications] = useState({
    comment_added: settings?.email_notifications?.comment_added !== false,
    task_assigned: settings?.email_notifications?.task_assigned !== false,
    contract_signed: settings?.email_notifications?.contract_signed !== false,
    document_shared: settings?.email_notifications?.document_shared !== false,
    document_updated: settings?.email_notifications?.document_updated !== false,
  });

  const handleToggleNotifications = async (enabled: boolean) => {
    setNotificationsEnabled(enabled);
    
    if (!settings) return;
    
    try {
      await updateSettings({
        notifications_enabled: enabled,
      });
      
      toast.success(
        enabled ? 'Notifications enabled' : 'Notifications disabled'
      );
    } catch (error) {
      console.error('Error updating notifications:', error);
      toast.error('Failed to update notification settings');
      setNotificationsEnabled(!enabled); // Revert on error
    }
  };

  const handleToggleEmailNotification = async (
    key: keyof typeof emailNotifications,
    enabled: boolean
  ) => {
    setEmailNotifications((prev) => ({
      ...prev,
      [key]: enabled,
    }));
    
    if (!settings) return;
    
    try {
      await updateSettings({
        email_notifications: {
          ...emailNotifications,
          [key]: enabled,
        },
      });
    } catch (error) {
      console.error('Error updating email notifications:', error);
      toast.error('Failed to update email notification settings');
      setEmailNotifications((prev) => ({
        ...prev,
        [key]: !enabled, // Revert on error
      }));
    }
  };

  const handleSaveAll = async () => {
    if (!settings) return;
    
    try {
      await updateSettings({
        notifications_enabled: notificationsEnabled,
        email_notifications: emailNotifications,
      });
      
      toast.success('Notification settings saved successfully');
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast.error('Failed to save notification settings');
    }
  };

  if (!settings) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-5 w-10" />
          </div>
          <div className="space-y-4 mt-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-5 w-10" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>
          Manage how and when you receive notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Enable Notifications</h3>
            <p className="text-sm text-neutral-500">
              Receive notifications about activity in your account
            </p>
          </div>
          <Switch
            checked={notificationsEnabled}
            onCheckedChange={handleToggleNotifications}
            disabled={loading}
          />
        </div>
        
        <div className="border-t pt-6">
          <h3 className="font-medium mb-4">Email Notifications</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Comments</h4>
                <p className="text-xs text-neutral-500">
                  When someone comments on your document
                </p>
              </div>
              <Switch
                checked={emailNotifications.comment_added}
                onCheckedChange={(checked) =>
                  handleToggleEmailNotification('comment_added', checked)
                }
                disabled={loading || !notificationsEnabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Task Assignments</h4>
                <p className="text-xs text-neutral-500">
                  When you are assigned a task
                </p>
              </div>
              <Switch
                checked={emailNotifications.task_assigned}
                onCheckedChange={(checked) =>
                  handleToggleEmailNotification('task_assigned', checked)
                }
                disabled={loading || !notificationsEnabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Contract Signatures</h4>
                <p className="text-xs text-neutral-500">
                  When a contract is signed
                </p>
              </div>
              <Switch
                checked={emailNotifications.contract_signed}
                onCheckedChange={(checked) =>
                  handleToggleEmailNotification('contract_signed', checked)
                }
                disabled={loading || !notificationsEnabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Document Sharing</h4>
                <p className="text-xs text-neutral-500">
                  When a document is shared with you
                </p>
              </div>
              <Switch
                checked={emailNotifications.document_shared}
                onCheckedChange={(checked) =>
                  handleToggleEmailNotification('document_shared', checked)
                }
                disabled={loading || !notificationsEnabled}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">Document Updates</h4>
                <p className="text-xs text-neutral-500">
                  When a document you collaborate on is updated
                </p>
              </div>
              <Switch
                checked={emailNotifications.document_updated}
                onCheckedChange={(checked) =>
                  handleToggleEmailNotification('document_updated', checked)
                }
                disabled={loading || !notificationsEnabled}
              />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={handleSaveAll} disabled={loading}>
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Preferences
        </Button>
      </CardFooter>
    </Card>
  );
}
