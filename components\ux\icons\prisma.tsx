import { SVGProps } from "react";

export function Prisma(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 159 194"
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M2.397 122.867a9.632 9.632 0 0 0 .077 10.192l35.622 56.193a9.633 9.633 0 0 0 10.905 4.069l102.797-30.839c5.61-1.683 8.432-7.941 5.98-13.26L91.695 5.872c-3.222-6.99-12.938-7.564-16.961-1.001L2.397 122.867ZM89.94 38.644c-1.404-3.25-6.16-2.75-6.857.722L57.64 166.044c-.537 2.671 1.964 4.952 4.575 4.173l71.025-21.202a3.61 3.61 0 0 0 2.282-4.894L89.94 38.644Z"
        clipRule="evenodd"
      />
    </svg>
  );
}
