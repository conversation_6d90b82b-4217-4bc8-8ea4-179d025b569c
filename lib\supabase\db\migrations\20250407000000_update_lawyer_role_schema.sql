-- Migration to update the lawyer role schema

-- First, ensure we have the proper user roles
INSERT INTO public.roles (name, permissions, created_at, updated_at)
VALUES 
  ('user', ARRAY['create_form', 'edit_form', 'delete_form', 'view_form']::text[], NOW(), NOW()),
  ('lawyer', ARRAY['create_form', 'edit_form', 'delete_form', 'view_form', 'review_document', 'manage_consultations']::text[], NOW(), NOW())
ON CONFLICT (name) 
DO UPDATE SET 
  permissions = EXCLUDED.permissions,
  updated_at = NOW();

-- Add review_document and manage_consultations to the permission enum if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permission' AND 
                  'review_document' = ANY(enum_range(NULL::permission)::text[])) THEN
        ALTER TYPE permission ADD VALUE 'review_document';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permission' AND 
                  'manage_consultations' = ANY(enum_range(NULL::permission)::text[])) THEN
        ALTER TYPE permission ADD VALUE 'manage_consultations';
    END IF;
END$$;

-- Update the lawyers table to ensure it has all necessary fields
ALTER TABLE public.lawyers
ADD COLUMN IF NOT EXISTS consultation_fee numeric,
ADD COLUMN IF NOT EXISTS consultation_duration_options integer[] DEFAULT '{30,60,90}'::integer[],
ADD COLUMN IF NOT EXISTS education text,
ADD COLUMN IF NOT EXISTS languages text[] DEFAULT '{English}'::text[],
ADD COLUMN IF NOT EXISTS practice_areas text[] DEFAULT '{}'::text[],
ADD COLUMN IF NOT EXISTS consultation_count integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS average_rating numeric DEFAULT 0,
ADD COLUMN IF NOT EXISTS profile_complete boolean DEFAULT false;

-- Create a function to automatically create a lawyer record when a user's role is set to 'lawyer'
CREATE OR REPLACE FUNCTION create_lawyer_profile()
RETURNS TRIGGER AS $$
BEGIN
    -- If the role is being set to 'lawyer' and there's no existing lawyer record
    IF NEW.role = 'lawyer' AND NOT EXISTS (SELECT 1 FROM public.lawyers WHERE user_id = NEW.id) THEN
        INSERT INTO public.lawyers (
            user_id, 
            full_name, 
            email, 
            specialization, 
            status
        ) VALUES (
            NEW.id, 
            COALESCE(NEW.full_name, 'Lawyer'), 
            COALESCE(NEW.email, ''), 
            '{}'::text[], 
            'pending'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to call the function when a profile is updated
DROP TRIGGER IF EXISTS create_lawyer_profile_trigger ON public.profiles;
CREATE TRIGGER create_lawyer_profile_trigger
AFTER UPDATE OF role ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION create_lawyer_profile();

-- Update RLS policies for lawyers table
ALTER TABLE public.lawyers ENABLE ROW LEVEL SECURITY;

-- Policy for users to view active lawyers
CREATE POLICY IF NOT EXISTS "Users can view active lawyers"
  ON public.lawyers
  FOR SELECT
  USING (status = 'active');

-- Policy for lawyers to view and edit their own profile
CREATE POLICY IF NOT EXISTS "Lawyers can view and edit their own profile"
  ON public.lawyers
  FOR ALL
  USING (user_id = auth.uid());

-- Policy for admins to manage all lawyers
CREATE POLICY IF NOT EXISTS "Admins can manage all lawyers"
  ON public.lawyers
  FOR ALL
  USING (EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND role = 'admin'
  ));

-- Update lawyer_consultations table
ALTER TABLE public.lawyer_consultations
ADD COLUMN IF NOT EXISTS meeting_link text,
ADD COLUMN IF NOT EXISTS payment_status text DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS payment_amount numeric DEFAULT 0,
ADD COLUMN IF NOT EXISTS consultation_type text DEFAULT 'video',
ADD COLUMN IF NOT EXISTS feedback_provided boolean DEFAULT false;

-- Update RLS policies for lawyer_consultations
ALTER TABLE public.lawyer_consultations ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own consultations
CREATE POLICY IF NOT EXISTS "Users can view their own consultations"
  ON public.lawyer_consultations
  FOR SELECT
  USING (user_id = auth.uid());

-- Policy for lawyers to view consultations where they are the lawyer
CREATE POLICY IF NOT EXISTS "Lawyers can view their consultations"
  ON public.lawyer_consultations
  FOR SELECT
  USING (
    lawyer_id IN (
      SELECT id FROM public.lawyers
      WHERE user_id = auth.uid()
    )
  );

-- Policy for lawyers to update consultations where they are the lawyer
CREATE POLICY IF NOT EXISTS "Lawyers can update their consultations"
  ON public.lawyer_consultations
  FOR UPDATE
  USING (
    lawyer_id IN (
      SELECT id FROM public.lawyers
      WHERE user_id = auth.uid()
    )
  );

-- Create a view to show lawyer dashboard stats
CREATE OR REPLACE VIEW lawyer_dashboard_stats AS
SELECT 
  l.id AS lawyer_id,
  l.user_id,
  l.full_name,
  COUNT(DISTINCT lc.id) AS total_consultations,
  COUNT(DISTINCT CASE WHEN lc.status = 'scheduled' THEN lc.id END) AS upcoming_consultations,
  COUNT(DISTINCT CASE WHEN lc.status = 'completed' THEN lc.id END) AS completed_consultations,
  COALESCE(AVG(lr.rating), 0) AS average_rating,
  COUNT(DISTINCT lr.id) AS total_reviews,
  SUM(CASE WHEN lc.payment_status = 'paid' THEN lc.payment_amount ELSE 0 END) AS total_earnings
FROM 
  public.lawyers l
LEFT JOIN 
  public.lawyer_consultations lc ON l.id = lc.lawyer_id
LEFT JOIN 
  public.lawyer_reviews lr ON l.id = lr.lawyer_id
GROUP BY 
  l.id, l.user_id, l.full_name;

-- Grant access to the view
GRANT SELECT ON lawyer_dashboard_stats TO authenticated;

-- Create a function to get upcoming consultations for a lawyer
CREATE OR REPLACE FUNCTION get_lawyer_upcoming_consultations(lawyer_uuid uuid)
RETURNS TABLE (
  id uuid,
  client_name text,
  client_email text,
  consultation_date timestamp with time zone,
  duration_minutes integer,
  status text,
  document_id uuid,
  document_title text,
  notes text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    lc.id,
    p.full_name AS client_name,
    p.email AS client_email,
    lc.consultation_date,
    lc.duration_minutes,
    lc.status,
    lc.document_id,
    d.title AS document_title,
    lc.notes
  FROM 
    public.lawyer_consultations lc
  JOIN 
    public.profiles p ON lc.user_id = p.id
  LEFT JOIN 
    public.documents d ON lc.document_id = d.id
  WHERE 
    lc.lawyer_id = lawyer_uuid
    AND lc.status = 'scheduled'
    AND lc.consultation_date > NOW()
  ORDER BY 
    lc.consultation_date ASC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get a user's lawyer profile
CREATE OR REPLACE FUNCTION get_user_lawyer_profile(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  full_name text,
  email text,
  specialization text[],
  bio text,
  years_experience integer,
  hourly_rate numeric,
  consultation_fee numeric,
  availability jsonb,
  avatar_url text,
  is_verified boolean,
  status text,
  profile_complete boolean,
  average_rating numeric,
  consultation_count integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    l.id,
    l.full_name,
    l.email,
    l.specialization,
    l.bio,
    l.years_experience,
    l.hourly_rate,
    l.consultation_fee,
    l.availability,
    l.avatar_url,
    l.is_verified,
    l.status,
    l.profile_complete,
    l.average_rating,
    l.consultation_count
  FROM 
    public.lawyers l
  WHERE 
    l.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;
