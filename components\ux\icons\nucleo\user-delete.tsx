import { SVGProps } from "react";

export function UserDelete(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 13 16"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g fill="currentColor">
        <path
          d="M6.00011 6.44443C7.35014 6.44443 8.44455 5.35002 8.44455 3.99999C8.44455 2.64996 7.35014 1.55554 6.00011 1.55554C4.65008 1.55554 3.55566 2.64996 3.55566 3.99999C3.55566 5.35002 4.65008 6.44443 6.00011 6.44443Z"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.66675 10.6666L12.2223 14.2222"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.3894 8.85507C6.94407 8.73951 6.48185 8.66663 6.00007 8.66663C3.73251 8.66663 1.78762 10.0266 0.925402 11.9733C0.600958 12.7066 1.00274 13.5502 1.76718 13.7911C2.85607 14.1342 4.29696 14.4435 6.00007 14.4435C6.16096 14.4435 6.30762 14.4284 6.46407 14.4231"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.2223 10.6666L8.66675 14.2222"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}
