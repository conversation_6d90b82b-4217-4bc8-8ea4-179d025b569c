'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { useCollaboration } from '@/lib/hooks';
import { supabaseClient } from '@/lib/supabase/client';
import {
  ChevronDown,
  Download,
  FileText,
  Filter,
  MoreHorizontal,
  Plus,
  Share2,
  Trash2,
  Upload,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface ProjectDocument {
  id: string;
  project_id: string;
  document_id: string;
  added_by: string;
  created_at: string;
  document: {
    id: string;
    title: string;
    document_type: string;
    status: string;
    owner_id: string;
    owner_name?: string;
    created_at: string;
    updated_at: string;
  };
  project: {
    id: string;
    name: string;
  };
}

// We don't need this type since we're using type assertions in the code

export function TeamDocumentsClient({ username }: { username: string }) {
  const { projects, loading: projectsLoading } = useCollaboration();
  const [teamDocuments, setTeamDocuments] = useState<ProjectDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [documentTypeFilter, setDocumentTypeFilter] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (projects && projects.length > 0 && !selectedProject) {
      setSelectedProject('all');
    }
  }, [projects, selectedProject]);

  useEffect(() => {
    fetchTeamDocuments();
  }, [selectedProject]);

  const fetchTeamDocuments = async () => {
    setIsLoading(true);
    try {
      const { data: userData } = await supabaseClient.auth.getUser();
      if (!userData.user) {
        toast.error('You must be logged in to view team documents');
        setIsLoading(false);
        return;
      }

      // Get projects the user is a member of
      const { data: userProjects, error: projectsError } = await supabaseClient
        .from('project_members')
        .select('project_id')
        .eq('user_id', userData.user.id);

      if (projectsError) throw projectsError;

      const projectIds = userProjects.map(
        (p: { project_id: string }) => p.project_id
      );

      if (projectIds.length === 0) {
        setTeamDocuments([]);
        setIsLoading(false);
        return;
      }

      // Query for project documents
      let query = supabaseClient.from('project_documents').select(`
          id,
          project_id,
          document_id,
          added_by,
          created_at,
          document:documents(id, title, document_type, status, owner_id, created_at, updated_at),
          project:projects(id, name)
        `);

      // Filter by selected project if not "all"
      if (selectedProject && selectedProject !== 'all') {
        query = query.eq('project_id', selectedProject);
      } else {
        query = query.in('project_id', projectIds);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Get owner names for documents
      // Filter out items with null document and safely access owner_id
      const ownerIds = [
        ...new Set(
          data
            .filter(
              (item) => item.document != null && item.document.owner_id != null
            )
            .map((item) => {
              // This is safe because we've filtered for non-null documents and owner_ids
              return item.document!.owner_id as string;
            })
        ),
      ];

      const ownerProfiles: Record<string, string> = {};

      for (const ownerId of ownerIds) {
        const { data: profileData } = await supabaseClient
          .from('profiles')
          .select('id, full_name')
          .eq('id', ownerId)
          .single();

        if (profileData && typeof profileData.full_name === 'string') {
          ownerProfiles[ownerId] = profileData.full_name;
        }
      }

      // Add owner names to documents and ensure all required fields are present
      const documentsWithOwners = data
        .filter((item) => item.document != null && item.project != null)
        .map((item) => {
          // Ensure document has all required fields with proper type assertions
          const doc = item.document!; // Non-null assertion is safe because of the filter
          const proj = item.project!; // Non-null assertion is safe because of the filter

          return {
            id: item.id,
            project_id: item.project_id,
            document_id: item.document_id,
            added_by: item.added_by || '',
            created_at: item.created_at,
            document: {
              id: doc.id || '',
              title: doc.title || '',
              document_type: doc.document_type || '',
              status: doc.status || '',
              owner_id: doc.owner_id || '',
              owner_name: doc.owner_id
                ? ownerProfiles[doc.owner_id] || 'Unknown User'
                : 'Unknown User',
              created_at: doc.created_at || '',
              updated_at: doc.updated_at || '',
            },
            project: {
              id: proj.id || '',
              name: proj.name || '',
            },
          } as ProjectDocument;
        });

      setTeamDocuments(documentsWithOwners);
    } catch (error) {
      console.error('Error fetching team documents:', error);
      toast.error('Failed to load team documents');
    } finally {
      setIsLoading(false);
    }
  };

  // Function implementation removed to avoid unused function warning
  // Will be implemented when the UI needs this functionality

  const handleRemoveDocumentFromProject = async (projectDocumentId: string) => {
    try {
      const { error } = await supabaseClient
        .from('project_documents')
        .delete()
        .eq('id', projectDocumentId);

      if (error) throw error;

      toast.success('Document removed from project');
      setTeamDocuments(
        teamDocuments.filter((doc) => doc.id !== projectDocumentId)
      );
    } catch (error) {
      console.error('Error removing document from project:', error);
      toast.error('Failed to remove document from project');
    }
  };

  // Filter documents based on search query and filters
  const filteredDocuments = teamDocuments.filter((item) => {
    const doc = item.document;
    const matchesSearch =
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.document_type.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType =
      !documentTypeFilter || doc.document_type === documentTypeFilter;

    return matchesSearch && matchesType;
  });

  if (projectsLoading || isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <FileText className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Team Documents</h1>
        </div>
        <Skeleton className="h-[500px] w-full rounded-lg" />
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <FileText className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Team Documents</h1>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Users className="size-10 text-neutral-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Teams Found</h2>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              You need to create or join a team before you can access team
              documents.
            </p>
            <Button
              onClick={() =>
                (window.location.href = `/${username}/collaboration/projects/new`)
              }
            >
              Create a Team
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <main className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <FileText className="size-5 text-neutral-500" />
        <h1 className="text-2xl font-bold">Team Documents</h1>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full md:w-auto">
                {selectedProject === 'all'
                  ? 'All Projects'
                  : projects.find((p) => p.id === selectedProject)?.name ||
                    'Select Project'}
                <ChevronDown className="ml-2 size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Projects</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setSelectedProject('all')}>
                All Projects
              </DropdownMenuItem>
              {projects.map((project) => (
                <DropdownMenuItem
                  key={project.id}
                  onClick={() => setSelectedProject(project.id)}
                >
                  {project.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full md:w-auto md:min-w-[250px]"
          />
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Filter className="size-4" />
                <span>Filter</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Document Type</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setDocumentTypeFilter(null)}>
                All Types
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setDocumentTypeFilter('contract')}
              >
                Contracts
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setDocumentTypeFilter('agreement')}
              >
                Agreements
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDocumentTypeFilter('form')}>
                Forms
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDocumentTypeFilter('letter')}>
                Letters
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button
            className="gap-1"
            onClick={() =>
              (window.location.href = `/${username}/documents/upload`)
            }
          >
            <Upload className="size-4" />
            <span>Upload</span>
          </Button>
        </div>
      </div>

      {filteredDocuments.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <FileText className="size-10 text-neutral-300 mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Team Documents</h2>
            <p className="text-neutral-500 text-center max-w-md mb-6">
              {searchQuery
                ? 'No documents match your search criteria.'
                : selectedProject !== 'all'
                  ? "This project doesn't have any documents yet."
                  : "Your teams don't have any documents yet."}
            </p>
            <Button
              className="gap-1"
              onClick={() =>
                (window.location.href = `/${username}/documents/new`)
              }
            >
              <Plus className="size-4" />
              <span>Add Document</span>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredDocuments.map((item) => (
            <Card key={item.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">
                      {item.document.title}
                    </CardTitle>
                    <CardDescription>
                      {item.document.document_type.charAt(0).toUpperCase() +
                        item.document.document_type.slice(1)}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        className="gap-2"
                        onClick={() =>
                          (window.location.href = `/${username}/documents/${item.document.id}`)
                        }
                      >
                        <FileText className="size-4" />
                        <span>Open Document</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="gap-2">
                        <Share2 className="size-4" />
                        <span>Share</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="gap-2">
                        <Download className="size-4" />
                        <span>Download</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="gap-2 text-red-600"
                        onClick={() => handleRemoveDocumentFromProject(item.id)}
                      >
                        <Trash2 className="size-4" />
                        <span>Remove from Project</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-neutral-500 flex items-center gap-1 mb-2">
                  <Users className="size-3" />
                  <span>Project: {item.project.name}</span>
                </div>
                <div className="text-xs text-neutral-400 mb-3">
                  Added on {new Date(item.created_at).toLocaleDateString()}
                </div>
                <div className="text-xs text-neutral-400">
                  Last updated on{' '}
                  {new Date(item.document.updated_at).toLocaleDateString()}
                </div>
              </CardContent>
              <CardFooter className="pt-0 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  onClick={() =>
                    (window.location.href = `/${username}/documents/${item.document.id}`)
                  }
                >
                  <FileText className="size-4" />
                  <span>Open</span>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </main>
  );
}
