import { Document } from '@/lib/types/database-modules';
import {
  Document as PDFDocument,
  Page,
  StyleSheet,
  Text,
  View,
  pdf,
} from '@react-pdf/renderer';
import React from 'react';

export type ExportFormat = 'pdf' | 'docx' | 'html' | 'text' | 'markdown';

export interface ExportOptions {
  format: ExportFormat;
  includeWatermark?: boolean;
  watermarkText?: string;
  watermarkOpacity?: number;
  includeMetadata?: boolean;
  includeSignaturePage?: boolean;
  signatureData?: string | null; // Base64 signature image data
  signatureInfo?: {
    name: string;
    date: string;
    title?: string;
  };
  styling?: {
    fontFamily?: string;
    fontClassName?: string;
    fontSize?: number;
    lineHeight?: number;
    margins?: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
    // Additional styling to match preview
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    headingStyle?: {
      fontWeight?: string;
      fontSize?: number;
      marginBottom?: number;
      textAlign?: string;
    };
    sectionStyle?: {
      marginBottom?: number;
    };
  };
  // Legal document formatting options
  legalFormatting?: {
    addSectionNumbers?: boolean; // Add section numbers (1, 2, 3, etc.)
    addTableOfContents?: boolean; // Add a table of contents
    addLineNumbers?: boolean; // Add line numbers for legal reference
    addBookmarks?: boolean; // Add PDF bookmarks/outline
    documentType?: 'contract' | 'agreement' | 'lease' | 'general'; // Type of legal document
    disclaimer?: string; // Legal disclaimer text to include
  };
}

// Define a Section interface to use in the react-pdf implementation
interface Section {
  id?: string;
  title?: string;
  content: string;
  variables?: Array<{ name: string; value?: string }>;
}

/**
 * Service for exporting documents to various formats
 */
export class DocumentExportService {
  /**
   * Export a document to the specified format
   */
  async exportDocument(
    document: Document,
    options: ExportOptions
  ): Promise<Blob | string> {
    switch (options.format) {
      case 'pdf':
        // Always use react-pdf for PDF generation
        return this.exportToPdf(document, options);
      case 'docx':
        return this.exportToDocx(document, options);
      case 'html':
        return this.exportToHtml(document, options);
      case 'text':
        return this.exportToText(document, options);
      case 'markdown':
        return this.exportToMarkdown(document, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Sanitize text to prevent encoding issues in PDF generation
   * This removes or replaces problematic characters
   */
  private sanitizeText(text: string): string {
    if (!text) return '';

    // Replace problematic characters with safe alternatives
    return (
      text
        // Replace fancy quotes with simple quotes
        .replace(/[\u2018\u2019]/g, "'")
        .replace(/[\u201C\u201D]/g, '"')
        // Replace em-dash and en-dash with regular dash
        .replace(/[\u2013\u2014]/g, '-')
        // Replace other special characters
        .replace(/[\u2026]/g, '...')
        // Remove any other non-ASCII characters that might cause issues
        .replace(/[^\x00-\x7F]/g, ' ')
    );
  }

  /**
   * Export a document to PDF using react-pdf
   */
  private async exportToPdf(
    document: Document,
    options: ExportOptions
  ): Promise<Blob> {
    try {
      // Use the font specified in options, or fall back to Times New Roman
      // For PDF we'll use standard PDF fonts to avoid embedding issues
      // Map common fonts to PDF standard fonts
      let fontFamily = 'Times-Roman'; // Default PDF font (equivalent to Times New Roman)

      // If the user has selected a specific font, try to map it to a PDF standard font
      if (options.styling?.fontFamily) {
        if (
          options.styling.fontFamily.includes('Times New Roman') ||
          options.styling.fontFamily.includes('Georgia') ||
          options.styling.fontFamily.includes('Merriweather') ||
          options.styling.fontFamily.includes('Playfair') ||
          options.styling.fontFamily.includes('Baskerville') ||
          options.styling.fontFamily.includes('PT Serif') ||
          options.styling.fontFamily.includes('Crimson')
        ) {
          fontFamily = 'Times-Roman'; // Serif font
        } else if (
          options.styling.fontFamily.includes('Roboto') ||
          options.styling.fontFamily.includes('Arial') ||
          options.styling.fontFamily.includes('Helvetica')
        ) {
          fontFamily = 'Helvetica'; // Sans-serif font
        }
      }

      // Create styles
      const styles = StyleSheet.create({
        page: {
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          padding: 40,
          fontFamily: fontFamily,
          fontSize: options.styling?.fontSize || 12,
        },
        header: {
          marginBottom: 20,
          textAlign: 'center',
        },
        title: {
          fontSize: 24,
          marginBottom: 10,
          fontWeight: 'bold',
          textAlign: 'center',
        },
        description: {
          fontSize: 12,
          color: '#666666',
          marginBottom: 20,
          textAlign: 'center',
        },
        section: {
          marginBottom: 20,
        },
        sectionTitle: {
          fontSize: 16,
          fontWeight: 'bold',
          marginBottom: 10,
        },
        content: {
          fontSize: 12,
          lineHeight: 1.5,
        },
        footer: {
          position: 'absolute',
          bottom: 30,
          left: 30,
          right: 30,
          marginTop: 40, // Increased margin above the footer to create more space
          paddingTop: 10,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        },
        footerText: {
          fontSize: 8,
          color: '#666666',
        },
        footerLeft: {
          fontSize: 8,
          color: '#666666',
          textAlign: 'left',
        },
        footerCenter: {
          fontSize: 8,
          color: '#666666',
          textAlign: 'center',
        },
        footerRight: {
          fontSize: 8,
          color: '#666666',
          textAlign: 'right',
        },
        watermark: {
          position: 'absolute',
          opacity: (options.watermarkOpacity || 30) / 100,
          transform: 'rotate(-45deg)',
          fontSize: 60,
          color: '#e6e6e6',
          top: '40%',
          left: '20%',
          zIndex: -1,
        },
      });

      // Process content sections
      const sections: Section[] =
        document.content &&
        typeof document.content === 'object' &&
        document.content.sections
          ? document.content.sections
          : [];

      // Strip HTML tags from content
      const stripHtml = (html: string): string => {
        return html.replace(/<[^>]*>/g, '');
      };

      // Create document with React.createElement to avoid JSX issues
      const documentElement = React.createElement(
        PDFDocument,
        {
          title: this.sanitizeText(document.title),
          author: 'Notamess',
          subject: this.sanitizeText(document.description || ''),
          creator: 'Notamess Forms',
        },
        [
          // Main page
          React.createElement(
            Page,
            {
              key: 'main-page',
              size: 'A4',
              style: styles.page,
            },
            [
              // Watermark if needed
              options.includeWatermark && options.watermarkText
                ? React.createElement(
                    Text,
                    {
                      key: 'watermark',
                      style: styles.watermark,
                    },
                    this.sanitizeText(options.watermarkText || '')
                  )
                : null,

              // Header section
              React.createElement(
                View,
                {
                  key: 'header',
                  style: styles.header,
                },
                [
                  React.createElement(
                    Text,
                    {
                      key: 'title',
                      style: styles.title,
                    },
                    this.sanitizeText(document.title)
                  ),
                  document.description
                    ? React.createElement(
                        Text,
                        {
                          key: 'description',
                          style: styles.description,
                        },
                        this.sanitizeText(document.description)
                      )
                    : null,
                ]
              ),

              // Metadata if requested
              options.includeMetadata
                ? React.createElement(
                    View,
                    {
                      key: 'metadata',
                      style: { marginBottom: 20 },
                    },
                    [
                      React.createElement(
                        Text,
                        { key: 'type' },
                        `Type: ${document.document_type}`
                      ),
                      React.createElement(
                        Text,
                        { key: 'status' },
                        `Status: ${document.status}`
                      ),
                      React.createElement(
                        Text,
                        { key: 'created' },
                        `Created: ${new Date(document.created_at).toLocaleDateString()}`
                      ),
                    ]
                  )
                : null,

              // Document content sections
              ...sections.map((section: Section, i: number) =>
                React.createElement(
                  View,
                  {
                    key: `section-${i}`,
                    style: styles.section,
                  },
                  [
                    section.title
                      ? React.createElement(
                          Text,
                          {
                            key: `title-${i}`,
                            style: styles.sectionTitle,
                          },
                          options.legalFormatting?.addSectionNumbers
                            ? `${i + 1}. ${this.sanitizeText(section.title)}`
                            : this.sanitizeText(section.title)
                        )
                      : null,
                    React.createElement(
                      Text,
                      {
                        key: `content-${i}`,
                        style: styles.content,
                      },
                      this.sanitizeText(stripHtml(section.content || ''))
                    ),
                  ]
                )
              ),

              // Footer with 3 columns: left (company+date), center (doc ID), right (page number)
              React.createElement(
                View,
                {
                  key: 'footer',
                  style: styles.footer,
                  fixed: true,
                },
                [
                  // Left column: Generated by Forms Notamess + date
                  React.createElement(
                    Text,
                    {
                      key: 'generated',
                      style: styles.footerLeft,
                    },
                    `Generated By Forms Notamess | ${new Date().toLocaleDateString()}`
                  ),
                  // Center column: Document ID
                  React.createElement(
                    Text,
                    {
                      key: 'id',
                      style: styles.footerCenter,
                    },
                    `${document.id}`
                  ),
                  // Right column: Page number
                  React.createElement(Text, {
                    key: 'page-number',
                    style: styles.footerRight,
                    render: ({ pageNumber, totalPages }) =>
                      `Page ${pageNumber} of ${totalPages}`,
                  }),
                ]
              ),
            ].filter(Boolean) // Filter out null elements
          ),

          // Signature page if requested
          options.includeSignaturePage
            ? React.createElement(
                Page,
                {
                  key: 'signature-page',
                  size: 'A4',
                  style: styles.page,
                },
                [
                  React.createElement(
                    Text,
                    {
                      key: 'sig-title',
                      style: Object.assign({}, styles.sectionTitle, {
                        textAlign: 'center' as 'center',
                      }),
                    },
                    'Signature Page'
                  ),
                  React.createElement(
                    View,
                    {
                      key: 'signature-block',
                      style: { marginTop: 100 },
                    },
                    [
                      React.createElement(View, {
                        key: 'sig-line',
                        style: {
                          borderTopWidth: 1,
                          borderTopColor: '#000000',
                          width: '60%',
                          marginLeft: 'auto',
                          marginRight: 'auto',
                          marginBottom: 5,
                        },
                      }),
                      React.createElement(
                        Text,
                        {
                          key: 'sig-text',
                          style: { textAlign: 'center' as 'center' },
                        },
                        'Signature'
                      ),
                    ]
                  ),
                  React.createElement(
                    View,
                    {
                      key: 'date-block',
                      style: { marginTop: 50 },
                    },
                    [
                      React.createElement(View, {
                        key: 'date-line',
                        style: {
                          borderTopWidth: 1,
                          borderTopColor: '#000000',
                          width: '60%',
                          marginLeft: 'auto',
                          marginRight: 'auto',
                          marginBottom: 5,
                        },
                      }),
                      React.createElement(
                        Text,
                        {
                          key: 'date-text',
                          style: { textAlign: 'center' as 'center' },
                        },
                        'Date'
                      ),
                    ]
                  ),
                  React.createElement(
                    View,
                    {
                      key: 'name-block',
                      style: { marginTop: 50 },
                    },
                    [
                      React.createElement(View, {
                        key: 'name-line',
                        style: {
                          borderTopWidth: 1,
                          borderTopColor: '#000000',
                          width: '60%',
                          marginLeft: 'auto',
                          marginRight: 'auto',
                          marginBottom: 5,
                        },
                      }),
                      React.createElement(
                        Text,
                        {
                          key: 'name-text',
                          style: { textAlign: 'center' as 'center' },
                        },
                        'Print Name'
                      ),
                    ]
                  ),

                  // Footer with 3 columns: left (company+date), center (doc ID), right (page number)
                  React.createElement(
                    View,
                    {
                      key: 'footer',
                      style: styles.footer,
                      fixed: true,
                    },
                    [
                      // Left column: Generated by Forms Notamess + date
                      React.createElement(
                        Text,
                        {
                          key: 'generated',
                          style: styles.footerLeft,
                        },
                        `Generated By Forms Notamess | ${new Date().toLocaleDateString()}`
                      ),
                      // Center column: Document ID
                      React.createElement(
                        Text,
                        {
                          key: 'id',
                          style: styles.footerCenter,
                        },
                        `${document.id}`
                      ),
                      // Right column: Page number
                      React.createElement(Text, {
                        key: 'page-number',
                        style: styles.footerRight,
                        render: ({ pageNumber, totalPages }) =>
                          `Page ${pageNumber} of ${totalPages}`,
                      }),
                    ]
                  ),
                ]
              )
            : null,
        ].filter(Boolean) // Filter out null elements
      );

      // Generate PDF
      const pdfBlob = await pdf(documentElement).toBlob();

      return pdfBlob;
    } catch (error) {
      console.error('Error generating PDF with react-pdf:', error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error('Failed to generate PDF: ' + errorMessage);
    }
  }

  /**
   * Export a document to Word (DOCX) format
   * Note: This is a simplified mock implementation
   * In a real application, you would use a library like docx.js
   */
  private async exportToDocx(
    document: Document,
    options: ExportOptions
  ): Promise<Blob> {
    // In a real implementation, you would use a library like docx.js to create a Word document
    // For this mock implementation, we'll create a simple HTML file and convert it to a Blob

    // We're using the HTML export which already includes our footer
    const html = this.exportToHtml(document, options);

    // Create a Blob with the HTML content and a Word MIME type
    // Note: This won't create a valid DOCX file, just a demonstration
    const blob = new Blob([html], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });

    return blob;
  }

  /**
   * Export a document to HTML format
   */
  private exportToHtml(document: Document, options: ExportOptions): string {
    // Create HTML document
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.sanitizeText(document.title)}</title>
  <style>
    @media print {
      body {
        font-size: 12pt;
      }
      .watermark {
        opacity: 0.3 !important;
        display: block !important;
      }
    }

    /* Use the font specified in options */
    body {
      font-family: ${options.styling?.fontFamily || '"Times New Roman", Times, serif'};
      font-size: 16px;
      line-height: 1.5;
      color: #000;
      max-width: 800px;
      margin: 0 auto;
      padding: 25px;
      background-color: #fff;
      position: relative;
    }
    h1 {
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 1em;
      text-align: center;
    }
    h2 {
      font-size: 1.2em;
      font-weight: bold;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
    }
    p {
      margin: 0.5em 0;
    }
    .metadata {
      color: #666;
      font-size: 0.9em;
      margin-bottom: 2em;
    }
    .section {
      margin-bottom: 1.5em;
    }
    .section-content {
      white-space: pre-wrap;
      line-height: 1.5;
    }
    .watermark {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 4em;
      color: rgba(200, 200, 200, 0.3);
      pointer-events: none;
      z-index: -1;
    }
    footer {
      margin-top: 100px; /* Increased margin to create more space between content and footer */
      color: #666;
      font-size: 0.8em;
      padding-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .indented-1 { margin-left: 20px; }
    .indented-2 { margin-left: 40px; }
    .indented-3 { margin-left: 60px; }
    ul, ol {
      margin: 0.5em 0;
      padding-left: 2em;
    }
    li {
      margin-bottom: 0.3em;
    }
    b, strong {
      font-weight: bold;
    }
    i, em {
      font-style: italic;
    }
    u {
      text-decoration: underline;
    }
    .text-center {
      text-align: center;
    }
    .text-right {
      text-align: right;
    }
    .text-justify {
      text-align: justify;
    }
    pre {
      white-space: pre-wrap;
      font-family: inherit;
      margin: 0;
    }
  </style>
</head>
<body${options.styling?.fontClassName ? ` class="${options.styling.fontClassName}"` : ''}>
  <h1>${this.sanitizeText(document.title)}</h1>`;

    // Add description if available
    if (document.description) {
      html += `
  <p class="text-center">${this.sanitizeText(document.description)}</p>`;
    }

    // Add metadata if requested
    if (options.includeMetadata) {
      html += `
  <div class="metadata">
    <div>Type: ${document.document_type}</div>
    <div>Status: ${document.status}</div>
    <div>Created: ${new Date(document.created_at).toLocaleDateString()}</div>
  </div>`;
    }

    // Add watermark if requested
    if (options.includeWatermark && options.watermarkText) {
      const opacity = (options.watermarkOpacity || 30) / 100;
      html += `
  <div class="watermark" style="opacity: ${opacity};">${this.sanitizeText(options.watermarkText || '')}</div>`;
    }

    // Process HTML content to preserve formatting
    const processHtmlContent = (content: string): string => {
      // If content already has HTML formatting, just make sure it's properly structured
      if (content.includes('<')) {
        // Preserve alignment styles
        content = content
          .replace(
            /<div style="text-align:\s*center[^>]*>/gi,
            '<div class="text-center">'
          )
          .replace(
            /<div style="text-align:\s*right[^>]*>/gi,
            '<div class="text-right">'
          )
          .replace(
            /<div style="text-align:\s*justify[^>]*>/gi,
            '<div class="text-justify">'
          );

        // Preserve indentation
        content = content.replace(
          /<div style="margin-left:\s*(\d+)px[^>]*>/gi,
          (_match, indent) => {
            const level = Math.floor(parseInt(indent) / 20);
            return `<div class="indented-${level}">`;
          }
        );

        // Make sure lists are properly formatted
        content = content
          .replace(/<ul[^>]*>/gi, '<ul>')
          .replace(/<ol[^>]*>/gi, '<ol>')
          .replace(/<li[^>]*>/gi, '<li>');

        return content;
      } else {
        // Plain text, wrap in paragraphs
        return content
          .split('\n')
          .map((line) => (line.trim() ? `<p>${line}</p>` : '<br/>'))
          .join('\n');
      }
    };

    // Add document content
    if (
      document.content &&
      typeof document.content === 'object' &&
      document.content.sections
    ) {
      const sections = document.content.sections;

      for (const section of sections) {
        html += `
  <div class="section">`;

        if (section.title) {
          html += `
    <h2>${section.title}</h2>`;
        }

        const processedContent = processHtmlContent(section.content);
        html += `
    <div class="section-content">
      ${processedContent}
    </div>
  </div>`;
      }
    } else if (document.content) {
      // Handle plain text content
      const content =
        typeof document.content === 'string'
          ? document.content
          : JSON.stringify(document.content, null, 2);

      html += `
  <div class="section">
    <pre>${content}</pre>
  </div>`;
    }

    // Add signature page if requested
    if (options.includeSignaturePage) {
      html += `
  <div class="section" style="page-break-before: always;">
    <h2>Signature Page</h2>
    <div style="margin-top: 30px;">
      <p>Signed by:</p>`;

      // If we have a signature image, add it
      if (
        options.signatureData &&
        options.signatureData.startsWith('data:image')
      ) {
        html += `
      <div style="margin-top: 20px;">
        <img src="${options.signatureData}" alt="Signature" style="max-width: 200px; max-height: 80px;" />
      </div>`;

        // Add signature info if available
        if (options.signatureInfo) {
          html += `
      <div style="margin-top: 10px;">
        <p><strong>Name:</strong> ${options.signatureInfo.name}</p>
        <p><strong>Date:</strong> ${options.signatureInfo.date}</p>`;

          if (options.signatureInfo.title) {
            html += `
        <p><strong>Title:</strong> ${options.signatureInfo.title}</p>`;
          }

          html += `
      </div>`;
        }
      } else {
        // No signature image, add signature lines
        html += `
      <div style="margin-top: 20px;">
        <div style="border-bottom: 1px solid #000; width: 200px; height: 30px;"></div>
        <p>Signature</p>
      </div>
      <div style="margin-top: 20px;">
        <div style="border-bottom: 1px solid #000; width: 200px; height: 30px;"></div>
        <p>Date</p>
      </div>
      <div style="margin-top: 20px;">
        <div style="border-bottom: 1px solid #000; width: 200px; height: 30px;"></div>
        <p>Print Name</p>
      </div>
      <div style="margin-top: 20px;">
        <div style="border-bottom: 1px solid #000; width: 200px; height: 30px;"></div>
        <p>Title</p>
      </div>`;
      }

      html += `
    </div>
  </div>`;
    }

    // Add signature if available
    if (
      document.content &&
      typeof document.content === 'object' &&
      'signature' in document.content &&
      document.content.signature
    ) {
      html += `
  <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
    <h2 style="font-size: 1.2rem; margin-bottom: 1rem;">Signature</h2>
    <div style="margin-bottom: 0.5rem;">
      <img src="${document.content.signature}" alt="Signature" style="max-width: 200px; max-height: 80px;" />
    </div>
    <p>Signed electronically</p>
    <p>Date: ${new Date().toLocaleDateString()}</p>
  </div>`;
    }

    // Add footer with 3 columns: left (company+date), center (doc ID), right (page number)
    const currentDate = new Date().toLocaleDateString();
    html += `
  <footer style="display: flex; justify-content: space-between; align-items: center;">
    <div style="text-align: left; flex: 1;">Generated By Forms Notamess | ${currentDate}</div>
    <div style="text-align: center; flex: 1;">${document.id}</div>
    <div style="text-align: right; flex: 1;">Page 1</div>
  </footer>`;

    // Close HTML document
    html += `
</body>
</html>`;

    return html;
  }

  /**
   * Export a document to Markdown format
   */
  private exportToMarkdown(document: Document, options: ExportOptions): string {
    let markdown = `# ${document.title}\n\n`;

    // Add metadata if requested
    if (options.includeMetadata) {
      markdown += `**Type:** ${document.document_type}  \n`;
      markdown += `**Status:** ${document.status}  \n`;
      markdown += `**Created:** ${new Date(document.created_at).toLocaleDateString()}  \n\n`;
    }

    // Add document content
    if (
      document.content &&
      typeof document.content === 'object' &&
      document.content.sections
    ) {
      const sections = document.content.sections;

      for (const section of sections) {
        if (section.title) {
          markdown += `## ${section.title}\n\n`;
        }

        // Convert HTML content to markdown if needed
        let content = section.content;
        if (content.includes('<')) {
          // Simple HTML to markdown conversion (for basic HTML)
          content = content
            .replace(/<h1[^>]*>([^<]+)<\/h1>/g, '# $1\n')
            .replace(/<h2[^>]*>([^<]+)<\/h2>/g, '## $1\n')
            .replace(/<h3[^>]*>([^<]+)<\/h3>/g, '### $1\n')
            .replace(/<p[^>]*>([^<]+)<\/p>/g, '$1\n\n')
            .replace(/<strong[^>]*>([^<]+)<\/strong>/g, '**$1**')
            .replace(/<em[^>]*>([^<]+)<\/em>/g, '*$1*')
            .replace(/<ul[^>]*>([\s\S]*?)<\/ul>/g, '$1')
            .replace(/<li[^>]*>([^<]+)<\/li>/g, '- $1\n')
            .replace(/<br\s*\/?>/g, '\n')
            .replace(/<[^>]*>/g, '');
        }

        markdown += `${content}\n\n`;
      }
    } else if (document.content) {
      // Handle plain text content
      const content =
        typeof document.content === 'string'
          ? document.content
          : JSON.stringify(document.content, null, 2);

      markdown += content;
    }

    // Add watermark if requested
    if (options.includeWatermark && options.watermarkText) {
      markdown += `\n\n> *${options.watermarkText}*\n`;
    }

    // Add signature page if requested
    if (options.includeSignaturePage) {
      markdown += `\n\n## Signature Page\n\n`;
      markdown += `Signed by:\n\n`;
      markdown += `Signature: _______________________\n\n`;
      markdown += `Date: _______________________\n\n`;
      markdown += `Print Name: _______________________\n\n`;
      markdown += `Title: _______________________\n\n`;
    }

    // Add footer with "Created by Notamess" and export date
    const currentDate = new Date().toLocaleDateString();
    markdown += `\n\n---\n*Created by Notamess | ${currentDate}*\n`;

    return markdown;
  }

  /**
   * Export a document to plain text format
   */
  private exportToText(document: Document, options: ExportOptions): string {
    let text = `${document.title}\n\n`;

    // Add metadata if requested
    if (options.includeMetadata) {
      text += `Type: ${document.document_type}\n`;
      text += `Status: ${document.status}\n`;
      text += `Created: ${new Date(document.created_at).toLocaleDateString()}\n\n`;
    }

    // Add document content
    if (
      document.content &&
      typeof document.content === 'object' &&
      document.content.sections
    ) {
      const sections = document.content.sections;

      for (const section of sections) {
        if (section.title) {
          text += `${section.title}\n${'='.repeat(section.title.length)}\n\n`;
        }

        // Convert HTML content to plain text if needed
        let content = section.content;
        if (content.includes('<')) {
          // Simple HTML to text conversion (for basic HTML)
          content = content.replace(/<[^>]*>/g, '');
        }

        text += `${content}\n\n`;
      }
    } else if (document.content) {
      // Handle plain text content
      const content =
        typeof document.content === 'string'
          ? document.content
          : JSON.stringify(document.content, null, 2);

      text += content;
    }

    // Add watermark if requested
    if (options.includeWatermark && options.watermarkText) {
      text += `\n\n${options.watermarkText}\n`;
    }

    // Add signature page if requested
    if (options.includeSignaturePage) {
      text += `\n\nSIGNATURE PAGE\n=============\n\n`;
      text += `Signed by:\n\n`;
      text += `Signature: _______________________\n\n`;
      text += `Date: _______________________\n\n`;
      text += `Print Name: _______________________\n\n`;
      text += `Title: _______________________\n\n`;
    }

    return text;
  }
}
