import type { FormState } from '@/lib/contexts/FormStateContext';
import { beforeEach, describe, expect, it } from 'vitest';
import type { DocumentTemplate } from '../DocumentGenerationService';
import { DocumentGenerationService } from '../DocumentGenerationService';

describe('DocumentGenerationService', () => {
  let service: DocumentGenerationService;
  let template: DocumentTemplate;
  let formState: FormState;
  let options: any;

  beforeEach(() => {
    service = DocumentGenerationService.getInstance();
    template = {
      metadata: {
        id: '1',
        version: '1.0',
        name: 'Test Document',
        description: 'A test document',
        category: 'form',
        jurisdiction: 'US',
        language: 'en',
        tags: ['test'],
        lastUpdated: new Date(),
        isPublished: true,
      },
      content: 'Hello {{name}},\n\nThis is a test document.',
      variables: [
        {
          name: 'name',
          description: 'Full name',
          type: 'string',
          value: '<PERSON>',
        },
      ],
    };

    formState = {
      formData: {
        name: '<PERSON>',
      },
      currentSection: 0,
      isComplete: false,
      isDirty: false,
      lastSaved: new Date(),
      needsSync: false,
    };

    options = {
      format: 'pdf',
      styling: {
        theme: 'light',
        signature: {
          signatory: {
            name: '<PERSON>',
            role: 'Manager',
          },
          timestamp: new Date('2024-01-01'),
          validUntil: new Date('2025-01-01'),
        },
      },
    };
  });

  describe('generateDocument', () => {
    it('should generate a PDF document with signature', async () => {
      options.format = 'pdf';
      const result = await service.generateDocument(
        template,
        formState,
        options
      );
      expect(result).toBeInstanceOf(Blob);
      if (result instanceof Blob) {
        expect(await result.arrayBuffer()).toBeTruthy();
      }
    });

    it('should generate a DOCX document with signature', async () => {
      options.format = 'docx';
      const result = await service.generateDocument(
        template,
        formState,
        options
      );
      expect(result).toBeInstanceOf(Buffer);
      if (Buffer.isBuffer(result)) {
        expect(result.length).toBeGreaterThan(0);
      }
    });
  });

  describe('signature handling', () => {
    it('should include signature block in DOCX', async () => {
      options.format = 'docx';
      const result = await service.generateDocument(
        template,
        formState,
        options
      );

      // Verify that the document was generated
      expect(result).toBeInstanceOf(Buffer);
      if (Buffer.isBuffer(result)) {
        expect(result.length).toBeGreaterThan(0);

        // Convert to base64 for basic content checks
        const base64Content = result.toString('base64');
        expect(base64Content).toBeTruthy();
        expect(base64Content.length).toBeGreaterThan(0);
      }
    });
  });
});
