import { FormState } from '@/lib/contexts/FormStateContext';
import { CompressionService } from './CompressionService';
import { NotificationService } from './NotificationService';

interface StorageAdapter {
  save(key: string, data: unknown, compress?: boolean): Promise<void>;
  load(key: string): Promise<unknown | null>;
  delete(key: string): Promise<void>;
}

class LocalStorageAdapter implements StorageAdapter {
  private compression: CompressionService;

  constructor() {
    this.compression = CompressionService.getInstance();
  }

  async save(key: string, data: unknown, compress = false): Promise<void> {
    const value = compress
      ? await this.compression.compress(data)
      : JSON.stringify(data);
    localStorage.setItem(key, value);
  }

  async load(key: string): Promise<unknown | null> {
    const data = localStorage.getItem(key);
    if (!data) return null;

    try {
      // Try to decompress first
      return await this.compression.decompress(data);
    } catch {
      // If decompression fails, try parsing as regular JSON
      return JSON.parse(data);
    }
  }

  async delete(key: string): Promise<void> {
    localStorage.removeItem(key);
  }
}

class IndexedDBAdapter implements StorageAdapter {
  private dbName = 'forms_db';
  private storeName = 'form_states';
  private db: IDBDatabase | null = null;
  private compression: CompressionService;

  constructor() {
    this.compression = CompressionService.getInstance();
  }

  private async getDB(): Promise<IDBDatabase> {
    if (this.db) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        db.createObjectStore(this.storeName);
      };
    });
  }

  async save(key: string, data: unknown, compress = false): Promise<void> {
    const db = await this.getDB();
    const value = compress
      ? await this.compression.compress(data)
      : JSON.stringify(data);

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.put(value, key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  async load(key: string): Promise<unknown | null> {
    const db = await this.getDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = async () => {
        const data = request.result;
        if (!data) return resolve(null);

        try {
          // Try to decompress first
          resolve(await this.compression.decompress(data));
        } catch {
          // If decompression fails, try parsing as regular JSON
          resolve(JSON.parse(data));
        }
      };
    });
  }

  async delete(key: string): Promise<void> {
    const db = await this.getDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(this.storeName, 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(key);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }
}

export class FormPersistenceService {
  private localStorage: StorageAdapter;
  private indexedDB: StorageAdapter;
  private syncQueue: Map<string, FormState>;
  private isOnline: boolean;
  private maxRetries = 3;
  private retryDelay = 1000; // Start with 1 second delay
  private notifications: NotificationService;
  private compression: CompressionService;

  // Size thresholds in bytes
  private readonly COMPRESSION_THRESHOLD = 1024 * 50; // 50KB
  private readonly INDEXEDDB_THRESHOLD = 1024 * 1024 * 5; // 5MB

  constructor() {
    this.localStorage = new LocalStorageAdapter();
    this.indexedDB = new IndexedDBAdapter();
    this.syncQueue = new Map();
    this.isOnline = typeof navigator !== 'undefined' ? navigator.onLine : false;
    this.notifications = NotificationService.getInstance();
    this.compression = CompressionService.getInstance();
    this.notifications.setRetryCallback(this.retrySync.bind(this));

    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
    }
  }

  private handleOnline = () => {
    this.isOnline = true;
    this.notifications.onlineSync();
    void this.processSyncQueue();
  };

  private handleOffline = () => {
    this.isOnline = false;
    this.notifications.offlineMode();
  };

  private async processSyncQueue(): Promise<void> {
    if (!this.isOnline || this.syncQueue.size === 0) return;

    const entries = Array.from(this.syncQueue.entries());
    let hasErrors = false;

    for (const [formId, state] of entries) {
      try {
        await this.syncWithServer(formId, state);
        this.syncQueue.delete(formId);
      } catch (error) {
        console.error(`Failed to sync form ${formId}:`, error);
        hasErrors = true;
      }
    }

    if (hasErrors) {
      this.notifications.syncError();
    } else if (entries.length > 0) {
      this.notifications.syncComplete();
    }
  }

  private async syncWithServer(
    formId: string,
    state: FormState,
    retryCount = 0
  ): Promise<void> {
    try {
      const response = await fetch(`/api/forms/${formId}/state`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...state,
          lastSaved: state.lastSaved?.toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Sync failed');
      }

      return;
    } catch (error) {
      if (retryCount < this.maxRetries) {
        const delay = this.retryDelay * Math.pow(2, retryCount);
        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.syncWithServer(formId, state, retryCount + 1);
      }
      throw error;
    }
  }

  private async resolveConflict(
    formId: string,
    localState: FormState,
    serverState: FormState
  ): Promise<FormState> {
    if (
      serverState.lastSaved &&
      localState.lastSaved &&
      new Date(serverState.lastSaved) > new Date(localState.lastSaved)
    ) {
      this.notifications.formStateConflict();
      return serverState;
    }

    return localState;
  }

  private getStorageKey(formId: string): string {
    return `form_state_${formId}`;
  }

  async saveState(formId: string, state: FormState): Promise<void> {
    const key = this.getStorageKey(formId);
    const size = new Blob([JSON.stringify(state)]).size;
    const shouldCompress = size > this.COMPRESSION_THRESHOLD;
    const useIndexedDB = size > this.INDEXEDDB_THRESHOLD;

    try {
      if (useIndexedDB) {
        await this.indexedDB.save(key, state, shouldCompress);
      } else {
        await this.localStorage.save(key, state, shouldCompress);
      }

      // Add to sync queue if online sync is needed
      if (state.needsSync) {
        this.syncQueue.set(formId, state);
        if (this.isOnline) {
          void this.processSyncQueue();
        } else {
          this.notifications.offlineSave();
        }
      }

      this.notifications.saveSuccess();
    } catch (error) {
      console.error('Failed to save form state:', error);
      this.notifications.saveError();
      throw error;
    }
  }

  async loadState(formId: string): Promise<FormState | null> {
    const key = this.getStorageKey(formId);
    try {
      // Try IndexedDB first
      let state = (await this.indexedDB.load(key)) as FormState | null;

      // Fall back to localStorage if not found in IndexedDB
      if (!state) {
        state = (await this.localStorage.load(key)) as FormState | null;
      }

      // If state exists and we're online, check for conflicts
      if (state && this.isOnline && state.needsSync) {
        try {
          const response = await fetch(`/api/forms/${formId}/state`);
          if (response.ok) {
            const serverState = await response.json();
            state = await this.resolveConflict(formId, state, serverState);
          }
        } catch (error) {
          console.warn('Failed to check server state:', error);
          // Continue with local state if server check fails
        }
      }

      return state;
    } catch (error) {
      console.error('Failed to load form state:', error);
      this.notifications.loadError();
      throw error;
    }
  }

  async clearState(formId: string): Promise<void> {
    const key = this.getStorageKey(formId);
    try {
      // Clear from both storage types to ensure complete cleanup
      await Promise.all([
        this.localStorage.delete(key),
        this.indexedDB.delete(key),
      ]);

      // Remove from sync queue if present
      this.syncQueue.delete(formId);

      this.notifications.clearSuccess();
    } catch (error) {
      console.error('Failed to clear form state:', error);
      this.notifications.clearError();
      throw error;
    }
  }

  destroy(): void {
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
    }
  }

  private async retrySync(): Promise<void> {
    if (this.isOnline) {
      await this.processSyncQueue();
    }
  }
}
