'use client';

import { MonthlyTrendChart } from '@/components/reports/MonthlyTrendChart';
import { PerformanceSummaryCard } from '@/components/reports/PerformanceSummaryCard';
import { PopularTimeSlotsCard } from '@/components/reports/PopularTimeSlotsCard';
import { ReportFiltersComponent } from '@/components/reports/ReportFilters';
import { SaveReportDialog } from '@/components/reports/SaveReportDialog';
import { TopClientsCard } from '@/components/reports/TopClientsCard';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLawyerReports } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { ReportFilters } from '@/lib/types/database-modules';
import { ArrowLeft, Download, Printer } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function PerformanceReportPage() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  const {
    performanceReport,
    loading,
    generatePerformanceReport,
    saveReport,
    getDateRanges,
  } = useLawyerReports();

  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: getDateRanges().last30Days,
    statuses: [],
    consultationTypes: [],
    isRecurring: 'indeterminate',
  });

  // Generate report on initial load
  useEffect(() => {
    generatePerformanceReport(filters);
  }, []);

  // Handle filters change
  const handleFiltersChange = (newFilters: ReportFilters) => {
    setFilters(newFilters);
  };

  // Handle apply filters
  const handleApplyFilters = () => {
    generatePerformanceReport(filters);
  };

  // Handle save report
  const handleSaveReport = async (reportName: string) => {
    if (!performanceReport) {
      toast.error('No report data to save');
      return false;
    }

    const result = await saveReport(
      reportName,
      'performance' as any,
      {
        filters,
      },
      performanceReport
    );

    return !!result;
  };

  // Handle export report
  const handleExportReport = () => {
    if (!performanceReport) {
      toast.error('No report data to export');
      return;
    }

    // Create a blob with the report data
    const blob = new Blob([JSON.stringify(performanceReport, null, 2)], {
      type: 'application/json',
    });

    // Create a download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Report exported successfully');
  };

  // Handle print report
  const handlePrintReport = () => {
    window.print();
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Performance Report</h1>
          <p className="text-muted-foreground">
            Analyze your consultation performance and trends
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/${username}/lawyer/reports`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Reports
          </Button>
          <Button
            variant="outline"
            onClick={handleExportReport}
            disabled={!performanceReport || loading}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button
            variant="outline"
            onClick={handlePrintReport}
            disabled={!performanceReport || loading}
          >
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <SaveReportDialog
            reportType="performance"
            onSave={handleSaveReport}
          />
        </div>
      </div>

      <ReportFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onApplyFilters={handleApplyFilters}
        predefinedRanges={getDateRanges()}
        isLoading={loading}
      />

      <div className="space-y-6 print:space-y-4">
        <PerformanceSummaryCard
          data={performanceReport?.performance_summary || null}
          loading={loading}
        />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 print:gap-4">
          <MonthlyTrendChart
            data={performanceReport?.monthly_trend || null}
            loading={loading}
          />

          <Tabs defaultValue="time_slots" className="print:hidden">
            <TabsList className="mb-4">
              <TabsTrigger value="time_slots">Popular Time Slots</TabsTrigger>
              <TabsTrigger value="clients">Top Clients</TabsTrigger>
            </TabsList>
            <TabsContent value="time_slots">
              <PopularTimeSlotsCard
                data={performanceReport?.popular_time_slots || null}
                loading={loading}
              />
            </TabsContent>
            <TabsContent value="clients">
              <TopClientsCard
                data={performanceReport?.top_clients || null}
                loading={loading}
              />
            </TabsContent>
          </Tabs>

          <div className="hidden print:block">
            <PopularTimeSlotsCard
              data={performanceReport?.popular_time_slots || null}
              loading={loading}
            />
          </div>

          <div className="hidden print:block">
            <TopClientsCard
              data={performanceReport?.top_clients || null}
              loading={loading}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
