'use client';

import { useParams } from 'next/navigation';

import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCollaboration } from '@/lib/hooks';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Loader2,
  Plus,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface ProjectEvent {
  id: string;
  title: string;
  projectId: string;
  projectName: string;
  date: Date;
  type: 'deadline' | 'meeting' | 'milestone';
}

export default function ProjectCalendarPage() {
  const params = useParams();
  const username = params.username as string;
  // We don't need the profile here
  const { projects, loading, fetchProjects } = useCollaboration();
  const router = useRouter();
  const [date, setDate] = useState<Date>(new Date());
  const [events, setEvents] = useState<ProjectEvent[]>([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [month, setMonth] = useState<Date>(new Date());

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  useEffect(() => {
    if (projects.length > 0) {
      generateProjectEvents();
    }
  }, [projects]);

  // Generate sample events based on project start and end dates
  const generateProjectEvents = () => {
    setIsLoadingEvents(true);

    try {
      const generatedEvents: ProjectEvent[] = [];

      // For each project, create events based on start and end dates
      projects.forEach((project) => {
        // Add project start date as a milestone
        const startDate = new Date(project.start_date ?? '');
        generatedEvents.push({
          id: `start-${project.id}`,
          title: 'Project Start',
          projectId: project.id,
          projectName: project.name,
          date: startDate,
          type: 'milestone',
        });

        // Add project end date as a deadline
        const endDate = new Date(project.end_date ?? '');
        generatedEvents.push({
          id: `end-${project.id}`,
          title: 'Project Deadline',
          projectId: project.id,
          projectName: project.name,
          date: endDate,
          type: 'deadline',
        });

        // Add a milestone in the middle of the project
        const middleDate = new Date(
          (startDate.getTime() + endDate.getTime()) / 2
        );
        generatedEvents.push({
          id: `milestone-${project.id}`,
          title: 'Project Milestone',
          projectId: project.id,
          projectName: project.name,
          date: middleDate,
          type: 'milestone',
        });

        // Add a meeting every week from start date until end date
        let meetingDate = new Date(startDate);
        let meetingCount = 1;
        while (meetingDate < endDate) {
          // Add 7 days for weekly meeting
          meetingDate = new Date(meetingDate);
          meetingDate.setDate(meetingDate.getDate() + 7);

          if (meetingDate < endDate) {
            generatedEvents.push({
              id: `meeting-${project.id}-${meetingCount}`,
              title: 'Team Meeting',
              projectId: project.id,
              projectName: project.name,
              date: meetingDate,
              type: 'meeting',
            });
            meetingCount++;
          }
        }
      });

      setEvents(generatedEvents);
    } catch (error) {
      console.error('Error generating project events:', error);
      toast.error('Failed to generate project events');
    } finally {
      setIsLoadingEvents(false);
    }
  };

  // Get events for the selected date
  const getEventsForDate = (date: Date) => {
    return events.filter((event) => {
      const eventDate = new Date(event.date);
      return (
        eventDate.getDate() === date.getDate() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getFullYear() === date.getFullYear()
      );
    });
  };

  // Get events for the selected month
  const getEventsForMonth = (month: Date) => {
    return events.filter((event) => {
      const eventDate = new Date(event.date);
      return (
        eventDate.getMonth() === month.getMonth() &&
        eventDate.getFullYear() === month.getFullYear()
      );
    });
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setMonth(newMonth);
  };

  // Navigate to next month
  const goToNextMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setMonth(newMonth);
  };

  // Format date to display in the UI
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get color for event type
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'deadline':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'meeting':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'milestone':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const selectedDateEvents = getEventsForDate(date);
  const monthEvents = getEventsForMonth(month);

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <h1 className="text-2xl font-bold">Project Calendar</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle>Calendar</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={goToPreviousMonth}
                >
                  <ChevronLeft className="size-4" />
                </Button>
                <div className="font-medium">
                  {month.toLocaleDateString('en-US', {
                    month: 'long',
                    year: 'numeric',
                  })}
                </div>
                <Button variant="outline" size="icon" onClick={goToNextMonth}>
                  <ChevronRight className="size-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading || isLoadingEvents ? (
                <div className="flex items-center justify-center py-10">
                  <Loader2 className="size-8 text-neutral-500 animate-spin" />
                </div>
              ) : (
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => newDate && setDate(newDate)}
                  month={month}
                  onMonthChange={setMonth}
                  className="rounded-md border"
                  modifiers={{
                    event: monthEvents.map((event) => new Date(event.date)),
                  }}
                  modifiersStyles={{
                    event: {
                      fontWeight: 'bold',
                      backgroundColor: 'rgba(59, 130, 246, 0.1)',
                      color: 'rgb(59, 130, 246)',
                      borderRadius: '100%',
                    },
                  }}
                />
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="size-5 text-neutral-500" />
                <span>{formatDate(date)}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedDateEvents.length === 0 ? (
                <div className="text-center py-10">
                  <p className="text-neutral-500 mb-4">
                    No events scheduled for this day
                  </p>
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(`/${username}/collaboration/projects/new`)
                    }
                    className="gap-1"
                  >
                    <Plus className="size-4" />
                    <span>Create Project</span>
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {selectedDateEvents.map((event) => (
                    <div
                      key={event.id}
                      className={`p-3 rounded-md border ${getEventTypeColor(event.type)}`}
                    >
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm">{event.projectName}</div>
                      <div className="text-xs mt-1 capitalize">
                        {event.type}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="mt-4">
            <CardHeader className="pb-2">
              <CardTitle>Upcoming Events</CardTitle>
            </CardHeader>
            <CardContent>
              {events.length === 0 ? (
                <p className="text-neutral-500 text-center py-4">
                  No upcoming events
                </p>
              ) : (
                <div className="space-y-3">
                  {events
                    .filter((event) => new Date(event.date) >= new Date())
                    .sort(
                      (a, b) =>
                        new Date(a.date).getTime() - new Date(b.date).getTime()
                    )
                    .slice(0, 5)
                    .map((event) => (
                      <div
                        key={event.id}
                        className="flex items-center gap-3 p-2 rounded-md hover:bg-neutral-50 transition-colors"
                      >
                        <div
                          className={`w-2 h-2 rounded-full ${event.type === 'deadline' ? 'bg-red-500' : event.type === 'meeting' ? 'bg-blue-500' : 'bg-green-500'}`}
                        ></div>
                        <div>
                          <div className="font-medium">{event.title}</div>
                          <div className="text-xs text-neutral-500">
                            {new Date(event.date).toLocaleDateString()} •{' '}
                            {event.projectName}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
