-- Migration to remove all smart contracts related tables and columns

-- First, drop tables with foreign key constraints
DROP TABLE IF EXISTS public.contract_events;
DROP TABLE IF EXISTS public.contract_signers;

-- Then drop the main tables
DROP TABLE IF EXISTS public.smart_contracts;
DROP TABLE IF EXISTS public.contract_templates;

-- Remove smart contracts columns from user_usage
ALTER TABLE public.user_usage DROP COLUMN IF EXISTS smart_contracts_count;

-- Remove smart contracts columns from usage_limits
ALTER TABLE public.usage_limits DROP COLUMN IF EXISTS smart_contracts_limit;

-- Update features in usage_limits to remove smart_contract_deployment
UPDATE public.usage_limits
SET features = features - 'smart_contract_deployment'
WHERE features ? 'smart_contract_deployment';
