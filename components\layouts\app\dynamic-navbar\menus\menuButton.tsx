import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import React from 'react';

type menuButtonTypes = {
  setMenuOpen: React.Dispatch<React.SetStateAction<boolean>>;
  menuOpen: boolean;
};

export function MenuButton({ setMenuOpen, menuOpen }: menuButtonTypes) {
  return (
    <div className="relative z-0 border-r border-dashed border-neutral-200 flex flex-col items-center justify-center px-1.5">
      <Button
        variant={'shadow_accent'}
        size={'icon'}
        className="size-8"
        onClick={() => setMenuOpen(!menuOpen)}
      >
        <div className="flex h-3 w-4 cursor-pointer flex-col items-center justify-between relative">
          <span
            className={cn(
              'h-[2px] w-full absolute top-0 transform cursor-pointer rounded-lg transition duration-300 ease-in-out',
              'bg-white',
              menuOpen
                ? 'rotate-45 translate-y-[5.2px]'
                : 'rotate-0 translate-y-0'
            )}
          />
          <span
            className={cn(
              'h-[2px] w-full absolute bottom-0 transform cursor-pointer rounded-lg transition duration-300 ease-in-out',
              'bg-white',
              menuOpen
                ? '-rotate-45 -translate-y-[5.2px]'
                : 'rotate-0 translate-y-0'
            )}
          />
        </div>
      </Button>
    </div>
  );
}
