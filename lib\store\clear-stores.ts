'use client';

import { dynamicNavStates } from './dynamicNav';
import { isEditingStore } from './is-editing';
import { userStore } from './user';
import { waitlistStore } from './waitlist';

/**
 * Utility function to clear all Zustand stores when a user logs out
 * This ensures no state persists between user sessions
 */
export function clearAllStores() {
  // Clear user store
  userStore.getState().removeUser(null);
  userStore.getState().removeProfile(null);

  // Clear waitlist store
  waitlistStore.getState().reset();

  // Clear editing store
  isEditingStore.getState().setIsEditing(false);

  // Clear dynamic nav states
  dynamicNavStates.getState().removeUrl();
  dynamicNavStates.getState().removeActions();

  // Font settings have been removed

  console.log('All stores cleared successfully');
}
