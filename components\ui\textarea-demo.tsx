'use client';

import * as Textarea from '@/components/ui/textarea';
import { useState } from 'react';

export function TextareaDemo() {
  const [text, setText] = useState('');

  return (
    <div className="mx-auto w-full max-w-96">
      <Textarea.Root
        placeholder="Jot down your thoughts..."
        value={text}
        onChange={(e) => setText(e.target.value)}
      >
        <Textarea.CharCounter current={text.length} max={200} />
      </Textarea.Root>
    </div>
  );
}
