'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useOrganizations } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { OrganizationMember } from '@/lib/types/database-modules';
import {
  Crown,
  Loader2,
  Mail,
  MoreHorizontal,
  Shield,
  Trash,
  UserCog,
  UserPlus,
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface OrganizationMembersListProps {
  organizationId: string;
  initialMembers: OrganizationMember[];
}

export function OrganizationMembersList({
  organizationId,
  initialMembers,
}: OrganizationMembersListProps) {
  const { username } = useParams();
  const { profile } = userStore();
  const { removeMember } = useOrganizations();

  const [members, setMembers] = useState<OrganizationMember[]>(initialMembers);
  const [isLoading, setIsLoading] = useState(false);

  // Update members when initialMembers changes
  useEffect(() => {
    setMembers(initialMembers);
  }, [initialMembers]);

  // Get role badge
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'owner':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Crown className="h-3 w-3 mr-1" />
            Owner
          </Badge>
        );
      case 'admin':
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </Badge>
        );
      default:
        return <Badge className="bg-gray-100 text-gray-800">Member</Badge>;
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string | undefined) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle member removal
  const handleRemoveMember = async (member: OrganizationMember) => {
    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to remove ${member.user?.full_name || 'this member'} from the organization?`
    );

    if (!confirmed) return;

    setIsLoading(true);

    try {
      // Create a promise for the member removal process
      const removeMemberPromise = (async () => {
        const success = await removeMember(member.id);

        if (!success) {
          throw new Error('Failed to remove member');
        }

        // Update local state
        setMembers(members.filter((m) => m.id !== member.id));

        return success;
      })();

      // Use toast.promise to handle loading, success, and error states
      toast.promise(removeMemberPromise, {
        loading: 'Removing member...',
        success: () =>
          `${member.user?.full_name || 'Member'} removed from organization`,
        error: 'Failed to remove member',
      });

      await removeMemberPromise;
    } catch (error: any) {
      console.error('Error removing member:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsLoading(false);
    }
  };

  // Check if current user is owner
  const isCurrentUserOwner = members.some(
    (member) => member.user_id === profile?.id && member.role === 'owner'
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Members ({members.length})</h3>
        <Link href={`/${username}/organizations/${organizationId}/members/add`}>
          <Button size="sm">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Member
          </Button>
        </Link>
      </div>

      <div className="border rounded-lg divide-y">
        {members.map((member) => (
          <div
            key={member.id}
            className="p-4 flex items-center justify-between"
          >
            <div className="flex items-center gap-3">
              <Avatar>
                <AvatarImage
                  src={member.user?.avatar_url || ''}
                  alt={member.user?.full_name || 'Member'}
                />
                <AvatarFallback>
                  {getInitials(member.user?.full_name)}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">
                  {member.user?.full_name || 'Unknown User'}
                </div>
                <div className="text-sm text-neutral-500">
                  {member.user?.email || 'No email'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getRoleBadge(member.role)}

              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => {
                  if (member.user?.email) {
                    window.location.href = `mailto:${member.user.email}`;
                  } else {
                    toast.error('No email address available');
                  }
                }}
              >
                <Mail className="h-4 w-4" />
              </Button>

              {/* Only show dropdown for other members if current user is owner */}
              {(isCurrentUserOwner || member.user_id === profile?.id) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <MoreHorizontal className="h-4 w-4" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {/* Only owners can change roles */}
                    {isCurrentUserOwner && member.user_id !== profile?.id && (
                      <DropdownMenuItem asChild>
                        <Link
                          href={`/${username}/organizations/${organizationId}/members/${member.id}/edit-role`}
                        >
                          <UserCog className="h-4 w-4 mr-2" />
                          Change Role
                        </Link>
                      </DropdownMenuItem>
                    )}

                    {/* Only owners can remove members, and owners can't remove themselves */}
                    {isCurrentUserOwner && member.user_id !== profile?.id && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleRemoveMember(member)}
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Remove from Organization
                        </DropdownMenuItem>
                      </>
                    )}

                    {/* Members can leave the organization */}
                    {member.user_id === profile?.id &&
                      member.role !== 'owner' && (
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleRemoveMember(member)}
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Leave Organization
                        </DropdownMenuItem>
                      )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
