'use client';

import { supabaseClient } from '@/lib/supabase/client';
import { userStore } from '@/lib/store/user';
import { toast } from 'sonner';

export const avatarService = {
  /**
   * Upload an avatar image to Supabase storage
   * Uses a consistent filename pattern to avoid duplicates
   */
  uploadAvatar: async (file: File, userId?: string): Promise<string | null> => {
    try {
      // Get user ID from store if not provided
      const userIdToUse = userId || userStore.getState().user?.id;
      if (!userIdToUse) throw new Error('User not authenticated');

      // Use a consistent filename pattern: {userId}.jpg
      // This ensures we always overwrite the previous avatar
      const filePath = `${userIdToUse}.jpg`;

      // Convert the file to a blob if it's not already
      let fileToUpload = file;
      if (file.type !== 'image/jpeg') {
        try {
          // If it's not a JPEG, convert it to ensure consistency
          const blob = await file.arrayBuffer();
          fileToUpload = new File([blob], filePath, { type: 'image/jpeg' });
        } catch (conversionError) {
          console.error('Error converting file:', conversionError);
          // If conversion fails, use the original file
          fileToUpload = file;
        }
      }

      // First, check if the user is authenticated
      const { data: sessionData } = await supabaseClient.auth.getSession();
      if (!sessionData.session) {
        console.error('User not authenticated');
        toast.error('You must be logged in to upload an avatar');
        return null;
      }

      // Log the user ID and file details for debugging
      console.log('Uploading avatar for user:', userIdToUse);
      console.log('File details:', {
        name: fileToUpload.name,
        type: fileToUpload.type,
        size: fileToUpload.size,
      });

      // Upload the file to Supabase storage with explicit error handling
      const uploadResult = await supabaseClient.storage
        .from('avatars')
        .upload(filePath, fileToUpload, {
          cacheControl: '3600',
          upsert: true, // This will overwrite the existing file
          contentType: 'image/jpeg',
        });

      if (uploadResult.error) {
        const error = uploadResult.error;
        console.error('Storage upload error details:', {
          message: error.message || 'No message',
          name: error.name || 'Unknown error',
          stack: error.stack || 'No stack trace',
          details: error.details || 'No details',
          hint: error.hint || 'No hint',
          code: error.code || 'No code',
        });

        // Show a more specific error message based on the error
        if (error.message?.includes('permission')) {
          toast.error('Permission denied when uploading avatar');
        } else if (error.message?.includes('not found')) {
          toast.error('Storage bucket not found');
        } else {
          toast.error(`Upload error: ${error.message || 'Unknown error'}`);
        }
        return null;
      }

      // Get the public URL
      const { data: publicUrlData } = supabaseClient.storage
        .from('avatars')
        .getPublicUrl(filePath);

      if (!publicUrlData) {
        console.error('Failed to get public URL');
        toast.error('Could not generate public URL for avatar');
        return null;
      }

      // Add a cache-busting parameter to the URL to prevent browser caching
      const cacheBuster = `?t=${Date.now()}`;
      const publicUrl = `${publicUrlData.publicUrl}${cacheBuster}`;

      // Clean up old avatar files in the background
      cleanupOldAvatars(userIdToUse).catch(console.error);

      return publicUrl;
    } catch (error: any) {
      // More detailed error logging
      console.error('Error uploading avatar:', error);
      console.error('Error details:', {
        message: error.message || 'No message',
        name: error.name || 'Unknown error',
        stack: error.stack || 'No stack trace',
        details: error.details || 'No details',
        hint: error.hint || 'No hint',
        code: error.code || 'No code',
      });

      // Provide a more helpful error message
      const errorMessage = error?.message || 'Unknown error';
      toast.error(`Failed to upload avatar: ${errorMessage}`);

      return null;
    }
  },

  /**
   * Update the user's avatar URL in the profiles table
   */
  updateAvatarUrl: async (avatarUrl: string): Promise<boolean> => {
    try {
      const { user } = userStore.getState();
      if (!user) throw new Error('User not authenticated');

      // Update the profile
      const { error } = await supabaseClient
        .from('profiles')
        .update({ avatar_url: avatarUrl, updated_at: new Date().toISOString() })
        .eq('id', user.id);

      if (error) throw error;

      // Update the local store
      userStore.getState().updateProfile({
        ...userStore.getState().profile!,
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
      });

      return true;
    } catch (error) {
      console.error('Error updating avatar URL:', error);
      return false;
    }
  },

  /**
   * Reset the avatar to the default UI Avatars URL
   */
  resetAvatar: async (): Promise<boolean> => {
    try {
      const { user, profile } = userStore.getState();
      if (!user || !profile) throw new Error('User not authenticated');

      // Generate UI Avatars URL
      const name = profile.full_name || 'User';
      const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
        name
      )}&background=0D8ABC&color=fff`;

      // Update the profile
      const { error } = await supabaseClient
        .from('profiles')
        .update({
          avatar_url: uiAvatarUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) throw error;

      // Update the local store
      userStore.getState().updateProfile({
        ...profile,
        avatar_url: uiAvatarUrl,
        updated_at: new Date().toISOString(),
      });

      return true;
    } catch (error) {
      console.error('Error resetting avatar:', error);
      return false;
    }
  },

  /**
   * Get the avatar URL for a user
   */
  getAvatarUrl: (userId: string): Promise<string | null> => {
    return new Promise(async (resolve, reject) => {
      try {
        // Get the user's profile
        const { data, error } = await supabaseClient
          .from('profiles')
          .select('avatar_url, full_name')
          .eq('id', userId)
          .single();

        if (error) throw error;

        if (data?.avatar_url) {
          resolve(data.avatar_url);
        } else {
          // Generate UI Avatars URL as fallback
          const name = data?.full_name || 'User';
          const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
            name
          )}&background=0D8ABC&color=fff`;
          resolve(uiAvatarUrl);
        }
      } catch (error) {
        console.error('Error getting avatar URL:', error);
        reject(error);
      }
    });
  },
};

/**
 * Clean up old avatar files for a user
 * This is a maintenance function that can be called periodically
 * @param userId The user ID
 */
async function cleanupOldAvatars(userId: string): Promise<void> {
  try {
    // First, check if the user is authenticated
    const { data: sessionData } = await supabaseClient.auth.getSession();
    if (!sessionData.session) {
      console.warn('Skipping cleanup - user not authenticated');
      return;
    }

    // List all files in the avatars bucket for this user
    const { data: files, error: listError } = await supabaseClient.storage
      .from('avatars')
      .list('', {
        search: userId,
      });

    if (listError) {
      // Just log the error but don't throw - this is a background operation
      console.warn('Error listing avatar files:', listError);
      return;
    }

    // Skip if no files found
    if (!files || files.length === 0) {
      return;
    }

    // The current avatar should be named {userId}.jpg
    const currentAvatarName = `${userId}.jpg`;

    // Filter out the current avatar and collect old ones to delete
    const filesToDelete = files
      .filter((file) => {
        // Keep the current avatar with the standardized filename
        return file.name !== currentAvatarName;
      })
      .map((file) => file.name);

    if (filesToDelete.length > 0) {
      console.log(
        `Attempting to delete ${filesToDelete.length} old avatar files for user ${userId}`
      );

      // Delete old avatar files one by one to avoid batch deletion issues
      for (const fileName of filesToDelete) {
        try {
          const { error: deleteError } = await supabaseClient.storage
            .from('avatars')
            .remove([fileName]);

          if (deleteError) {
            console.warn(
              `Error deleting avatar file ${fileName}:`,
              deleteError
            );
          } else {
            console.log(`Successfully deleted old avatar file: ${fileName}`);
          }
        } catch (deleteError) {
          console.warn(
            `Exception deleting avatar file ${fileName}:`,
            deleteError
          );
        }
      }
    }
  } catch (error: any) {
    console.error('Error cleaning up old avatars:', error?.message || error);
    // Don't show a toast for this background operation
  }
}
