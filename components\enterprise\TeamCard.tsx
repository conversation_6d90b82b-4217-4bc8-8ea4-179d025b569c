import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Team, TeamMember } from '@/lib/types/database-modules';
import { ChevronRight, Shield, UserCog, Users } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

// Extended TeamMember type to include user profile information
interface ExtendedTeamMember extends TeamMember {
  full_name?: string;
  avatar_url?: string;
}

interface TeamCardProps {
  team: Team & { members?: ExtendedTeamMember[] };
  showActions?: boolean;
}

export function TeamCard({ team, showActions = true }: TeamCardProps) {
  const { username } = useParams();
  const getMemberAvatars = (members: ExtendedTeamMember[], max: number = 3) => {
    const displayMembers = members.slice(0, max);
    const hasMoreMembers = members.length > max;

    return (
      <div className="flex -space-x-2">
        {displayMembers.map((member, index) => (
          <div
            key={member.id}
            className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center overflow-hidden"
            title={member.full_name || `Team member ${index + 1}`}
          >
            {member.avatar_url ? (
              <img
                src={member.avatar_url}
                alt={member.full_name || `Team member ${index + 1}`}
                className="h-full w-full object-cover"
              />
            ) : (
              <span className="text-xs font-medium">
                {(member.full_name || 'User')
                  .split(' ')
                  .map((n: string) => n[0])
                  .join('')
                  .toUpperCase()}
              </span>
            )}
          </div>
        ))}
        {hasMoreMembers && (
          <div
            className="h-8 w-8 rounded-full border-2 border-background bg-muted flex items-center justify-center"
            title={`${members.length - max} more members`}
          >
            <span className="text-xs font-medium">+{members.length - max}</span>
          </div>
        )}
      </div>
    );
  };

  const getEditorCount = (members: ExtendedTeamMember[]) => {
    // In this system, 'member' role is equivalent to editor
    return members.filter((member) => member.role === 'member').length;
  };

  const getAdminCount = (members: ExtendedTeamMember[]) => {
    // Only count admin roles
    return members.filter((member) => member.role === 'admin').length;
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5 text-primary" />
          {team.name}
        </CardTitle>
        <CardDescription>{team.description}</CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm">
              <span className="text-muted-foreground">Members: </span>
              <span className="font-medium">{team.members?.length || 0}</span>
            </div>
            {getMemberAvatars(team.members || [])}
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="flex flex-col space-y-1">
              <span className="text-xs text-muted-foreground">Admins</span>
              <div className="flex items-center">
                <Shield className="mr-1.5 h-3.5 w-3.5 text-muted-foreground" />
                <span className="font-medium">
                  {getAdminCount(team.members || [])}
                </span>
              </div>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-xs text-muted-foreground">Editors</span>
              <div className="flex items-center">
                <UserCog className="mr-1.5 h-3.5 w-3.5 text-muted-foreground" />
                <span className="font-medium">
                  {getEditorCount(team.members || [])}
                </span>
              </div>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="text-xs text-muted-foreground">Permissions</span>
              <span className="font-medium">
                {team.permissions && typeof team.permissions === 'object'
                  ? Array.isArray(team.permissions)
                    ? team.permissions.length
                    : Object.keys(team.permissions).length
                  : 0}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="pt-2">
          <div className="grid w-full grid-cols-2 gap-2">
            <Link
              href={`/${username}/organizations/${team.organization_id}/teams/${team.id}`}
              passHref
            >
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <span>View Team</span>
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
            <Link
              href={`/${username}/organizations/${team.organization_id}/teams/${team.id}/members`}
              passHref
            >
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <span>Members</span>
                <UserCog className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
