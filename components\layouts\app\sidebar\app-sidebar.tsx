'use client';

import { AnimatePresence } from 'motion/react';
import * as React from 'react';

import {
  Sidebar,
  <PERSON>barContent,
  <PERSON>barFooter,
  SidebarHeader,
} from '@/components/ui/sidebar';
import { FormsLogo } from '@/components/ux/icons/logo';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import CollaborationContent from '../contents/collaboration-content';
import DashboardContent from '../contents/dashboard-content';
import DocumentsContent from '../contents/documents-content';
import LawyerContent from '../contents/lawyer-content';
import OrganizationsContent from '../contents/organizations-content';
import SettingsContent from '../contents/settings-content';
import { Profile } from './profile-sidebar';
import { UserPlanDetails } from './user-plan';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { profile, user } = userStore();
  const pathname = usePathname();

  // Determine the active section with more specific path checking
  const isSettings = pathname.includes('/settings');

  // Check if the URL is a direct username (like /lawyereghant)
  const isDirectUsername = pathname.match(/^\/[^\/]+$/) !== null;

  // Check for lawyer paths first, as they can contain 'documents'
  // But exclude direct username paths that happen to contain 'lawyer' in the username
  const isLawyer = !isDirectUsername && pathname.includes('/lawyer');

  // Only consider it a documents path if it contains '/documents'
  const isDocuments = pathname.includes('/documents');
  const isCollaboration = pathname.includes('/collaboration');
  const isOrganizations = pathname.includes('/organizations');

  // Track previous active sections for animation directions
  const [prevSections, setPrevSections] = React.useState({
    isSettings,
    isDocuments,
    isLawyer,
    isCollaboration,
    isOrganizations,
    isDirectUsername,
  });

  // Track direction of transition
  React.useEffect(() => {
    if (
      prevSections.isSettings !== isSettings ||
      prevSections.isDocuments !== isDocuments ||
      prevSections.isLawyer !== isLawyer ||
      prevSections.isCollaboration !== isCollaboration ||
      prevSections.isOrganizations !== isOrganizations ||
      prevSections.isDirectUsername !== isDirectUsername
    ) {
      setPrevSections({
        isSettings,
        isDocuments,
        isLawyer,
        isCollaboration,
        isOrganizations,
        isDirectUsername,
      });
    }
  }, [
    isSettings,
    isDocuments,
    isLawyer,
    isCollaboration,
    isOrganizations,
    isDirectUsername,
    prevSections,
  ]);

  // Determine active section
  const getActiveSection = () => {
    if (isSettings) return 'settings';

    // Check for documents path first
    if (isDocuments) {
      // If it's a lawyer path that also contains documents, prioritize lawyer
      if (pathname.includes('/lawyer/dashboard/documents')) {
        return 'lawyer';
      }
      return 'documents';
    }

    if (isLawyer) return 'lawyer';
    if (isCollaboration) return 'collaboration';
    if (isOrganizations) return 'organizations';

    // If it's a direct username path or the root path, show dashboard
    if (isDirectUsername || pathname === '/') return 'dashboard';
    return 'dashboard';
  };

  // Determine animation direction
  const getDirection = (section: string) => {
    const activeSection = getActiveSection();
    const sections = [
      'dashboard',
      'documents',
      'lawyer',
      'collaboration',
      'organizations',
      'settings',
    ];

    if (section === activeSection) return 0;

    const sectionIndex = sections.indexOf(section);
    const activeSectionIndex = sections.indexOf(activeSection);

    return sectionIndex < activeSectionIndex ? -1 : 1;
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-between">
          <Link
            href={'#'}
            className={cn(
              // buttonVariants({ variant: 'shadow', size: 'icon' }),
              'flex rounded-2xl'
            )}
          >
            <FormsLogo className="size-10" />
          </Link>
          <div className="flex items-center gap-2">
            <Profile />
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="overflow-hidden">
        <AnimatePresence mode="wait" initial={false}>
          {getActiveSection() === 'dashboard' && (
            <DashboardContent
              key="dashboard"
              direction={getDirection('dashboard')}
            />
          )}
          {getActiveSection() === 'documents' && (
            <DocumentsContent
              key="documents"
              direction={getDirection('documents')}
            />
          )}

          {getActiveSection() === 'lawyer' && (
            <LawyerContent key="lawyer" direction={getDirection('lawyer')} />
          )}
          {getActiveSection() === 'collaboration' && (
            <CollaborationContent
              key="collaboration"
              direction={getDirection('collaboration')}
            />
          )}
          {getActiveSection() === 'organizations' && (
            <OrganizationsContent
              key="organizations"
              direction={getDirection('organizations')}
            />
          )}
          {getActiveSection() === 'settings' && (
            <SettingsContent
              key="settings"
              direction={getDirection('settings')}
            />
          )}
        </AnimatePresence>
      </SidebarContent>
      <SidebarFooter>
        <div className="w-full flex flex-col gap-2">
          <div className="w-full flex items-center justify-between">
            <UserPlanDetails />
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
