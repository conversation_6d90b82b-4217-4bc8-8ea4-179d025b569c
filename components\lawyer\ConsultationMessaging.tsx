'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useLawyerMessages } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { LawyerConsultationMessage } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { FileText, PaperclipIcon, Send } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

interface ConsultationMessagingProps {
  consultationId: string;
  className?: string;
}

export function ConsultationMessaging({
  consultationId,
  className,
}: ConsultationMessagingProps) {
  const { user, profile } = userStore();
  const { loading, messages, sendMessage, markMessagesAsRead } =
    useLawyerMessages(consultationId);
  const [newMessage, setNewMessage] = useState('');
  const [attachment, setAttachment] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark messages as read when component mounts
  useEffect(() => {
    markMessagesAsRead();
  }, [markMessagesAsRead]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() && !attachment) return;

    try {
      let attachmentUrl = '';
      let attachmentName = '';
      let attachmentType = '';

      // Upload attachment if present
      if (attachment) {
        setIsUploading(true);

        try {
          // Create a unique file name
          const fileName = `${Date.now()}_${attachment.name}`;

          // Use the Supabase client to upload the file
          const { supabaseClient } = await import('@/lib/supabase/client');

          // Create a promise for the file upload
          const uploadPromise = async () => {
            const { error } = await supabaseClient.storage
              .from('consultation-attachments')
              .upload(fileName, attachment);

            if (error) throw error;

            // Get the public URL
            const { data: urlData } = supabaseClient.storage
              .from('consultation-attachments')
              .getPublicUrl(fileName);

            attachmentUrl = urlData.publicUrl;
            attachmentName = attachment.name;
            attachmentType = attachment.type;

            return { success: true };
          };

          // Use toast.promise with the upload promise
          toast.promise(uploadPromise(), {
            loading: 'Uploading attachment...',
            success: 'Attachment uploaded successfully',
            error: (err) =>
              `Failed to upload attachment: ${err.message || 'Unknown error'}`,
          });
        } catch (err) {
          console.error('Error uploading attachment:', err);
          setIsUploading(false);
          return;
        } finally {
          setIsUploading(false);
        }
      }

      // Create a promise for sending the message
      const sendMessagePromise = async () => {
        return await sendMessage(
          newMessage.trim(),
          attachmentUrl || undefined,
          attachmentName || undefined,
          attachmentType || undefined
        );
      };

      // Send the message with toast.promise
      toast.promise(sendMessagePromise(), {
        loading: 'Sending message...',
        success: 'Message sent successfully',
        error: (err) =>
          `Failed to send message: ${err.message || 'Unknown error'}`,
      });

      // Clear input and attachment
      setNewMessage('');
      setAttachment(null);
    } catch (err) {
      console.error('Error sending message:', err);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (limit to 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File too large', {
          description: 'Maximum file size is 10MB',
        });
        return;
      }

      setAttachment(file);
    }
  };

  const removeAttachment = () => {
    setAttachment(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatMessageTime = (timestamp: string) => {
    return format(new Date(timestamp), 'h:mm a');
  };

  const formatMessageDate = (timestamp: string) => {
    return format(new Date(timestamp), 'MMMM d, yyyy');
  };

  // Group messages by date
  const groupedMessages: {
    date: string;
    messages: LawyerConsultationMessage[];
  }[] = [];

  messages.forEach((message) => {
    const messageDate = formatMessageDate(message.created_at);
    const lastGroup = groupedMessages[groupedMessages.length - 1];

    if (lastGroup && lastGroup.date === messageDate) {
      lastGroup.messages.push(message);
    } else {
      groupedMessages.push({
        date: messageDate,
        messages: [message],
      });
    }
  });

  return (
    <Card className={cn('flex flex-col h-full', className)}>
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg font-medium">
          Consultation Messages
        </CardTitle>
      </CardHeader>

      <ScrollArea className="flex-1 p-4">
        {loading && messages.length === 0 ? (
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-20 w-[300px] rounded-md" />
              </div>
            </div>
            <div className="flex items-start justify-end gap-3">
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px] ml-auto" />
                <Skeleton className="h-16 w-[270px] rounded-md" />
              </div>
              <Skeleton className="h-10 w-10 rounded-full" />
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {groupedMessages.map((group, groupIndex) => (
              <div key={groupIndex} className="space-y-4">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs">
                    <span className="bg-card px-2 text-muted-foreground">
                      {group.date}
                    </span>
                  </div>
                </div>

                {group.messages.map((message, messageIndex) => {
                  const isCurrentUser = user?.id === message.sender_id;
                  const showAvatar =
                    messageIndex === 0 ||
                    group.messages[messageIndex - 1].sender_id !==
                      message.sender_id;

                  return (
                    <div
                      key={message.id}
                      className={cn(
                        'flex items-start gap-3',
                        isCurrentUser && 'justify-end'
                      )}
                    >
                      {!isCurrentUser && showAvatar && (
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-muted flex-shrink-0">
                          {message.sender?.avatar_url ? (
                            <Image
                              src={message.sender.avatar_url}
                              alt={message.sender.full_name || 'User'}
                              width={40}
                              height={40}
                              className="object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary font-medium">
                              {(message.sender?.full_name || 'U').charAt(0)}
                            </div>
                          )}
                        </div>
                      )}

                      {!isCurrentUser && !showAvatar && (
                        <div className="w-10" />
                      )}

                      <div
                        className={cn(
                          'max-w-[80%] space-y-1',
                          isCurrentUser && 'items-end'
                        )}
                      >
                        {showAvatar && (
                          <div
                            className={cn(
                              'text-sm font-medium',
                              isCurrentUser && 'text-right'
                            )}
                          >
                            {isCurrentUser
                              ? 'You'
                              : message.sender?.full_name || 'User'}
                          </div>
                        )}

                        <div className="space-y-2">
                          {message.message && (
                            <div
                              className={cn(
                                'rounded-lg px-3 py-2 text-sm',
                                isCurrentUser
                                  ? 'bg-primary text-primary-foreground'
                                  : 'bg-muted'
                              )}
                            >
                              {message.message}
                            </div>
                          )}

                          {message.attachment_url && (
                            <div
                              className={cn(
                                'rounded-lg px-3 py-2 text-sm',
                                isCurrentUser
                                  ? 'bg-primary/90 text-primary-foreground'
                                  : 'bg-muted'
                              )}
                            >
                              <a
                                href={message.attachment_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center gap-2 hover:underline"
                              >
                                <FileText className="h-4 w-4" />
                                <span>
                                  {message.attachment_name || 'Attachment'}
                                </span>
                              </a>
                            </div>
                          )}

                          <div
                            className={cn(
                              'text-xs text-muted-foreground',
                              isCurrentUser && 'text-right'
                            )}
                          >
                            {formatMessageTime(message.created_at)}
                          </div>
                        </div>
                      </div>

                      {isCurrentUser && showAvatar && (
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-muted flex-shrink-0">
                          {profile?.avatar_url ? (
                            <Image
                              src={profile.avatar_url}
                              alt={profile.full_name || 'You'}
                              width={40}
                              height={40}
                              className="object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary font-medium">
                              {(profile?.full_name || 'Y').charAt(0)}
                            </div>
                          )}
                        </div>
                      )}

                      {isCurrentUser && !showAvatar && <div className="w-10" />}
                    </div>
                  );
                })}
              </div>
            ))}

            {messages.length === 0 && !loading && (
              <div className="text-center py-8 text-muted-foreground">
                No messages yet. Start the conversation!
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>

      {attachment && (
        <div className="px-4 py-2 border-t flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm">
            <FileText className="h-4 w-4" />
            <span className="truncate max-w-[200px]">{attachment.name}</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={removeAttachment}
            disabled={isUploading}
          >
            Remove
          </Button>
        </div>
      )}

      <div className="p-4 border-t mt-auto">
        <div className="flex gap-2">
          <Input
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={loading || isUploading}
            className="flex-1"
          />

          <Button
            variant="outline"
            size="icon"
            onClick={handleAttachmentClick}
            disabled={loading || isUploading}
            title="Attach file"
          >
            <PaperclipIcon className="h-4 w-4" />
          </Button>

          <Button
            onClick={handleSendMessage}
            disabled={
              (!newMessage.trim() && !attachment) || loading || isUploading
            }
          >
            <Send className="h-4 w-4" />
          </Button>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
          />
        </div>
      </div>
    </Card>
  );
}
