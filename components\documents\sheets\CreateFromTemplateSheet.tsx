'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet } from '@/components/ui/sheet';
import { useDocuments } from '@/lib/hooks';
import { FileText, BookTemplateIcon as TemplateIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface CreateFromTemplateSheetProps {
  templateId: string;
  templateTitle: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (documentId: string) => void;
}

export function CreateFromTemplateSheet({
  templateId,
  templateTitle,
  isOpen,
  onClose,
  onSuccess,
}: CreateFromTemplateSheetProps) {
  const { createFromTemplate } = useDocuments();

  const [newDocumentTitle, setNewDocumentTitle] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateFromTemplate = async () => {
    if (!newDocumentTitle.trim()) {
      toast.error('Missing information', {
        description: 'Please provide a title for the new document',
      });
      return;
    }

    setIsCreating(true);

    try {
      const newDocument = await createFromTemplate(templateId, {
        title: newDocumentTitle,
      });

      if (newDocument) {
        toast.success('Document created', {
          description: 'Your document has been created successfully',
        });

        if (onSuccess) {
          onSuccess(newDocument.id);
        }

        setNewDocumentTitle('');
        onClose();
      }
    } catch (error) {
      console.error('Error creating document from template:', error);
      toast.error('Creation failed', {
        description:
          error instanceof Error
            ? error.message
            : 'Failed to create document from template',
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <Sheet.Title className="flex items-center">
            <TemplateIcon className="mr-2 h-5 w-5" />
            Create from Template
          </Sheet.Title>
        </div>

        <div className="space-y-6 py-6">
          <div className="flex items-center gap-3 p-3 border rounded-md bg-muted/20">
            <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
              <FileText className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-medium">{templateTitle}</h3>
              <span className="text-sm text-muted-foreground block">
                Template
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="document-title">Document Title</Label>
            <Input
              id="document-title"
              placeholder="Enter a title for your new document"
              value={newDocumentTitle}
              onChange={(e) => setNewDocumentTitle(e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              This will be the title of your new document created from this
              template.
            </p>
          </div>
        </div>

        <div className="pt-4 border-t flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateFromTemplate}
            disabled={isCreating || !newDocumentTitle.trim()}
          >
            {isCreating ? 'Creating...' : 'Create Document'}
          </Button>
        </div>
      </div>
    </Sheet>
  );
}
