'use client';

import { useState } from 'react';
import { useSettings } from '@/lib/hooks';
import { UserSettings } from '@/lib/types/database-modules';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface GeneralSettingsProps {
  settings: UserSettings | null;
}

export function GeneralSettings({ settings }: GeneralSettingsProps) {
  const { loading, updateSettings } = useSettings();

  const [theme, setTheme] = useState<string>(settings?.theme || 'system');
  const [language, setLanguage] = useState<string>(settings?.language || 'en');
  const [documentView, setDocumentView] = useState<'card' | 'table'>(
    settings?.display_preferences?.document_view || 'card'
  );
  const [showPreviews, setShowPreviews] = useState<boolean>(
    settings?.display_preferences?.show_document_previews !== false
  );
  const [defaultExportFormat, setDefaultExportFormat] = useState<string>(
    settings?.export_preferences?.default_format || 'pdf'
  );
  const [includeMetadata, setIncludeMetadata] = useState<boolean>(
    settings?.export_preferences?.include_metadata !== false
  );
  const [includeSignatures, setIncludeSignatures] = useState<boolean>(
    settings?.export_preferences?.include_signatures !== false
  );

  const handleSave = async () => {
    if (!settings) return;

    const updatePromise = updateSettings({
      theme,
      language,
      display_preferences: {
        document_view: documentView,
        sidebar_collapsed:
          settings.display_preferences?.sidebar_collapsed || false,
        show_document_previews: showPreviews,
        fontSize: settings.display_preferences?.fontSize || 'medium',
        reduceMotion: settings.display_preferences?.reduceMotion || false,
        highContrast: settings.display_preferences?.highContrast || false,
      },
      export_preferences: {
        default_format: defaultExportFormat,
        include_metadata: includeMetadata,
        include_signatures: includeSignatures,
      },
    });

    toast.promise(updatePromise, {
      loading: 'Saving settings...',
      success: 'Settings saved successfully!',
      error: 'Failed to save settings',
    });

    try {
      await updatePromise;
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  if (!settings) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-5 w-10" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Appearance</CardTitle>
          <CardDescription>
            Customize how NotAMess Forms looks and feels
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="theme">Theme</Label>
            <Select value={theme} onValueChange={setTheme}>
              <SelectTrigger id="theme">
                <SelectValue placeholder="Select theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="system">System</SelectItem>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger id="language">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Display Preferences</CardTitle>
          <CardDescription>
            Customize how documents and content are displayed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="documentView">Default Document View</Label>
            <Select
              value={documentView}
              onValueChange={(value) =>
                setDocumentView(value as 'card' | 'table')
              }
            >
              <SelectTrigger id="documentView">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="card">Card View</SelectItem>
                <SelectItem value="table">Table View</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="showPreviews">Show Document Previews</Label>
            <Switch
              id="showPreviews"
              checked={showPreviews}
              onCheckedChange={setShowPreviews}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Export Settings</CardTitle>
          <CardDescription>
            Configure how documents are exported
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="exportFormat">Default Export Format</Label>
            <Select
              value={defaultExportFormat}
              onValueChange={setDefaultExportFormat}
            >
              <SelectTrigger id="exportFormat">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="docx">Word (DOCX)</SelectItem>
                <SelectItem value="txt">Plain Text</SelectItem>
                <SelectItem value="html">HTML</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="includeMetadata">Include Metadata</Label>
            <Switch
              id="includeMetadata"
              checked={includeMetadata}
              onCheckedChange={setIncludeMetadata}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="includeSignatures">Include Signatures</Label>
            <Switch
              id="includeSignatures"
              checked={includeSignatures}
              onCheckedChange={setIncludeSignatures}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button onClick={handleSave} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
