import { z } from 'zod';

// Field Validators
const fieldValidators = {
  name: z
    .string()
    .min(1, { message: 'Must be at least 1 character' })
    .max(50, { message: 'Must be at most 50 characters' }),
  address: z
    .string()
    .min(1, { message: 'Must be at least 1 character' })
    .max(70, { message: 'Must be at most 70 characters' }),
  email: z
    .string()
    .email({ message: 'Email must be a valid email' })
    .min(5, { message: 'Must be between 5 and 30 characters' })
    .max(30, { message: 'Must be between 5 and 30 characters' })
    .optional(),
  walletAddress: z.string().optional(),
  date: z.date(),
  string: z.string(),
  stringMin1: z.string().min(1, { message: 'Must be at least 1 character' }),
  stringOptional: z.string().optional(),
  nonNegativeNumber: z.coerce.number().nonnegative({
    message: 'Must be a positive number',
  }),
  positiveNumber: z.coerce.number().positive({
    message: 'Must be a positive number',
  }),
  uuid: z.string().uuid(),
  enumRole: z.enum(['discloser', 'recipient', 'third-party']),
  enumMethod: z.enum(['digital', 'wet-ink']),
  enumContractRole: z.enum(['client', 'contractor', 'vendor', 'partner']),
  enumDisputeResolution: z.enum(['arbitration', 'litigation', 'mediation']),
  enumCurrency: z.string().length(3),
  enumNetwork: z.enum(['ethereum', 'polygon', 'arbitrum']),
};

// Base Schemas
const PartySchema = z.object({
  name: fieldValidators.name,
  address: fieldValidators.address,
  role: fieldValidators.enumRole,
  email: fieldValidators.email,
  walletAddress: fieldValidators.walletAddress,
});

const SignatureSchema = z.object({
  partyId: fieldValidators.uuid,
  date: fieldValidators.date,
  method: fieldValidators.enumMethod,
});

// NDA Specific Schemas
const ConfidentialInfoSchema = z.object({
  definition: fieldValidators.stringMin1,
  examples: z.array(fieldValidators.string).optional(),
});

const NDATermSchema = z.object({
  durationYears: fieldValidators.positiveNumber,
  type: z.enum(['fixed', 'indefinite']),
  survivalPeriod: fieldValidators.positiveNumber.optional(),
});

const NDASchema = z.object({
  parties: z.array(PartySchema).min(2),
  confidentialInfo: ConfidentialInfoSchema,
  exclusions: z.array(fieldValidators.string).optional(),
  obligations: z.object({
    nonDisclosure: z.boolean().default(true),
    nonUse: z.boolean().default(true),
    otherTerms: fieldValidators.stringOptional,
  }),
  term: NDATermSchema,
  returnDestruction: z.object({
    required: z.boolean(),
    deadlineDays: fieldValidators.positiveNumber.optional(),
    instructions: fieldValidators.stringOptional,
  }),
  jurisdiction: z.object({
    governingLaw: fieldValidators.stringMin1,
    disputeResolution: fieldValidators.enumDisputeResolution,
  }),
  signatures: z.array(SignatureSchema).min(1),
});

// General Contract Schemas
const PaymentTermsSchema = z.object({
  type: z.enum(['fixed', 'hourly', 'milestone']),
  amount: fieldValidators.positiveNumber,
  currency: fieldValidators.enumCurrency,
  schedule: z.array(fieldValidators.date).optional(),
});

const GeneralContractSchema = z.object({
  parties: z.array(
    PartySchema.extend({
      role: fieldValidators.enumContractRole,
    })
  ),
  scope: z.object({
    title: fieldValidators.stringMin1,
    description: fieldValidators.stringMin1,
    deliverables: z.array(fieldValidators.string).optional(),
  }),
  paymentTerms: PaymentTermsSchema,
  termination: z.object({
    conditions: z.array(fieldValidators.string),
    noticePeriodDays: fieldValidators.positiveNumber,
    autoRenewal: z.boolean(),
  }),
  liability: z.object({
    capAmount: fieldValidators.positiveNumber.optional(),
    exclusions: z.array(fieldValidators.string).optional(),
  }),
  warranties: z.array(fieldValidators.string),
  disputeResolution: z.object({
    type: fieldValidators.enumDisputeResolution,
    jurisdiction: fieldValidators.stringMin1,
  }),
  boilerplate: z.record(z.boolean()).optional(),
});

// AI Suggestions Schema
const AISuggestionSchema = z.object({
  originalClause: fieldValidators.string,
  suggestion: fieldValidators.string,
  reason: fieldValidators.string,
  confidence: z.coerce.number().min(0).max(1),
});

// Blockchain Metadata Schema
const BlockchainMetadataSchema = z.object({
  contractAddress: fieldValidators.string,
  transactionHash: fieldValidators.string,
  blockTimestamp: fieldValidators.date,
  network: fieldValidators.enumNetwork,
});

// Full Document Schema
const LegalDocumentSchema = z.object({
  id: fieldValidators.uuid,
  type: z.enum(['nda', 'employment', 'service-agreement']),
  title: fieldValidators.string,
  content: z.union([NDASchema, GeneralContractSchema]),
  aiSuggestions: z.array(AISuggestionSchema).optional(),
  blockchain: BlockchainMetadataSchema.optional(),
  versions: z.array(z.any()).optional(),
  createdAt: fieldValidators.date,
  updatedAt: fieldValidators.date,
});

// Employment Schema
const EmploymentSchema = z.object({
  parties: z.array(
    PartySchema.extend({
      role: z.enum(['employer', 'employee']),
    })
  ),
  position: z.object({
    title: fieldValidators.stringMin1,
    description: fieldValidators.stringMin1,
    startDate: fieldValidators.date,
    endDate: fieldValidators.date.optional(),
  }),
  compensation: z.object({
    salary: fieldValidators.positiveNumber,
    currency: fieldValidators.enumCurrency,
    paymentSchedule: z.enum(['monthly', 'bi-weekly', 'weekly']),
  }),
  benefits: z.array(fieldValidators.string).optional(),
  termination: z.object({
    noticePeriodDays: fieldValidators.positiveNumber,
    severance: fieldValidators.positiveNumber.optional(),
  }),
  confidentiality: z.boolean().default(true),
  nonCompete: z
    .object({
      durationMonths: fieldValidators.positiveNumber.optional(),
      geographicScope: fieldValidators.stringOptional,
    })
    .optional(),
  disputeResolution: z.object({
    type: fieldValidators.enumDisputeResolution,
    jurisdiction: fieldValidators.stringMin1,
  }),
});

// Lease Schema
const LeaseSchema = z.object({
  parties: z.array(
    PartySchema.extend({
      role: z.enum(['landlord', 'tenant']),
    })
  ),
  property: z.object({
    address: fieldValidators.address,
    description: fieldValidators.stringMin1,
  }),
  term: z.object({
    startDate: fieldValidators.date,
    endDate: fieldValidators.date,
    renewalOption: z.boolean().default(false),
  }),
  rent: z.object({
    amount: fieldValidators.positiveNumber,
    currency: fieldValidators.enumCurrency,
    dueDate: fieldValidators.date,
    lateFee: fieldValidators.positiveNumber.optional(),
  }),
  securityDeposit: fieldValidators.positiveNumber.optional(),
  maintenanceResponsibilities: z.array(fieldValidators.string).optional(),
  termination: z.object({
    noticePeriodDays: fieldValidators.positiveNumber,
    earlyTerminationFee: fieldValidators.positiveNumber.optional(),
  }),
  disputeResolution: z.object({
    type: fieldValidators.enumDisputeResolution,
    jurisdiction: fieldValidators.stringMin1,
  }),
});

// Usage Example
export type NDADocument = z.infer<typeof NDASchema>;
export type GeneralContract = z.infer<typeof GeneralContractSchema>;
export type LegalDocument = z.infer<typeof LegalDocumentSchema>;
export type EmploymentDocument = z.infer<typeof EmploymentSchema>;
export type LeaseDocument = z.infer<typeof LeaseSchema>;

export const formSchemas = {
  NDADocument: NDASchema,
  EmploymentDocument: EmploymentSchema,
  LeaseDocument: LeaseSchema,
  GeneralContract: GeneralContractSchema,
  LegalDocument: LegalDocumentSchema,
  BlockchainMetadata: BlockchainMetadataSchema,
};
