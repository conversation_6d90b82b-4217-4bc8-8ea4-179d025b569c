'use client';
import {
  <PERSON>,
  <PERSON>evronRight,
  <PERSON><PERSON>ard,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export function MainSettingsComp({ username }: { username: string }) {
  const router = useRouter();

  const settingsSections = [
    {
      href: `/${username}/settings/account`,
      icon: User,
      label: 'Account',
      description: 'Manage your personal and billing information',
    },
    {
      href: `/${username}/settings/subscription`,
      icon: CreditCard,
      label: 'Subscription',
      description: 'View and update your subscription plan and usage',
    },
    {
      href: `/${username}/settings/security`,
      icon: ShieldCheck,
      label: 'Security',
      description: 'Configure security options and account protection',
    },
    {
      href: `/${username}/settings/notifications`,
      icon: Bell,
      label: 'Notifications',
      description: 'Customize your notification preferences',
    },
    {
      href: `/${username}/settings/preferences`,
      icon: Settings,
      label: 'Preferences',
      description: 'Set your display and export preferences',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {settingsSections.map((section) => (
          <Link
            key={section.href}
            href={section.href}
            className="group relative overflow-hidden rounded-lg border bg-background p-4 hover:shadow-md transition-all duration-200"
          >
            <div className="flex items-center gap-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                <section.icon className="h-5 w-5" />
              </div>
              <div className="space-y-0.5">
                <h3 className="font-medium">{section.label}</h3>
                <p className="line-clamp-2 text-sm text-muted-foreground">
                  {section.description}
                </p>
              </div>
              <ChevronRight className="absolute right-4 top-1/2 h-5 w-5 -translate-y-1/2 opacity-40 transition-transform group-hover:translate-x-1" />
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
