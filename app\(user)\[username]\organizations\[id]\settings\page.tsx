'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useOrganizations } from '@/lib/hooks';
import { Organization } from '@/lib/types/database-modules';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ArrowLeft,
  Building2,
  CreditCard,
  Loader2,
  Save,
  Trash,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const organizationFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  description: z.string().optional(),
  contact_email: z
    .string()
    .email({ message: 'Please enter a valid email address.' })
    .optional()
    .or(z.literal('')),
  subscription_level: z.enum(['basic', 'professional', 'enterprise']),
  primary_color: z.string().optional(),
  secondary_color: z.string().optional(),
});

type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

export default function OrganizationSettingsPage() {
  const { username, id } = useParams();
  const router = useRouter();
  const { getOrganization, updateOrganization, deleteOrganization } =
    useOrganizations();

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      name: '',
      description: '',
      contact_email: '',
      subscription_level: 'basic',
      primary_color: '',
      secondary_color: '',
    },
  });

  useEffect(() => {
    async function loadOrganization() {
      setLoading(true);
      try {
        const orgId = Array.isArray(id) ? id[0] : id;

        if (!orgId) {
          toast.error('Organization ID is missing');
          setLoading(false);
          return;
        }

        const org = await getOrganization(orgId);

        if (org) {
          setOrganization(org);

          // Set form values with type assertion for subscription_level
          form.reset({
            name: org.name,
            description: org.description || '',
            contact_email: org.contact_email || '',
            subscription_level:
              (org.subscription_level as
                | 'basic'
                | 'professional'
                | 'enterprise') || 'basic',
            primary_color: org.primary_color || '',
            secondary_color: org.secondary_color || '',
          });
        }
      } catch (error: any) {
        toast.error('Failed to load organization', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setLoading(false);
      }
    }

    loadOrganization();
  }, [id, getOrganization, form]);

  async function onSubmit(values: OrganizationFormValues) {
    if (!organization) return;

    setIsSaving(true);

    try {
      const success = await updateOrganization(organization.id, {
        name: values.name,
        description: values.description || null,
        contact_email: values.contact_email || null,
        subscription_level: values.subscription_level,
        primary_color: values.primary_color || null,
        secondary_color: values.secondary_color || null,
      });

      if (success) {
        toast.success('Organization updated successfully');
      }
    } catch (error: any) {
      toast.error('Failed to update organization', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsSaving(false);
    }
  }

  async function handleDeleteOrganization() {
    if (!organization) return;

    // Show confirmation dialog
    const confirmed = window.confirm(
      'Are you sure you want to delete this organization? This action cannot be undone.'
    );

    if (!confirmed) return;

    setIsDeleting(true);

    try {
      const success = await deleteOrganization(organization.id);

      if (success) {
        toast.success('Organization deleted successfully');
        router.push(`/${username}/organizations`);
      }
    } catch (error: any) {
      toast.error('Failed to delete organization', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsDeleting(false);
    }
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-2 mb-6">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="h-8 w-48 bg-muted rounded-md animate-pulse"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                <div className="h-4 w-48 bg-muted rounded-md animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="space-y-2">
                      <div className="h-4 w-24 bg-muted rounded-md animate-pulse"></div>
                      <div className="h-10 w-full bg-muted rounded-md animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                <div className="h-4 w-48 bg-muted rounded-md animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-24 w-full bg-muted rounded-md animate-pulse"></div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                <div className="h-4 w-48 bg-muted rounded-md animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="space-y-1">
                      <div className="h-4 w-20 bg-muted rounded-md animate-pulse"></div>
                      <div className="h-6 w-full bg-muted rounded-md animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-muted rounded-md animate-pulse"></div>
                <div className="h-4 w-48 bg-muted rounded-md animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="space-y-1">
                      <div className="h-4 w-20 bg-muted rounded-md animate-pulse"></div>
                      <div className="h-6 w-full bg-muted rounded-md animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link href={`/${username}/organizations`}>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Organization Not Found</h1>
        </div>

        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Building2 className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Organization Not Found</h3>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              The organization you're looking for doesn't exist or you don't
              have access to it.
            </p>
            <Link href={`/${username}/organizations`}>
              <Button>Go Back to Organizations</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link href={`/${username}/organizations/${organization.id}`}>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{organization.name} Settings</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
              <CardDescription>
                Update your organization's information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter organization name"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter organization description"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          Briefly describe your organization
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contact_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Email</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          Email address for organization communications
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="subscription_level"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subscription Level</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select subscription level" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="basic">Basic</SelectItem>
                            <SelectItem value="professional">
                              Professional
                            </SelectItem>
                            <SelectItem value="enterprise">
                              Enterprise
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Your organization's subscription tier
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="primary_color"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Color</FormLabel>
                          <FormControl>
                            <div className="flex gap-2">
                              <Input
                                type="color"
                                className="w-12 h-10 p-1"
                                {...field}
                                value={field.value || '#000000'}
                              />
                              <Input
                                type="text"
                                placeholder="#000000"
                                {...field}
                                value={field.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="secondary_color"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Secondary Color</FormLabel>
                          <FormControl>
                            <div className="flex gap-2">
                              <Input
                                type="color"
                                className="w-12 h-10 p-1"
                                {...field}
                                value={field.value || '#000000'}
                              />
                              <Input
                                type="text"
                                placeholder="#000000"
                                {...field}
                                value={field.value || ''}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>

          <Card className="border-destructive/20">
            <CardHeader className="text-destructive">
              <CardTitle>Danger Zone</CardTitle>
              <CardDescription>
                Irreversible actions for your organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border border-destructive/50 p-4">
                <h3 className="text-lg font-medium">Delete Organization</h3>
                <p className="text-sm text-muted-foreground mt-1 mb-4">
                  Once you delete your organization, all of your data will be
                  permanently removed. This action cannot be undone.
                </p>
                <Button
                  variant="default"
                  className="bg-red-500 hover:bg-red-600 text-white"
                  onClick={handleDeleteOrganization}
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash className="mr-2 h-4 w-4" />
                      Delete Organization
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Subscription</CardTitle>
              <CardDescription>Manage your subscription plan</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Current Plan</h3>
                  <p className="capitalize">
                    {organization.subscription_level}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Billing Cycle</h3>
                  <p>Monthly</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Next Billing Date</h3>
                  <p>Not available</p>
                </div>

                <Button className="w-full" variant="outline">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Manage Billing
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Organization ID</CardTitle>
              <CardDescription>Technical information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <h3 className="text-sm font-medium">ID</h3>
                  <p className="text-sm font-mono bg-muted p-2 rounded">
                    {organization.id}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Created</h3>
                  <p>{new Date(organization.created_at).toLocaleString()}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium">Last Updated</h3>
                  <p>{new Date(organization.updated_at).toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
