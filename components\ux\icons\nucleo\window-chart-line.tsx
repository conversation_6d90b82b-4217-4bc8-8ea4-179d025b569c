import { SVGProps } from 'react';

export function WindowChartLine(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g fill="currentColor">
        <polyline
          fill="none"
          points="4.75 12 6.5 10 7.5 11.25 9.5 7.75 11 10.25 13.25 5.75"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        />
        <rect
          height="12.5"
          width="14.5"
          fill="none"
          rx="2"
          ry="2"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          transform="translate(18 18) rotate(180)"
          x="1.75"
          y="2.75"
        />
        <circle cx="4.25" cy="5.25" fill="currentColor" r=".75" stroke="none" />
        <circle cx="6.75" cy="5.25" fill="currentColor" r=".75" stroke="none" />
      </g>
    </svg>
  );
}
