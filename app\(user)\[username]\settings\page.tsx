'use client';

import { AvatarUpload } from '@/components/settings/avatar-upload';
import { RoleSwitch } from '@/components/settings/role-switch';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useSettings } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Define types for billing history and payment methods
interface BillingHistory {
  id: string;
  date: string;
  description: string;
  amount: string;
  status?: 'paid' | 'pending' | 'failed';
}

interface PaymentMethod {
  id: string;
  type: string;
  last4: string;
  expiry: string;
  isDefault: boolean;
}

// Define types for billing history and payment methods
interface BillingHistory {
  id: string;
  date: string;
  description: string;
  amount: string;
  status?: 'paid' | 'pending' | 'failed';
}

interface PaymentMethod {
  id: string;
  type: string;
  last4: string;
  expiry: string;
  isDefault: boolean;
}

const workspaceFormSchema = z.object({
  workspaceName: z
    .string()
    .min(2, { message: 'Workspace name must be at least 2 characters.' }),
  description: z.string().optional(),
  customDomain: z.string().optional(),
});

const accountFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email.' }),
  phone: z.string(),
});

export default function GeneralSettingsPage() {
  const { user, profile, updateProfile } = userStore();
  const {
    settings,
    loading: settingsLoading,
    updateSettings,
    subscription,
  } = useSettings();

  // General settings state
  const [theme, setTheme] = useState<string>('system');
  const [language, setLanguage] = useState<string>('en');
  const [documentView, setDocumentView] = useState<'card' | 'table'>('card');
  const [showPreviews, setShowPreviews] = useState<boolean>(true);
  const [defaultExportFormat, setDefaultExportFormat] = useState<string>('pdf');
  const [includeMetadata, setIncludeMetadata] = useState<boolean>(true);
  const [includeSignatures, setIncludeSignatures] = useState<boolean>(true);

  // Initialize general settings when they load
  useEffect(() => {
    if (settings) {
      setTheme(settings.theme || 'system');
      setLanguage(settings.language || 'en');
      setDocumentView(settings.display_preferences?.document_view || 'card');
      setShowPreviews(
        settings.display_preferences?.show_document_previews !== false
      );
      setDefaultExportFormat(
        settings.export_preferences?.default_format || 'pdf'
      );
      setIncludeMetadata(
        settings.export_preferences?.include_metadata !== false
      );
      setIncludeSignatures(
        settings.export_preferences?.include_signatures !== false
      );
    }
  }, [settings]);

  // State for billing history and payment methods
  const [billingHistory, setBillingHistory] = useState<BillingHistory[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loadingBilling, setLoadingBilling] = useState(false);

  // Fetch billing history and payment methods
  useEffect(() => {
    const fetchBillingData = async () => {
      if (!user?.id) return;

      setLoadingBilling(true);
      try {
        // Use the subscription data from the component state

        if (subscription) {
          // Create billing history from subscription data
          const history: BillingHistory[] = [];

          // Add current subscription
          if (subscription.next_billing_date) {
            history.push({
              id: `current-${subscription.id}`,
              date: new Date(
                subscription.next_billing_date
              ).toLocaleDateString(),
              description: `${subscription.plan} Plan (Next billing)`,
              amount: `$${subscription.plan === 'Free' ? '0.00' : subscription.plan === 'Standard' ? '29.99' : '99.99'}`,
              status: 'pending',
            });
          }

          // Add past 3 months for demo purposes
          const today = new Date();
          for (let i = 1; i <= 3; i++) {
            const pastDate = new Date(today);
            pastDate.setMonth(today.getMonth() - i);

            history.push({
              id: `past-${i}`,
              date: pastDate.toLocaleDateString(),
              description: `${subscription.plan} Plan`,
              amount: `$${subscription.plan === 'Free' ? '0.00' : subscription.plan === 'Standard' ? '29.99' : '99.99'}`,
              status: 'paid',
            });
          }

          setBillingHistory(history);

          // Create a demo payment method
          setPaymentMethods([
            {
              id: 'default-card',
              type: 'VISA',
              last4: '4242',
              expiry:
                '12/' + (new Date().getFullYear() + 1).toString().slice(-2),
              isDefault: true,
            },
          ]);
        }
      } catch (error) {
        console.error('Error fetching billing data:', error);
        // Fallback to demo data if there's an error
        setBillingHistory([
          {
            id: 'demo-1',
            date: '2023-01-01',
            description: 'Monthly subscription',
            amount: '$29.99',
            status: 'paid',
          },
          {
            id: 'demo-2',
            date: '2022-12-01',
            description: 'Monthly subscription',
            amount: '$29.99',
            status: 'paid',
          },
        ]);

        setPaymentMethods([
          {
            id: 'demo-card',
            type: 'VISA',
            last4: '4242',
            expiry: '04/25',
            isDefault: true,
          },
        ]);
      } finally {
        setLoadingBilling(false);
      }
    };

    fetchBillingData();
  }, [user?.id, subscription]);

  const workspaceForm = useForm<z.infer<typeof workspaceFormSchema>>({
    resolver: zodResolver(workspaceFormSchema),
    defaultValues: {
      workspaceName: 'NotAMess Labs',
      description: 'Smart contract and legal document management',
      customDomain: '',
    },
  });

  const accountForm = useForm<z.infer<typeof accountFormSchema>>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      name: profile?.full_name || '',
      email: user?.email || '',
      phone: profile?.phone_number || '',
    },
  });

  const onWorkspaceSubmit = async (
    values: z.infer<typeof workspaceFormSchema>
  ) => {
    try {
      // In a real implementation, this would call an API
      console.log('Updating workspace settings:', values);

      toast.success('Workspace settings updated successfully');
    } catch (error) {
      toast.error('Failed to update workspace settings');
      console.error(error);
    }
  };

  // We'll use the userStore's updateProfile function instead of useProfile hook

  const onAccountSubmit = async (values: z.infer<typeof accountFormSchema>) => {
    if (!user?.id) {
      toast.error('You must be logged in to update your profile');
      return;
    }

    try {
      // Create a new profile object with updated values
      const updatedProfile = profile
        ? {
            ...profile,
            full_name: values.name,
            phone_number: values.phone,
            updated_at: new Date().toISOString(),
          }
        : null;

      // Create a promise for the profile update
      const updateProfilePromise = Promise.resolve().then(() => {
        updateProfile(updatedProfile);
        return updatedProfile;
      });

      // Use toast.promise for better user feedback
      toast.promise(updateProfilePromise, {
        loading: 'Updating profile...',
        success: 'Profile updated successfully',
        error: 'Failed to update profile',
      });

      // Wait for the profile update to complete
      await updateProfilePromise;

      // If email has changed, update it in auth
      if (values.email !== user.email) {
        // Since we don't have direct access to auth update, we'll just show a toast
        toast.info('Email update functionality requires auth access', {
          description:
            'In a real implementation, this would update your email address',
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Please try again later';
      toast.error('Failed to update profile', {
        description: errorMessage,
      });
    }
  };

  // Save general settings
  const handleSaveGeneralSettings = async () => {
    if (!settings) return;

    try {
      const updatePromise = updateSettings({
        theme,
        language,
        display_preferences: {
          document_view: documentView,
          sidebar_collapsed:
            settings.display_preferences?.sidebar_collapsed || false,
          show_document_previews: showPreviews,
        },
        export_preferences: {
          default_format: defaultExportFormat,
          include_metadata: includeMetadata,
          include_signatures: includeSignatures,
        },
      });

      // Use toast.promise for better user feedback
      toast.promise(updatePromise, {
        loading: 'Saving general settings...',
        success: 'General settings saved successfully',
        error: 'Failed to save general settings',
      });

      await updatePromise;
    } catch (error) {
      console.error('Error saving general settings:', error);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">General Settings</h1>
        <p className="text-muted-foreground">
          Manage your account and workspace settings
        </p>
      </div>

      {/* Avatar Upload */}
      <AvatarUpload />

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Update your personal details</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...accountForm}>
            <form
              onSubmit={accountForm.handleSubmit(onAccountSubmit)}
              className="space-y-4"
            >
              <FormField
                control={accountForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={accountForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormDescription>
                      This email is also used for notifications
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={accountForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="(*************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => accountForm.reset()}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Account Type */}
      <RoleSwitch />

      {/* Workspace Information */}
      <Card>
        <CardHeader>
          <CardTitle>Workspace Information</CardTitle>
          <CardDescription>Update your workspace details</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...workspaceForm}>
            <form
              onSubmit={workspaceForm.handleSubmit(onWorkspaceSubmit)}
              className="space-y-4"
            >
              <FormField
                control={workspaceForm.control}
                name="workspaceName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Workspace Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My Workspace" {...field} />
                    </FormControl>
                    <FormDescription>
                      This is the name of your workspace
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={workspaceForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your workspace"
                        {...field}
                        className="resize-none"
                        rows={3}
                      />
                    </FormControl>
                    <FormDescription>
                      A brief description of your workspace
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={workspaceForm.control}
                name="customDomain"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Domain</FormLabel>
                    <FormControl>
                      <Input placeholder="workspace.example.com" {...field} />
                    </FormControl>
                    <FormDescription>
                      Set a custom domain for your workspace
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-2">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => workspaceForm.reset()}
                >
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>View your recent billing history</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingBilling ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : billingHistory.length > 0 ? (
            <Table>
              <TableCaption>Your recent billing history</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {billingHistory.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.date}</TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell>
                      {item.status && (
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                            item.status === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : item.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {item.status.charAt(0).toUpperCase() +
                            item.status.slice(1)}
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">{item.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No billing history available
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Manage your payment methods</CardDescription>
        </CardHeader>
        <CardContent>
          {loadingBilling ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : paymentMethods.length > 0 ? (
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="flex items-center justify-between rounded-lg border p-4"
                >
                  <div className="flex items-center space-x-4">
                    <div className="font-medium">
                      {method.type} •••• {method.last4}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Expires {method.expiry}
                    </div>
                    {method.isDefault && (
                      <div className="rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-semibold text-primary">
                        Default
                      </div>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      toast.info('Edit payment method', {
                        description:
                          'This functionality is not implemented yet',
                      })
                    }
                  >
                    Edit
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No payment methods available
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={() =>
              toast.info('Add payment method', {
                description: 'This functionality is not implemented yet',
              })
            }
          >
            Add Payment Method
          </Button>
        </CardFooter>
      </Card>

      {/* General Settings - Appearance */}
      <Card>
        <CardHeader>
          <CardTitle>Appearance</CardTitle>
          <CardDescription>
            Customize how NotAMess Forms looks and feels
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="theme">Theme</Label>
            <Select value={theme} onValueChange={setTheme}>
              <SelectTrigger id="theme">
                <SelectValue placeholder="Select theme" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="system">System</SelectItem>
                <SelectItem value="light">Light</SelectItem>
                <SelectItem value="dark">Dark</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger id="language">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* General Settings - Display Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Display Preferences</CardTitle>
          <CardDescription>
            Customize how documents and content are displayed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="documentView">Default Document View</Label>
            <Select
              value={documentView}
              onValueChange={(value) =>
                setDocumentView(value as 'card' | 'table')
              }
            >
              <SelectTrigger id="documentView">
                <SelectValue placeholder="Select view" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="card">Card View</SelectItem>
                <SelectItem value="table">Table View</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="showPreviews">Show Document Previews</Label>
            <Switch
              id="showPreviews"
              checked={showPreviews}
              onCheckedChange={setShowPreviews}
            />
          </div>
        </CardContent>
      </Card>

      {/* General Settings - Export Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Export Settings</CardTitle>
          <CardDescription>
            Configure how documents are exported
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="exportFormat">Default Export Format</Label>
            <Select
              value={defaultExportFormat}
              onValueChange={setDefaultExportFormat}
            >
              <SelectTrigger id="exportFormat">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="docx">Word (DOCX)</SelectItem>
                <SelectItem value="txt">Plain Text</SelectItem>
                <SelectItem value="html">HTML</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="includeMetadata">Include Metadata</Label>
            <Switch
              id="includeMetadata"
              checked={includeMetadata}
              onCheckedChange={setIncludeMetadata}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="includeSignatures">Include Signatures</Label>
            <Switch
              id="includeSignatures"
              checked={includeSignatures}
              onCheckedChange={setIncludeSignatures}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button
            onClick={handleSaveGeneralSettings}
            disabled={settingsLoading}
          >
            {settingsLoading && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Save General Settings
          </Button>
        </CardFooter>
      </Card>

      {/* Danger Zone */}
      <Card className="border-destructive/20">
        <CardHeader className="text-destructive">
          <CardTitle>Danger Zone</CardTitle>
          <CardDescription>
            Irreversible actions for your workspace
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="rounded-lg border border-destructive/50 p-4">
              <h3 className="text-lg font-medium">Delete Workspace</h3>
              <p className="text-sm text-muted-foreground">
                Once you delete your workspace, all of your data will be
                permanently removed. This action cannot be undone.
              </p>
              <Button
                variant="shadow_red"
                className="mt-4"
                onClick={() =>
                  toast.error('This functionality is disabled in the demo')
                }
              >
                Delete Workspace
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
