-- Enable the realtime extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "pg_net";

-- Create a policy to allow authenticated users to receive broadcasts
CREATE POLICY "Authenticated users can receive broadcasts"
ON "realtime"."messages"
FOR SELECT
TO authenticated
USING ( true );

-- Create a function to broadcast document changes
CREATE OR REPLACE FUNCTION public.broadcast_document_changes()
<PERSON><PERSON><PERSON><PERSON> trigger
AS $$
BEGIN
  PERFORM realtime.broadcast_changes(
    'document:' || new.id::text, -- topic
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    CASE 
      WHEN TG_OP = 'DELETE' THEN old
      ELSE new
    END, -- record
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to broadcast notification changes
CREATE OR REPLACE FUNCTION public.broadcast_notification_changes()
<PERSON><PERSON><PERSON><PERSON> trigger
AS $$
BEGIN
  PERFORM realtime.broadcast_changes(
    'notifications:' || new.user_id::text, -- topic
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'user_id', new.user_id,
      'title', new.title,
      'content', new.content,
      'type', new.type,
      'read', new.read,
      'action_url', new.action_url,
      'related_id', new.related_id,
      'related_type', new.related_type,
      'created_at', new.created_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to broadcast template changes
CREATE OR REPLACE FUNCTION public.broadcast_template_changes()
RETURNS trigger
AS $$
BEGIN
  PERFORM realtime.broadcast_changes(
    'templates', -- topic
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'title', new.title,
      'description', new.description,
      'document_type', new.document_type,
      'is_global', new.is_global,
      'category', new.category,
      'created_at', new.created_at,
      'updated_at', new.updated_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to broadcast booking changes
CREATE OR REPLACE FUNCTION public.broadcast_booking_changes()
RETURNS trigger
AS $$
BEGIN
  -- Broadcast to the client
  PERFORM realtime.broadcast_changes(
    'bookings:client:' || new.user_id::text, -- topic for client
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'lawyer_id', new.lawyer_id,
      'user_id', new.user_id,
      'document_id', new.document_id,
      'status', new.status,
      'consultation_date', new.consultation_date,
      'duration_minutes', new.duration_minutes,
      'created_at', new.created_at,
      'updated_at', new.updated_at
    ), -- record (only sending necessary fields)
    CASE
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );

  -- Broadcast to the lawyer
  PERFORM realtime.broadcast_changes(
    'bookings:lawyer:' || new.lawyer_id::text, -- topic for lawyer
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'lawyer_id', new.lawyer_id,
      'user_id', new.user_id,
      'document_id', new.document_id,
      'status', new.status,
      'consultation_date', new.consultation_date,
      'duration_minutes', new.duration_minutes,
      'created_at', new.created_at,
      'updated_at', new.updated_at
    ), -- record (only sending necessary fields)
    CASE
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to broadcast document sharing changes
CREATE OR REPLACE FUNCTION public.broadcast_document_sharing_changes()
RETURNS trigger
AS $$
BEGIN
  -- Broadcast to the document owner
  PERFORM realtime.broadcast_changes(
    'document_shares:owner:' || (SELECT owner_id FROM documents WHERE id = new.document_id)::text,
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'document_id', new.document_id,
      'user_id', new.user_id,
      'can_edit', new.can_edit,
      'created_at', new.created_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  
  -- Broadcast to the user the document is shared with
  PERFORM realtime.broadcast_changes(
    'document_shares:user:' || new.user_id::text,
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    json_build_object(
      'id', new.id,
      'document_id', new.document_id,
      'user_id', new.user_id,
      'can_edit', new.can_edit,
      'created_at', new.created_at
    ), -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a function to broadcast collaboration changes
CREATE OR REPLACE FUNCTION public.broadcast_collaboration_changes()
RETURNS trigger
AS $$
BEGIN
  PERFORM realtime.broadcast_changes(
    'collaboration:project:' || new.project_id::text,
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    new, -- record
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Set up triggers for documents table
DROP TRIGGER IF EXISTS on_document_changes ON public.documents;
CREATE TRIGGER on_document_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.documents
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_document_changes();

-- Set up triggers for notifications table
DROP TRIGGER IF EXISTS on_notification_changes ON public.notifications;
CREATE TRIGGER on_notification_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.notifications
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_notification_changes();

-- Set up triggers for templates table
DROP TRIGGER IF EXISTS on_template_changes ON public.templates;
CREATE TRIGGER on_template_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.templates
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_template_changes();

-- Set up triggers for lawyer_consultations table
DROP TRIGGER IF EXISTS on_booking_changes ON public.lawyer_consultations;
CREATE TRIGGER on_booking_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.lawyer_consultations
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_booking_changes();

-- Set up triggers for document_shares table
DROP TRIGGER IF EXISTS on_document_sharing_changes ON public.document_shares;
CREATE TRIGGER on_document_sharing_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.document_shares
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_document_sharing_changes();

-- Set up triggers for project_messages table
DROP TRIGGER IF EXISTS on_project_message_changes ON public.project_messages;
CREATE TRIGGER on_project_message_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.project_messages
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_collaboration_changes();

-- Set up triggers for project_tasks table
DROP TRIGGER IF EXISTS on_project_task_changes ON public.project_tasks;
CREATE TRIGGER on_project_task_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.project_tasks
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_collaboration_changes();

-- Set up triggers for project_members table
DROP TRIGGER IF EXISTS on_project_member_changes ON public.project_members;
CREATE TRIGGER on_project_member_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.project_members
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_collaboration_changes();
