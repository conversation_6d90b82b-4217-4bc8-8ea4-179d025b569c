'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useLawyers } from '@/lib/hooks';
import { LawyerConsultation } from '@/lib/types/database-modules';
import { format } from 'date-fns';
import { Calendar, Clock, FileText, Video } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface LawyerConsultationsListProps {
  consultations?: LawyerConsultation[];
  limit?: number;
  emptyMessage?: string;
  lawyerId?: string;
}

export function LawyerConsultationsList({
  consultations: initialConsultations,
  limit,
  emptyMessage = 'No consultations found',
  lawyerId,
}: LawyerConsultationsListProps) {
  const { updateConsultation, fetchLawyerConsultations } = useLawyers();
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [consultations, setConsultations] = useState<LawyerConsultation[]>(
    initialConsultations || []
  );
  const [loading, setLoading] = useState(!initialConsultations);

  // Fetch consultations if not provided
  useEffect(() => {
    if (!initialConsultations && lawyerId) {
      const fetchConsultations = async () => {
        setLoading(true);
        try {
          const fetchedConsultations = await fetchLawyerConsultations(lawyerId);
          setConsultations(fetchedConsultations || []);
        } catch (error) {
          console.error('Error fetching consultations:', error);
          toast.error('Failed to load consultations');
        } finally {
          setLoading(false);
        }
      };

      fetchConsultations();
    } else if (initialConsultations) {
      setConsultations(initialConsultations);
    }
  }, [initialConsultations, lawyerId, fetchLawyerConsultations]);

  const displayConsultations = limit
    ? consultations.slice(0, limit)
    : consultations;

  const handleStatusUpdate = async (
    consultationId: string,
    newStatus: 'completed' | 'cancelled'
  ) => {
    setUpdatingId(consultationId);
    try {
      // Create a promise for updating the consultation status
      const updatePromise = async () => {
        return await updateConsultation(consultationId, newStatus);
      };

      // Use toast.promise for better user feedback
      toast.promise(updatePromise(), {
        loading: 'Updating consultation status...',
        success:
          newStatus === 'completed'
            ? 'Consultation marked as completed'
            : 'Consultation cancelled',
        error: (err) =>
          `Failed to update consultation status: ${err.message || 'Unknown error'}`,
      });

      // Execute the promise
      const result = await updatePromise();

      // Update local state if needed
      if (result) {
        setConsultations((prev) =>
          prev.map((consultation) =>
            consultation.id === consultationId
              ? { ...consultation, status: newStatus }
              : consultation
          )
        );
      }
    } catch (error) {
      console.error('Error updating consultation status:', error);
      // Error is already handled by toast.promise
    } finally {
      setUpdatingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Scheduled
          </Badge>
        );
      case 'completed':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            Completed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200"
          >
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="bg-gray-50 text-gray-700 border-gray-200"
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="text-center py-12 border rounded-lg bg-muted/20">
        <div className="animate-spin mx-auto h-12 w-12 text-muted-foreground mb-3">
          <Clock className="h-12 w-12" />
        </div>
        <h4 className="text-lg font-medium mb-1">Loading consultations...</h4>
        <p className="text-muted-foreground">
          Please wait while we fetch your consultations.
        </p>
      </div>
    );
  }

  // If no consultations, show empty message
  if (displayConsultations.length === 0) {
    return (
      <div className="text-center py-12 border rounded-lg bg-muted/20">
        <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-3" />
        <h4 className="text-lg font-medium mb-1">{emptyMessage}</h4>
        <p className="text-muted-foreground">
          When you have consultations, they will appear here.
        </p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Client</TableHead>
          <TableHead>Date & Time</TableHead>
          <TableHead>Duration</TableHead>
          <TableHead>Document</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {displayConsultations.map((consultation) => {
          const consultationDate = consultation.consultation_date
            ? new Date(consultation.consultation_date)
            : null;

          return (
            <TableRow key={consultation.id}>
              <TableCell className="font-medium">
                {consultation.user_id
                  ? 'Client #' + consultation.user_id.substring(0, 8)
                  : 'Unknown Client'}
              </TableCell>
              <TableCell>
                {consultationDate ? (
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>{format(consultationDate, 'MMM d, yyyy')}</span>
                    </div>
                    <div className="flex items-center gap-1 mt-1">
                      <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                      <span>{format(consultationDate, 'h:mm a')}</span>
                    </div>
                  </div>
                ) : (
                  'Not scheduled'
                )}
              </TableCell>
              <TableCell>{consultation.duration_minutes} minutes</TableCell>
              <TableCell>
                {consultation.document_id ? (
                  <Link
                    href={`/documents/${consultation.document_id}`}
                    className="flex items-center gap-1 text-primary hover:underline"
                  >
                    <FileText className="h-3.5 w-3.5" />
                    <span>View Document</span>
                  </Link>
                ) : (
                  <span className="text-muted-foreground">No document</span>
                )}
              </TableCell>
              <TableCell>{getStatusBadge(consultation.status)}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  {consultation.status === 'scheduled' && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 gap-1"
                        disabled={updatingId === consultation.id}
                      >
                        <Video className="h-3.5 w-3.5" />
                        <span>Join</span>
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8"
                        onClick={() =>
                          handleStatusUpdate(consultation.id, 'completed')
                        }
                        disabled={updatingId === consultation.id}
                      >
                        Complete
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 text-red-600 hover:text-red-700"
                        onClick={() =>
                          handleStatusUpdate(consultation.id, 'cancelled')
                        }
                        disabled={updatingId === consultation.id}
                      >
                        Cancel
                      </Button>
                    </>
                  )}
                  {consultation.status === 'completed' && (
                    <Button size="sm" variant="outline" className="h-8">
                      View Notes
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}
