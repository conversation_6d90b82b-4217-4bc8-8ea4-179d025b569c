'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { RatingDisplay } from '@/components/ui/rating';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useLawyers } from '@/lib/hooks';
import { avatar_male_001 } from '@/lib/imgs';
import {
  CheckCircle2,
  Filter,
  Gavel,
  MessageSquare,
  Search,
  UserX,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function LawyerPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('all');

  const { loading, lawyers, fetchAllLawyers, fetchConsultations } =
    useLawyers();

  useEffect(() => {
    fetchAllLawyers().catch((error: Error) => {
      console.error('Error fetching lawyers:', error);
      // Don't show error to user, just log it
    });

    fetchConsultations().catch((error: Error) => {
      console.error('Error fetching consultations:', error);
      // Don't show error to user, just log it
    });
  }, [fetchAllLawyers, fetchConsultations]);

  // We no longer redirect lawyer users to their dashboard
  // They can access both the standard lawyer page and the lawyer dashboard

  // Filter lawyers based on search query and specialization
  const filteredLawyers = lawyers.filter((lawyer) => {
    const matchesSearch = searchQuery
      ? lawyer.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lawyer.specialization.some((spec: string) =>
          spec.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : true;

    const matchesSpecialization =
      selectedSpecialization === 'all'
        ? true
        : lawyer.specialization.some(
            (spec: string) =>
              spec.toLowerCase() === selectedSpecialization.toLowerCase()
          );

    return matchesSearch && matchesSpecialization;
  });

  return (
    <div className="p-6">
      <section className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Lawyer Support</h1>
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-blue-800 mb-2">
                  Get Expert Legal Advice
                </h2>
                <p className="text-blue-700 mb-4">
                  Connect with specialized attorneys for your legal needs. From
                  contract reviews to complex legal matters.
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge
                    variant="outline"
                    className="bg-white border-blue-200 text-blue-700"
                  >
                    Contract Law
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-white border-blue-200 text-blue-700"
                  >
                    Intellectual Property
                  </Badge>
                  <Badge
                    variant="outline"
                    className="bg-white border-blue-200 text-blue-700"
                  >
                    Corporate Law
                  </Badge>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button
                    className="bg-blue-600 hover:bg-blue-700"
                    onClick={() => {
                      const element = document.getElementById(
                        'find-lawyer-section'
                      );
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                  >
                    Find a Lawyer
                  </Button>
                  <Button
                    variant="outline"
                    className="border-blue-200 text-blue-700"
                    onClick={() => router.push('/iamjulius/lawyer/book')}
                  >
                    Book Consultation
                  </Button>
                </div>
              </div>
              <div className="w-full md:w-1/3 flex justify-center items-center">
                <div className="bg-white p-4 rounded-xl shadow-sm">
                  <Gavel className="size-24 text-blue-500 mx-auto" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      <section id="find-lawyer-section">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Find a Lawyer</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="gap-1">
              <Filter className="size-3.5" />
              <span>Filter</span>
            </Button>
          </div>
        </div>

        <div className="mb-4 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 size-4" />
          <Input
            placeholder="Search by name, specialization, or expertise..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Tabs defaultValue="all" onValueChange={setSelectedSpecialization}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Specializations</TabsTrigger>
            <TabsTrigger value="Contract Law">Contract Law</TabsTrigger>
            <TabsTrigger value="Corporate Law">Corporate Law</TabsTrigger>
            <TabsTrigger value="Intellectual Property">
              Intellectual Property
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 1. Error state takes precedence (if we had an error state) */}

              {/* 2. Loading state - always show when loading is true */}
              {loading ? (
                <div className="col-span-2">
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-6">
                          <div className="flex gap-4">
                            <div className="h-16 w-16 rounded-full bg-gray-200"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                              <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                              <div className="h-3 w-2/3 bg-gray-200 rounded"></div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : filteredLawyers.length === 0 ? (
                /* 3. Empty state - no lawyers at all */
                <div className="col-span-2 text-center py-12">
                  <UserX className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No lawyers found</h3>
                  <p className="mt-2 text-muted-foreground">
                    Try adjusting your search criteria
                  </p>
                  {searchQuery && (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => setSearchQuery('')}
                    >
                      Clear search
                    </Button>
                  )}
                </div>
              ) : (
                /* 4. Content state - show lawyers */
                filteredLawyers.map((lawyer) => (
                  <Card
                    key={lawyer.id}
                    className={
                      lawyer.average_rating && lawyer.average_rating >= 4.8
                        ? 'border-blue-200'
                        : ''
                    }
                  >
                    <CardContent className="p-5">
                      <div className="flex items-start gap-4">
                        <Image
                          alt="profile"
                          src={
                            lawyer.avatar_url ||
                            lawyer.profile_image_url ||
                            avatar_male_001
                          }
                          width={32}
                          height={32}
                          className="size-8 rounded-full"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-1">
                            <h3 className="font-semibold">
                              {lawyer.full_name}
                            </h3>
                            {lawyer.is_verified && (
                              <CheckCircle2 className="size-4 text-blue-500" />
                            )}
                            {lawyer.average_rating &&
                              lawyer.average_rating >= 4.8 && (
                                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                                  Featured
                                </Badge>
                              )}
                          </div>
                          <p className="text-sm text-neutral-500 mb-1">
                            {lawyer.specialization.join(', ')} •{' '}
                            {lawyer.years_experience} years experience
                          </p>
                          <div className="mb-2">
                            <RatingDisplay
                              rating={lawyer.average_rating || 0}
                              showCount={true}
                              size="sm"
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-green-600">
                                Available for consultation
                              </span>
                              <span className="text-sm font-medium">
                                ${lawyer.hourly_rate}/hr
                              </span>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="gap-1"
                                onClick={() => {
                                  // This would be implemented in a real app
                                  alert('Messaging feature coming soon!');
                                }}
                              >
                                <MessageSquare className="size-3.5" />
                                <span>Message</span>
                              </Button>
                              <Button
                                size="sm"
                                onClick={() =>
                                  router.push(
                                    `/iamjulius/lawyer/book?lawyer_id=${lawyer.id}`
                                  )
                                }
                              >
                                Book
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          {/* We'll use the same content for all tabs, as our filtering is handled in the component */}
          {['Contract Law', 'Corporate Law', 'Intellectual Property'].map(
            (specialization) => (
              <TabsContent
                key={specialization}
                value={specialization}
                className="mt-0"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 1. Error state takes precedence (if we had an error state) */}

                  {/* 2. Loading state - always show when loading is true */}
                  {loading ? (
                    <div className="col-span-2">
                      <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                          <Card key={i} className="animate-pulse">
                            <CardContent className="p-6">
                              <div className="flex gap-4">
                                <div className="h-16 w-16 rounded-full bg-gray-200"></div>
                                <div className="flex-1 space-y-2">
                                  <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                                  <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                                  <div className="h-3 w-2/3 bg-gray-200 rounded"></div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ) : filteredLawyers.length === 0 ? (
                    /* 3. Empty state - no lawyers at all */
                    <div className="col-span-2 text-center py-12">
                      <UserX className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">
                        No lawyers found
                      </h3>
                      <p className="mt-2 text-muted-foreground">
                        Try adjusting your search criteria
                      </p>
                      {searchQuery && (
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => setSearchQuery('')}
                        >
                          Clear search
                        </Button>
                      )}
                    </div>
                  ) : (
                    /* 4. Content state - show lawyers */
                    filteredLawyers.map((lawyer) => (
                      <Card
                        key={lawyer.id}
                        className={
                          lawyer.average_rating && lawyer.average_rating >= 4.8
                            ? 'border-blue-200'
                            : ''
                        }
                      >
                        <CardContent className="p-5">
                          <div className="flex items-start gap-4">
                            <Image
                              alt="profile"
                              src={
                                lawyer.avatar_url ||
                                lawyer.profile_image_url ||
                                avatar_male_001
                              }
                              width={32}
                              height={32}
                              className="size-8 rounded-full"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-1">
                                <h3 className="font-semibold">
                                  {lawyer.full_name}
                                </h3>
                                {lawyer.is_verified && (
                                  <CheckCircle2 className="size-4 text-blue-500" />
                                )}
                                {lawyer.average_rating &&
                                  lawyer.average_rating >= 4.8 && (
                                    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                                      Featured
                                    </Badge>
                                  )}
                              </div>
                              <p className="text-sm text-neutral-500 mb-1">
                                {lawyer.specialization.join(', ')} •{' '}
                                {lawyer.years_experience} years experience
                              </p>
                              <div className="mb-2">
                                <RatingDisplay
                                  rating={lawyer.average_rating || 0}
                                  showCount={true}
                                  size="sm"
                                />
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <span className="text-sm text-green-600">
                                    Available for consultation
                                  </span>
                                  <span className="text-sm font-medium">
                                    ${lawyer.hourly_rate}/hr
                                  </span>
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="gap-1"
                                  >
                                    <MessageSquare className="size-3.5" />
                                    <span>Message</span>
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() =>
                                      router.push(
                                        `/iamjulius/lawyer/book?lawyer_id=${lawyer.id}`
                                      )
                                    }
                                  >
                                    Book
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </TabsContent>
            )
          )}
        </Tabs>
      </section>
    </div>
  );
}
