'use client';

import { DocumentAnalytics } from '@/components/documents';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Download } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

export default function DocumentAnalyticsPage() {
  const { username } = useParams();
  const router = useRouter();
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('week');

  const handleExportAnalytics = () => {
    // In a real implementation, this would generate and download an analytics report
    alert('Analytics export functionality would be implemented here');
  };

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`/${username}/documents`)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Document Analytics</h1>
        </div>

        <Button variant="outline" onClick={handleExportAnalytics}>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      <DocumentAnalytics timeRange={timeRange} />
    </div>
  );
}
