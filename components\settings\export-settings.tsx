'use client';

import { useState } from 'react';
import { useSettings } from '@/lib/hooks';
import { UserSettings } from '@/lib/types/database-modules';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ExportSettingsProps {
  initialSettings: UserSettings | null;
}

export function ExportSettings({ initialSettings }: ExportSettingsProps) {
  const { updateExportSettings } = useSettings();
  const [isSaving, setIsSaving] = useState(false);

  // State for export settings
  const [defaultFormat, setDefaultFormat] = useState<string>(
    initialSettings?.export_preferences?.default_format ?? 'pdf'
  );

  const [includeMetadata, setIncludeMetadata] = useState<boolean>(
    initialSettings?.export_preferences?.include_metadata ?? true
  );

  const [includeSignatures, setIncludeSignatures] = useState<boolean>(
    initialSettings?.export_preferences?.include_signatures ?? true
  );

  // Handle saving export settings
  const saveExportSettings = async () => {
    setIsSaving(true);

    const updatePromise = updateExportSettings({
      export_preferences: {
        default_format: defaultFormat,
        include_metadata: includeMetadata,
        include_signatures: includeSignatures,
      },
    });

    toast.promise(updatePromise, {
      loading: 'Saving export settings...',
      success: 'Export settings saved successfully!',
      error: 'Failed to save export settings',
    });

    try {
      await updatePromise;
    } catch (error) {
      console.error('Error saving export settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // If no settings are provided, show skeleton
  if (!initialSettings) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Settings</CardTitle>
        <CardDescription>Configure how documents are exported</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="default-format">Default Export Format</Label>
          <Select value={defaultFormat} onValueChange={setDefaultFormat}>
            <SelectTrigger id="default-format">
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">PDF</SelectItem>
              <SelectItem value="docx">Word (DOCX)</SelectItem>
              <SelectItem value="txt">Plain Text (TXT)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="include-metadata">Include Metadata</Label>
            <p className="text-sm text-muted-foreground">
              Add document metadata to exported files
            </p>
          </div>
          <Switch
            id="include-metadata"
            checked={includeMetadata}
            onCheckedChange={setIncludeMetadata}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="include-signatures">Include Signatures</Label>
            <p className="text-sm text-muted-foreground">
              Include signature images in exported documents
            </p>
          </div>
          <Switch
            id="include-signatures"
            checked={includeSignatures}
            onCheckedChange={setIncludeSignatures}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={saveExportSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
