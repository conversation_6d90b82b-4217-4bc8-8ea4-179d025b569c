import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useOrganizations, useTeams } from '@/lib/hooks';
import { OrganizationWithDetails, Team } from '@/lib/types/database-modules';
import {
  Bar<PERSON>hart,
  Building2,
  FileText,
  MessageSquare,
  Plus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { ConsultingRequestCard } from './ConsultingRequestCard';
import { TeamCard } from './TeamCard';

// Define a ConsultingRequest type since it's not in database-modules.ts
interface ConsultingRequest {
  id: string;
  organizationId: string;
  requesterId: string;
  title: string;
  description: string;
  requirementDetails: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  assignedConsultantId?: string;
  estimatedHours?: number;
  attachments: any[];
  communications: any[];
  createdAt: Date;
  updatedAt: Date;
  dueDate?: Date;
  completedAt?: Date;
  tags?: string[];
}

interface OrganizationDashboardProps {
  organizationId: string;
}

export function OrganizationDashboard({
  organizationId,
}: OrganizationDashboardProps) {
  const [loading, setLoading] = useState(true);
  const [organization, setOrganization] =
    useState<OrganizationWithDetails | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [consultingRequests, setConsultingRequests] = useState<
    ConsultingRequest[]
  >([]);

  // Use hooks from the project
  const { getOrganization } = useOrganizations();
  const { getOrganizationTeams } = useTeams();

  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        // Load organization using the hook
        toast.loading('Loading organization details...');

        let org;
        try {
          org = await getOrganization(organizationId);
          toast.dismiss();
          toast.success('Organization loaded successfully');

          if (org) {
            setOrganization(org);
          } else {
            toast.error('Organization not found');
            setLoading(false);
            return;
          }
        } catch (error) {
          toast.dismiss();
          toast.error('Failed to load organization');
          setLoading(false);
          return;
        }

        // Get teams from the organization using the hook
        toast.loading('Loading teams...');
        try {
          const teamData = await getOrganizationTeams(organizationId);
          toast.dismiss();
          toast.success('Teams loaded successfully');
          setTeams(teamData || []);
        } catch (error) {
          toast.dismiss();
          toast.error('Failed to load teams');
        }

        // For consulting requests, we'll use mock data since there's no hook
        // In a real implementation, you would use a proper hook
        setConsultingRequests([
          {
            id: '1',
            organizationId,
            requesterId: '1',
            title: 'Custom Template Design',
            description: 'Need help designing a custom template',
            requirementDetails: 'We need a template for legal documents',
            priority: 'high',
            status: 'in_progress',
            attachments: [],
            communications: [],
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: '2',
            organizationId,
            requesterId: '1',
            title: 'Form Integration Help',
            description: 'Need assistance with API integration',
            requirementDetails:
              'We need to connect our forms to an external API',
            priority: 'medium',
            status: 'pending',
            attachments: [],
            communications: [],
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ]);
      } catch (error) {
        console.error('Error loading organization data:', error);
        toast.error('Failed to load organization data');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [organizationId, getOrganization, getOrganizationTeams]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-1/3 bg-muted/30 rounded animate-pulse" />
        <div className="grid gap-4 md:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="h-[180px]">
              <div className="h-full flex flex-col">
                <div className="h-12 border-b px-6 flex items-center">
                  <div className="h-4 w-24 bg-muted/30 rounded animate-pulse" />
                </div>
                <div className="flex-1 p-6 space-y-4">
                  <div className="h-4 w-full bg-muted/30 rounded animate-pulse" />
                  <div className="h-4 w-3/4 bg-muted/30 rounded animate-pulse" />
                  <div className="h-4 w-1/2 bg-muted/30 rounded animate-pulse" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-center space-y-4">
        <Building2 className="h-12 w-12 text-muted" />
        <h2 className="text-2xl font-bold">Organization Not Found</h2>
        <p className="text-muted-foreground">
          The organization you&apos;re looking for doesn&apos;t exist or you
          don&apos;t have access to it.
        </p>
        <Link href="/organizations" passHref>
          <Button>View All Organizations</Button>
        </Link>
      </div>
    );
  }

  const getPendingRequestsCount = () => {
    return consultingRequests.filter((r) => r.status === 'pending').length;
  };

  const getActiveRequestsCount = () => {
    return consultingRequests.filter(
      (r) => r.status === 'in_progress' || r.status === 'accepted'
    ).length;
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold flex items-center">
          <Building2 className="mr-2 h-7 w-7 text-primary" />
          {organization.name}
        </h1>
        {organization.logo_url && (
          <img
            src={organization.logo_url}
            alt={`${organization.name} logo`}
            className="h-12 w-12 rounded object-contain"
          />
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Users className="mr-2 h-4 w-4 text-primary" />
              Teams
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {organization.teams.length}
            </div>
            <p className="text-sm text-muted-foreground">
              {organization.teams.reduce(
                (sum: number, team: Team) => sum + (team.members?.length || 0),
                0
              )}{' '}
              total members
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Link href={`/organizations/${organizationId}/teams`} passHref>
              <Button variant="ghost" size="sm" className="w-full">
                Manage Teams
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <MessageSquare className="mr-2 h-4 w-4 text-primary" />
              Consulting Requests
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {consultingRequests.length}
            </div>
            <p className="text-sm text-muted-foreground">
              {getPendingRequestsCount()} pending · {getActiveRequestsCount()}{' '}
              active
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Link
              href={`/organizations/${organizationId}/consulting/requests`}
              passHref
            >
              <Button variant="ghost" size="sm" className="w-full">
                View All Requests
              </Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <FileText className="mr-2 h-4 w-4 text-primary" />
              Active Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(organization.features)
                ? organization.features.filter((f: any) => f.enabled).length
                : Object.keys(organization.features || {}).length}
            </div>
            <p className="text-sm text-muted-foreground">
              {organization.subscription_level.charAt(0).toUpperCase() +
                organization.subscription_level.slice(1)}{' '}
              subscription
            </p>
          </CardContent>
          <CardFooter className="pt-0">
            <Link
              href={`/organizations/${organizationId}/settings#features`}
              passHref
            >
              <Button variant="ghost" size="sm" className="w-full">
                Manage Features
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>

      {/* Teams Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Teams</h2>
          <Link href={`/organizations/${organizationId}/teams/new`} passHref>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Create Team
            </Button>
          </Link>
        </div>

        {teams.length === 0 ? (
          <Card className="border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-8 text-center">
              <Users className="h-12 w-12 text-muted" />
              <h3 className="mt-4 text-lg font-medium">No Teams</h3>
              <p className="mt-2 text-sm text-muted-foreground max-w-md">
                Create teams to organize your members and manage permissions.
                Teams can collaborate on forms and templates.
              </p>
              <Link
                href={`/organizations/${organizationId}/teams/new`}
                passHref
              >
                <Button className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Team
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {teams.slice(0, 3).map((team) => (
              <TeamCard key={team.id} team={team} />
            ))}
            {teams.length > 3 && (
              <Card className="flex items-center justify-center p-6">
                <Link href={`/organizations/${organizationId}/teams`} passHref>
                  <Button variant="outline">
                    View All {teams.length} Teams
                  </Button>
                </Link>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Consulting Requests Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Recent Consulting Requests</h2>
          <Link
            href={`/organizations/${organizationId}/consulting/requests/new`}
            passHref
          >
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              New Request
            </Button>
          </Link>
        </div>

        {consultingRequests.length === 0 ? (
          <Card className="border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-8 text-center">
              <MessageSquare className="h-12 w-12 text-muted" />
              <h3 className="mt-4 text-lg font-medium">
                No Consulting Requests
              </h3>
              <p className="mt-2 text-sm text-muted-foreground max-w-md">
                Create a consulting request to get professional help with your
                forms, templates, or integrations.
              </p>
              <Link
                href={`/organizations/${organizationId}/consulting/requests/new`}
                passHref
              >
                <Button className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Request
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {consultingRequests.slice(0, 3).map((request) => (
              <ConsultingRequestCard key={request.id} request={request} />
            ))}
            {consultingRequests.length > 3 && (
              <Card className="flex items-center justify-center p-6">
                <Link
                  href={`/organizations/${organizationId}/consulting/requests`}
                  passHref
                >
                  <Button variant="outline">
                    View All {consultingRequests.length} Requests
                  </Button>
                </Link>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Usage Analytics Teaser */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart className="mr-2 h-5 w-5 text-primary" />
            Usage Analytics
          </CardTitle>
          <CardDescription>
            Track form usage, submissions, and document generation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <p className="mb-4 text-muted-foreground">
              Access detailed analytics about your organization&apos;s form
              usage, document generation, and more.
            </p>
            <Link href={`/organizations/${organizationId}/analytics`} passHref>
              <Button>View Analytics</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
