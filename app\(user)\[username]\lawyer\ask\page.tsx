'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useLawyers } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileQuestion, Gavel, MessageSquare } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const questionSchema = z.object({
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  category: z.string().min(1, 'Please select a category'),
  question: z.string().min(20, 'Question must be at least 20 characters'),
  preferredLawyer: z.string().optional(),
});

type QuestionFormValues = z.infer<typeof questionSchema>;

export default function AskQuestionPage() {
  // We don't need to use the username parameter directly
  const { loading, lawyers, submitQuestion } = useLawyers();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<QuestionFormValues>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      subject: '',
      category: '',
      question: '',
      preferredLawyer: '',
    },
  });

  const onSubmit = async (values: QuestionFormValues) => {
    setIsSubmitting(true);

    // Get the selected lawyer's name for the success message
    const selectedLawyerName = values.preferredLawyer
      ? lawyers.find((l) => l.id === values.preferredLawyer)?.full_name ||
        'your preferred lawyer'
      : 'a qualified lawyer';

    try {
      // First create the promise
      const questionPromise = submitQuestion({
        subject: values.subject,
        category: values.category,
        question: values.question,
        preferredLawyer: values.preferredLawyer,
      });

      // Show toast for the promise
      toast.promise(questionPromise, {
        loading: 'Submitting your legal question...',
        success:
          'Question submitted successfully! ' +
          `${selectedLawyerName} will respond to your question soon.`,
        error:
          'Failed to submit question. Please try again or contact support.',
      });

      // Actually await the promise
      await questionPromise;

      // Reset the form on success
      form.reset();
    } catch (error) {
      console.error('Error submitting question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <section className="mb-6">
        <div className="flex items-center gap-2">
          <FileQuestion className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Ask a Legal Question</h1>
        </div>
        <p className="text-neutral-500 mt-1">
          Get quick answers from qualified attorneys
        </p>
      </section>

      <div className="grid md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Submit Your Question</CardTitle>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="subject"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subject</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Brief description of your question"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="contract">
                              Contract Law
                            </SelectItem>
                            <SelectItem value="corporate">
                              Corporate Law
                            </SelectItem>
                            <SelectItem value="ip">
                              Intellectual Property
                            </SelectItem>
                            <SelectItem value="employment">
                              Employment Law
                            </SelectItem>
                            <SelectItem value="family">Family Law</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="question"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Your Question</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe your legal question in detail..."
                            className="min-h-32"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Be specific and include relevant details to get the
                          most accurate answer.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="preferredLawyer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preferred Lawyer (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a lawyer (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {loading ? (
                              <SelectItem value="" disabled>
                                Loading lawyers...
                              </SelectItem>
                            ) : lawyers.length === 0 ? (
                              <SelectItem value="" disabled>
                                No lawyers available
                              </SelectItem>
                            ) : (
                              lawyers.map((lawyer) => (
                                <SelectItem key={lawyer.id} value={lawyer.id}>
                                  {lawyer.full_name} -{' '}
                                  {lawyer.specialization.join(', ')}
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          If you have a preferred lawyer, select them here.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Question'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <FileQuestion className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">Submit Your Question</p>
                    <p className="text-sm text-muted-foreground">
                      Provide details about your legal issue
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Gavel className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium">Lawyer Review</p>
                    <p className="text-sm text-muted-foreground">
                      A qualified attorney reviews your question
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-green-100 p-2 rounded-full">
                    <MessageSquare className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">Get Your Answer</p>
                    <p className="text-sm text-muted-foreground">
                      Receive a response within 24-48 hours
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
                <p className="text-sm text-blue-700">
                  <strong>Note:</strong> For complex legal matters, we recommend
                  scheduling a full consultation with a lawyer.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
