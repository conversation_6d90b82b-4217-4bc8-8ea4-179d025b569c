'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Sheet } from '@/components/ui/sheet';
import * as Textarea from '@/components/ui/textarea';
import { useDocuments } from '@/lib/hooks';
import {
  DocumentInsert,
  DocumentStatus,
  DocumentType,
} from '@/lib/types/database-modules';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface CreateDocumentSheetProps {
  isOpen: boolean;
  onClose: () => void;
  username: string;
}

export function CreateDocumentSheet({
  isOpen,
  onClose,
  username,
}: CreateDocumentSheetProps) {
  const { createDocument } = useDocuments();
  const router = useRouter();

  const [newDocument, setNewDocument] = useState<Partial<DocumentInsert>>({
    title: '',
    description: '',
    document_type: 'form' as DocumentType,
    status: 'draft' as DocumentStatus,
    is_template: false,
    content: { sections: [] },
  });

  const handleCreateDocument = async () => {
    if (!newDocument.title?.trim()) {
      toast.error('Missing information', {
        description: 'Please provide a title for the document',
      });
      return;
    }

    // Create a promise for the document creation
    const createPromise = createDocument.mutate(newDocument as DocumentInsert);

    // Use toast.promise to handle loading, success, and error states
    toast.promise(createPromise, {
      loading: 'Creating document...',
      success: 'Document created successfully',
      error: 'Failed to create document',
    });

    // Wait for the promise to resolve
    createPromise
      .then((createdDocument) => {
        // Reset form
        setNewDocument({
          title: '',
          description: '',
          document_type: 'form' as DocumentType,
          status: 'draft' as DocumentStatus,
          is_template: false,
          content: { sections: [] },
        });

        // Close the sheet
        onClose();

        // Navigate to the new document
        if (
          createdDocument &&
          typeof createdDocument === 'object' &&
          'id' in createdDocument
        ) {
          router.push(`/${username}/documents/${createdDocument.id}`);
        }
      })
      .catch((error) => {
        console.error('Error creating document:', error);
      });
  };

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <Sheet.Title>Create New Document</Sheet.Title>
        </div>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={newDocument.title || ''}
              onChange={(e) =>
                setNewDocument({ ...newDocument, title: e.target.value })
              }
              placeholder="Document title"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea.Root
              id="description"
              value={newDocument.description || ''}
              onChange={(e) =>
                setNewDocument({
                  ...newDocument,
                  description: e.target.value,
                })
              }
              placeholder="Document description"
            >
              <Textarea.CharCounter
                current={(newDocument.description || '').length}
                max={500}
              />
            </Textarea.Root>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="document-type">Document Type</Label>
              <Select
                value={newDocument.document_type}
                onValueChange={(value) =>
                  setNewDocument({
                    ...newDocument,
                    document_type: value as DocumentType,
                  })
                }
              >
                <SelectTrigger id="document-type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="form">Form</SelectItem>
                  <SelectItem value="contract">Contract</SelectItem>
                  <SelectItem value="letter">Letter</SelectItem>
                  <SelectItem value="agreement">Agreement</SelectItem>
                  <SelectItem value="report">Report</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label
                htmlFor="is-template"
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  id="is-template"
                  checked={newDocument.is_template}
                  onChange={(e) =>
                    setNewDocument({
                      ...newDocument,
                      is_template: e.target.checked,
                    })
                  }
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <span>Save as Template</span>
              </Label>
            </div>
          </div>
        </div>

        <div className="pt-4 border-t flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateDocument}
            disabled={!newDocument.title?.trim()}
          >
            Create Document
          </Button>
        </div>
      </div>
    </Sheet>
  );
}
