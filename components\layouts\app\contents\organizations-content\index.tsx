'use client';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { SidebarMenuPulseLink } from '@/components/ui/sidebar-menu-pulse-link';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import {
  Building2,
  ChevronLeft,
  PlusCircle,
  Settings,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface OrganizationsContentProps {
  direction?: number;
}

export default function OrganizationsContent({
  direction = 0,
}: OrganizationsContentProps) {
  const { profile } = userStore();
  const pathname = usePathname();
  const [showBackButton, setShowBackButton] = useState(false);

  // Define organization navigation items
  const organizationItems = [
    {
      name: 'All Organizations',
      url: `/${profile?.username}/organizations`,
      icon: Building2,
      active: pathname === `/${profile?.username}/organizations`,
    },
    {
      name: 'Create Organization',
      url: `/${profile?.username}/organizations/new`,
      icon: PlusCircle,
      active: pathname === `/${profile?.username}/organizations/new`,
    },
  ];

  // Define organization settings items
  const organizationSettingsItems = [
    {
      name: 'Organization Settings',
      url: `/${profile?.username}/organizations/settings`,
      icon: Settings,
      active: pathname.includes(`/${profile?.username}/organizations/settings`),
    },
    {
      name: 'Team Management',
      url: `/${profile?.username}/organizations/teams`,
      icon: Users,
      active: pathname.includes(`/${profile?.username}/organizations/teams`),
    },
  ];

  return (
    <motion.div
      key="organizations"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: 20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
      className="w-full overflow-hidden"
    >
      <div className="py-2 px-4 flex items-center gap-2 border-b h-10 border-dashed border-neutral-300/60">
        <Link
          href={`/${profile?.username}`}
          className={cn(
            'flex items-center gap-1 transition-all duration-200 ease-out',
            showBackButton ? 'text-neutral-600' : 'text-neutral-400'
          )}
          onMouseEnter={() => setShowBackButton(true)}
          onMouseLeave={() => setShowBackButton(false)}
        >
          <motion.span
            initial={{ x: 0 }}
            animate={{ x: showBackButton ? -3 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronLeft className="size-4" />
          </motion.span>
          <span className="text-sm font-medium uppercase">Organizations</span>
        </Link>
      </div>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Organizations
        </SidebarGroupLabel>
        <SidebarMenu>
          {organizationItems.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuPulseLink
                href={item.url}
                icon={item.icon}
                name={item.name}
              />
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Management
        </SidebarGroupLabel>
        <SidebarMenu>
          {organizationSettingsItems.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuPulseLink
                href={item.url}
                icon={item.icon}
                name={item.name}
              />
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>
    </motion.div>
  );
}
