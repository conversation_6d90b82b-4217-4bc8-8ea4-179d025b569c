'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDocuments } from '@/lib/hooks';
import { DocumentAttachment } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import {
  Download,
  File,
  FileArchive,
  FileAudio,
  FileImage,
  FileText,
  FileVideo,
  Loader2,
  Trash2,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface FileListProps {
  documentId: string;
  className?: string;
  onDelete?: (attachmentId: string) => void;
}

export function FileList({ documentId, className, onDelete }: FileListProps) {
  const { getAttachments, deleteAttachment } = useDocuments();
  const [attachments, setAttachments] = useState<DocumentAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);

  useEffect(() => {
    const fetchAttachments = async () => {
      setLoading(true);
      try {
        const data = await getAttachments(documentId);
        setAttachments(data);
      } catch (error) {
        console.error('Error fetching attachments:', error);
        toast.error('Failed to load attachments');
      } finally {
        setLoading(false);
      }
    };

    fetchAttachments();
  }, [documentId, getAttachments]);

  const handleDelete = async (attachmentId: string) => {
    if (confirm('Are you sure you want to delete this file?')) {
      setDeleting(attachmentId);
      try {
        const success = await deleteAttachment(attachmentId, documentId);
        if (success) {
          setAttachments((prev) =>
            prev.filter((attachment) => attachment.id !== attachmentId)
          );
          toast.success('File deleted successfully');
          if (onDelete) {
            onDelete(attachmentId);
          }
        } else {
          throw new Error('Failed to delete file');
        }
      } catch (error) {
        console.error('Error deleting file:', error);
        toast.error('Failed to delete file');
      } finally {
        setDeleting(null);
      }
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <FileImage className="h-8 w-8 text-blue-500" />;
    } else if (fileType.startsWith('video/')) {
      return <FileVideo className="h-8 w-8 text-red-500" />;
    } else if (fileType.startsWith('audio/')) {
      return <FileAudio className="h-8 w-8 text-green-500" />;
    } else if (fileType === 'application/pdf') {
      return <FileText className="h-8 w-8 text-red-600" />;
    } else if (
      fileType === 'application/zip' ||
      fileType === 'application/x-zip-compressed'
    ) {
      return <FileArchive className="h-8 w-8 text-yellow-600" />;
    } else if (
      fileType.includes('word') ||
      fileType.includes('document') ||
      fileType.includes('text')
    ) {
      return <FileText className="h-8 w-8 text-blue-600" />;
    } else {
      return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <Card className={cn('w-full', className)}>
        <CardHeader>
          <CardTitle>Attachments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-md" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (attachments.length === 0) {
    return (
      <Card className={cn('w-full', className)}>
        <CardHeader>
          <CardTitle>Attachments</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            <p>No attachments yet</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle>Attachments ({attachments.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {attachments.map((attachment) => (
            <div
              key={attachment.id}
              className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {getFileIcon(attachment.file_type)}
                <div className="space-y-1">
                  <p className="text-sm font-medium">{attachment.file_name}</p>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <span>{formatFileSize(attachment.file_size)}</span>
                    <span>•</span>
                    <span>
                      {formatDistanceToNow(new Date(attachment.created_at), {
                        addSuffix: true,
                      })}
                    </span>
                    {attachment.description && (
                      <>
                        <span>•</span>
                        <span className="truncate max-w-[150px]">
                          {attachment.description}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="ghost" size="icon" asChild>
                  <a
                    href={attachment.file_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    download
                  >
                    <Download className="h-4 w-4" />
                  </a>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDelete(attachment.id)}
                  disabled={deleting === attachment.id}
                >
                  {deleting === attachment.id ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4 text-destructive" />
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
