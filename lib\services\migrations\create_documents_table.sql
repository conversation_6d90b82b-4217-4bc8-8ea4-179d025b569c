-- Create documents table with appropriate fields and relationships
CREATE TABLE IF NOT EXISTS public.documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT,
  content JSONB,
  document_type TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft',
  version INTEGER NOT NULL DEFAULT 1,
  is_template BOOLEAN NOT NULL DEFAULT false,
  template_id UUID REFERENCES public.documents(id),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  metadata JSONB,
  file_url TEXT,
  file_type TEXT,
  file_size INTEGER,
  shared_with JSONB DEFAULT '[]'::jsonb
);

-- Add RLS (Row Level Security) policies
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own documents
CREATE POLICY "Users can view their own documents"
  ON public.documents
  FOR SELECT
  USING (owner_id = auth.uid());

-- Policy for users to view documents shared with them
CREATE POLICY "Users can view documents shared with them"
  ON public.documents
  FOR SELECT
  USING (shared_with ? auth.uid()::text);

-- Policy for users to insert their own documents
CREATE POLICY "Users can insert their own documents"
  ON public.documents
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

-- Policy for users to update their own documents
CREATE POLICY "Users can update their own documents"
  ON public.documents
  FOR UPDATE
  USING (owner_id = auth.uid());

-- Policy for users to delete their own documents
CREATE POLICY "Users can delete their own documents"
  ON public.documents
  FOR DELETE
  USING (owner_id = auth.uid());

-- Create tags table for document categorization
CREATE TABLE IF NOT EXISTS public.document_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create junction table for many-to-many relationship between documents and tags
CREATE TABLE IF NOT EXISTS public.document_to_tags (
  document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
  tag_id UUID NOT NULL REFERENCES public.document_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (document_id, tag_id)
);

-- Add RLS policies for tags
ALTER TABLE public.document_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_to_tags ENABLE ROW LEVEL SECURITY;

-- Everyone can view tags
CREATE POLICY "Everyone can view tags"
  ON public.document_tags
  FOR SELECT
  TO authenticated
  USING (true);

-- Only users with manage_tags permission can insert tags
CREATE POLICY "Users can insert tags"
  ON public.document_tags
  FOR INSERT
  TO authenticated
  USING (true);

-- Only users with manage_tags permission can update tags
CREATE POLICY "Users can update tags"
  ON public.document_tags
  FOR UPDATE
  TO authenticated
  USING (true);

-- Users can view document-tag relationships
CREATE POLICY "Users can view document-tag relationships"
  ON public.document_to_tags
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.documents d
      WHERE d.id = document_id
      AND (d.owner_id = auth.uid() OR d.shared_with ? auth.uid()::text)
    )
  );

-- Users can manage tags for their own documents
CREATE POLICY "Users can manage tags for their own documents"
  ON public.document_to_tags
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.documents d
      WHERE d.id = document_id
      AND d.owner_id = auth.uid()
    )
  );

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update the updated_at timestamp
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add index for faster queries
CREATE INDEX idx_documents_owner_id ON public.documents(owner_id);
CREATE INDEX idx_documents_document_type ON public.documents(document_type);
CREATE INDEX idx_documents_is_template ON public.documents(is_template);
CREATE INDEX idx_document_tags_name ON public.document_tags(name);

-- Create a view for document summaries
CREATE OR REPLACE VIEW public.document_summaries AS
SELECT 
  d.id,
  d.title,
  d.description,
  d.document_type,
  d.status,
  d.is_template,
  d.created_at,
  d.updated_at,
  d.owner_id,
  p.username as owner_username,
  p.full_name as owner_full_name,
  COALESCE(
    (SELECT jsonb_agg(t.name)
     FROM public.document_to_tags dt
     JOIN public.document_tags t ON dt.tag_id = t.id
     WHERE dt.document_id = d.id
    ),
    '[]'::jsonb
  ) as tags
FROM 
  public.documents d
JOIN
  public.profiles p ON d.owner_id = p.id;

-- Grant appropriate permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.document_tags TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.document_to_tags TO authenticated;
GRANT SELECT ON public.document_summaries TO authenticated; 