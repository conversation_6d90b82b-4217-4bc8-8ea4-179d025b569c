import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { pageProps } from '@/types';
import {
  Briefcase,
  Check,
  FileCheck,
  FileEdit,
  FilePlus,
  FileText,
  GraduationCap,
  HandshakeIcon,
  Newspaper,
  Plus,
  Search,
} from 'lucide-react';

export default async function CreateDocumentPage({ params }: pageProps) {
  const username = (await params).username;

  // Mock templates data
  const templates = [
    {
      id: 1,
      title: 'Non-Disclosure Agreement',
      description: 'Standard NDA for business confidentiality',
      category: 'Legal',
      icon: FileCheck,
      popular: true,
    },
    {
      id: 2,
      title: 'Employment Contract',
      description: 'Standard employment agreement',
      category: 'HR',
      icon: Briefcase,
      popular: true,
    },
    {
      id: 3,
      title: 'Service Agreement',
      description: 'Contract for professional services',
      category: 'Legal',
      icon: HandshakeIcon,
      popular: false,
    },
    {
      id: 4,
      title: 'Privacy Policy',
      description: 'Website privacy policy document',
      category: 'Legal',
      icon: FileText,
      popular: false,
    },
    {
      id: 5,
      title: 'Terms of Service',
      description: 'Website terms and conditions',
      category: 'Legal',
      icon: FileText,
      popular: false,
    },
    {
      id: 6,
      title: 'Press Release',
      description: 'Company announcement template',
      category: 'Marketing',
      icon: Newspaper,
      popular: false,
    },
    {
      id: 7,
      title: 'Research Report',
      description: 'Research findings document format',
      category: 'Academic',
      icon: GraduationCap,
      popular: false,
    },
    {
      id: 8,
      title: 'Blank Document',
      description: 'Start with a clean slate',
      category: 'General',
      icon: FileEdit,
      popular: true,
    },
  ];

  // Group templates by category
  const categories = [
    ...new Set(templates.map((template) => template.category)),
  ];
  const popularTemplates = templates.filter((template) => template.popular);

  // Function to get icon color by category
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Legal':
        return 'text-blue-600';
      case 'HR':
        return 'text-purple-600';
      case 'Marketing':
        return 'text-amber-600';
      case 'Academic':
        return 'text-green-600';
      default:
        return 'text-neutral-600';
    }
  };

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center gap-2">
          <FilePlus className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Create New Document</h1>
        </div>
        <p className="text-neutral-500 mt-1">
          Create a document from scratch or choose a template
        </p>
      </section>

      <section className="mb-8">
        <div className="flex flex-col gap-2 max-w-xl">
          <Label htmlFor="documentName">Document Name</Label>
          <div className="flex gap-2">
            <Input id="documentName" placeholder="Enter document name" />
            <Button className="whitespace-nowrap">
              <Plus className="size-4 mr-1" /> Create Blank
            </Button>
          </div>
        </div>
      </section>

      <section>
        <Tabs defaultValue="popular">
          <div className="flex items-center justify-between mb-4">
            <TabsList>
              <TabsTrigger value="popular">Popular</TabsTrigger>
              {categories.map((category) => (
                <TabsTrigger key={category} value={category}>
                  {category}
                </TabsTrigger>
              ))}
              <TabsTrigger value="all">All Templates</TabsTrigger>
            </TabsList>

            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-neutral-400" />
              <Input
                className="pl-9 w-[250px]"
                placeholder="Search templates"
              />
            </div>
          </div>

          {/* Popular templates tab */}
          <TabsContent value="popular" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {popularTemplates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  getCategoryColor={getCategoryColor}
                />
              ))}
            </div>
          </TabsContent>

          {/* Category tabs */}
          {categories.map((category) => (
            <TabsContent key={category} value={category} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates
                  .filter((template) => template.category === category)
                  .map((template) => (
                    <TemplateCard
                      key={template.id}
                      template={template}
                      getCategoryColor={getCategoryColor}
                    />
                  ))}
              </div>
            </TabsContent>
          ))}

          {/* All templates tab */}
          <TabsContent value="all" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  getCategoryColor={getCategoryColor}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
}

// Template card component
function TemplateCard({
  template,
  getCategoryColor,
}: {
  template: {
    id: number;
    title: string;
    description: string;
    category: string;
    icon: any;
    popular: boolean;
  };
  getCategoryColor: (category: string) => string;
}) {
  const IconComponent = template.icon;

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <CardContent className="p-0">
        <div className="p-5">
          <div className="flex items-start justify-between mb-3">
            <div
              className={`size-10 rounded-lg flex items-center justify-center ${getCategoryColor(template.category)} bg-opacity-10 bg-current`}
            >
              <IconComponent
                className={`size-5 ${getCategoryColor(template.category)}`}
              />
            </div>
            <Badge variant="outline">{template.category}</Badge>
          </div>
          <h3 className="font-semibold text-lg mb-1">{template.title}</h3>
          <p className="text-neutral-500 text-sm">{template.description}</p>
        </div>
        <div className="border-t p-3 flex justify-between items-center bg-neutral-50">
          <Button variant="ghost" size="sm">
            Preview
          </Button>
          <Button>
            <Check className="size-4 mr-1" /> Use Template
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
