'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ReportDateRange, ReportFilters } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, Filter, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface ReportFiltersProps {
  filters: ReportFilters;
  onFiltersChange: (filters: ReportFilters) => void;
  onApplyFilters: () => void;
  predefinedRanges: Record<string, ReportDateRange>;
  isLoading?: boolean;
}

export function ReportFiltersComponent({
  filters,
  onFiltersChange,
  onApplyFilters,
  predefinedRanges,
  isLoading = false,
}: ReportFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Handle date range change
  const handleDateRangeChange = (range: ReportDateRange) => {
    onFiltersChange({
      ...filters,
      dateRange: range,
    });
  };

  // Handle consultation type change
  const handleConsultationTypeChange = (
    type: 'video' | 'document',
    checked: boolean
  ) => {
    const currentTypes = filters.consultationTypes || [];

    if (checked) {
      onFiltersChange({
        ...filters,
        consultationTypes: [...currentTypes, type],
      });
    } else {
      onFiltersChange({
        ...filters,
        consultationTypes: currentTypes.filter((t: string) => t !== type),
      });
    }
  };

  // Handle status change
  const handleStatusChange = (
    status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled',
    checked: boolean
  ) => {
    const currentStatuses = filters.statuses || [];

    if (checked) {
      onFiltersChange({
        ...filters,
        statuses: [...currentStatuses, status],
      });
    } else {
      onFiltersChange({
        ...filters,
        statuses: currentStatuses.filter((s: string) => s !== status),
      });
    }
  };

  // Handle recurring change
  const handleRecurringChange = (checked: boolean | 'indeterminate') => {
    if (typeof checked === 'boolean') {
      onFiltersChange({
        ...filters,
        isRecurring: checked,
      });
    }
  };

  return (
    <div className="mb-6">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <PopoverTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="h-4 w-4" />
                Filters
              </Button>
            </PopoverTrigger>

            <div className="text-sm">
              <span className="text-muted-foreground">Date Range:</span>{' '}
              <span className="font-medium">
                {format(filters.dateRange.startDate, 'MMM d, yyyy')} -{' '}
                {format(filters.dateRange.endDate, 'MMM d, yyyy')}
              </span>
            </div>
          </div>

          <Button onClick={onApplyFilters} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              'Generate Report'
            )}
          </Button>
        </div>

        <PopoverContent className="w-[400px] p-0" align="start">
          <Card>
            <CardContent className="p-4 space-y-4">
              {/* Date Range */}
              <div className="space-y-2">
                <Label className="text-base">Date Range</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'justify-start text-left font-normal',
                          !filters.dateRange.startDate &&
                            'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {filters.dateRange.startDate
                          ? format(filters.dateRange.startDate, 'PPP')
                          : 'Start date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={filters.dateRange.startDate}
                        onSelect={(date) =>
                          date &&
                          handleDateRangeChange({
                            ...filters.dateRange,
                            startDate: date,
                          })
                        }
                      />
                    </PopoverContent>
                  </Popover>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'justify-start text-left font-normal',
                          !filters.dateRange.endDate && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {filters.dateRange.endDate
                          ? format(filters.dateRange.endDate, 'PPP')
                          : 'End date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={filters.dateRange.endDate}
                        onSelect={(date) =>
                          date &&
                          handleDateRangeChange({
                            ...filters.dateRange,
                            endDate: date,
                          })
                        }
                        disabled={(date) => date < filters.dateRange.startDate}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-2">
                  {Object.entries(predefinedRanges).map(([key, range]) => (
                    <Button
                      key={key}
                      variant="outline"
                      size="sm"
                      onClick={() => handleDateRangeChange(range)}
                    >
                      {key
                        .replace(/([A-Z])/g, ' $1')
                        .replace(/^./, (str) => str.toUpperCase())}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Consultation Type */}
              <div className="space-y-2">
                <Label className="text-base">Consultation Type</Label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="video"
                      checked={filters.consultationTypes?.includes('video')}
                      onCheckedChange={(checked) =>
                        handleConsultationTypeChange('video', !!checked)
                      }
                    />
                    <Label htmlFor="video">Video Consultations</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="document"
                      checked={filters.consultationTypes?.includes('document')}
                      onCheckedChange={(checked) =>
                        handleConsultationTypeChange('document', !!checked)
                      }
                    />
                    <Label htmlFor="document">Document Reviews</Label>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label className="text-base">Status</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="scheduled"
                      checked={filters.statuses?.includes('scheduled')}
                      onCheckedChange={(checked) =>
                        handleStatusChange('scheduled', !!checked)
                      }
                    />
                    <Label htmlFor="scheduled">Scheduled</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="confirmed"
                      checked={filters.statuses?.includes('confirmed')}
                      onCheckedChange={(checked) =>
                        handleStatusChange('confirmed', !!checked)
                      }
                    />
                    <Label htmlFor="confirmed">Confirmed</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="completed"
                      checked={filters.statuses?.includes('completed')}
                      onCheckedChange={(checked) =>
                        handleStatusChange('completed', !!checked)
                      }
                    />
                    <Label htmlFor="completed">Completed</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="cancelled"
                      checked={filters.statuses?.includes('cancelled')}
                      onCheckedChange={(checked) =>
                        handleStatusChange('cancelled', !!checked)
                      }
                    />
                    <Label htmlFor="cancelled">Cancelled</Label>
                  </div>
                </div>
              </div>

              {/* Recurring */}
              <div className="space-y-2">
                <Label className="text-base">Recurring</Label>
                <RadioGroup
                  value={
                    filters.isRecurring === undefined
                      ? 'all'
                      : filters.isRecurring
                        ? 'yes'
                        : 'no'
                  }
                  onValueChange={(value) => {
                    if (value === 'all') {
                      handleRecurringChange('indeterminate');
                    } else {
                      handleRecurringChange(value === 'yes');
                    }
                  }}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="all" />
                    <Label htmlFor="all">All Consultations</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="yes" />
                    <Label htmlFor="yes">Recurring Only</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="no" />
                    <Label htmlFor="no">Non-Recurring Only</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    onFiltersChange({
                      dateRange: predefinedRanges.last30Days,
                      consultationTypes: [],
                      statuses: [],
                      isRecurring: 'indeterminate',
                    });
                  }}
                >
                  Reset Filters
                </Button>
                <Button
                  onClick={() => {
                    setIsOpen(false);
                    onApplyFilters();
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
    </div>
  );
}
