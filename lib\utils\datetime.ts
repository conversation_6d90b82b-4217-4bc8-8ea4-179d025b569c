import {
  format,
  parseISO,
  formatDistance,
  formatRelative,
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  isValid,
  formatDistanceToNow,
} from 'date-fns';

type DateInput = string | Date;
type FormatPattern = 'full' | 'short' | 'time' | 'dateTime' | 'iso';

interface DateFormats {
  full: 'MMMM do, yyyy';
  short: 'MMM d, yyyy';
  time: 'h:mm a';
  dateTime: 'MMM d, yyyy h:mm a';
  iso: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx";
}

export const du = {
  /**
   * Converts an ISO date string to a Date object
   * @param dateString - ISO date string to parse
   * @returns Date object
   */
  parseDate: (dateString: string): Date => {
    return parseISO(dateString);
  },

  /**
   * Formats a date into a readable string
   * @param date - Date to format (string or Date object)
   * @param formatString - Format pattern to use
   * @returns Formatted date string
   */
  formatDate: (date: DateInput, formatString: string = 'PPP'): string => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, formatString);
  },

  /**
   * Gets relative time from now (e.g., "2 hours ago")
   * @param date - Date to compare
   * @returns Relative time string
   */
  getRelativeTime: (date: DateInput): string => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true });
  },

  /**
   * Gets distance between two dates
   * @param dateLeft - First date to compare
   * @param dateRight - Second date to compare
   * @returns Distance string between dates
   */
  getDateDistance: (dateLeft: DateInput, dateRight: DateInput): string => {
    const dateLeftObj =
      typeof dateLeft === 'string' ? parseISO(dateLeft) : dateLeft;
    const dateRightObj =
      typeof dateRight === 'string' ? parseISO(dateRight) : dateRight;
    return formatDistance(dateLeftObj, dateRightObj);
  },

  /**
   * Formats date relative to current date
   * @param date - Date to format
   * @returns Relative formatted string
   */
  getRelativeFormat: (date: DateInput): string => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return formatRelative(dateObj, new Date());
  },

  /**
   * Gets difference in days between two dates
   * @param dateLeft - First date to compare
   * @param dateRight - Second date to compare
   * @returns Number of days difference
   */
  getDaysDifference: (dateLeft: DateInput, dateRight: DateInput): number => {
    const dateLeftObj =
      typeof dateLeft === 'string' ? parseISO(dateLeft) : dateLeft;
    const dateRightObj =
      typeof dateRight === 'string' ? parseISO(dateRight) : dateRight;
    return differenceInDays(dateLeftObj, dateRightObj);
  },

  /**
   * Gets difference in hours between two dates
   * @param dateLeft - First date to compare
   * @param dateRight - Second date to compare
   * @returns Number of hours difference
   */
  getHoursDifference: (dateLeft: DateInput, dateRight: DateInput): number => {
    const dateLeftObj =
      typeof dateLeft === 'string' ? parseISO(dateLeft) : dateLeft;
    const dateRightObj =
      typeof dateRight === 'string' ? parseISO(dateRight) : dateRight;
    return differenceInHours(dateLeftObj, dateRightObj);
  },

  /**
   * Gets difference in minutes between two dates
   * @param dateLeft - First date to compare
   * @param dateRight - Second date to compare
   * @returns Number of minutes difference
   */
  getMinutesDifference: (dateLeft: DateInput, dateRight: DateInput): number => {
    const dateLeftObj =
      typeof dateLeft === 'string' ? parseISO(dateLeft) : dateLeft;
    const dateRightObj =
      typeof dateRight === 'string' ? parseISO(dateRight) : dateRight;
    return differenceInMinutes(dateLeftObj, dateRightObj);
  },

  /**
   * Common format patterns for dates
   */
  formats: {
    full: 'MMMM do, yyyy',
    short: 'MMM d, yyyy',
    time: 'h:mm a',
    dateTime: 'MMM d, yyyy h:mm a',
    iso: "yyyy-MM-dd'T'HH:mm:ss.SSSxxx",
  } as DateFormats,

  /**
   * Validates if a date string is valid
   * @param dateString - Date string to validate
   * @returns Boolean indicating if date is valid
   */
  isValidDate: (dateString: string): boolean => {
    return isValid(parseISO(dateString));
  },
};
