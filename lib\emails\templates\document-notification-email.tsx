import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  <PERSON>,
  <PERSON>r,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface DocumentNotificationEmailProps {
  documentLink: string;
  userName: string;
  documentName: string;
  notificationType: 'shared' | 'updated' | 'commented' | 'signed';
  senderName: string;
  message?: string;
}

export const DocumentNotificationEmail = ({
  documentLink,
  userName,
  documentName,
  notificationType,
  senderName,
  message,
}: DocumentNotificationEmailProps) => {
  // Generate the appropriate subject and content based on notification type
  const getSubject = () => {
    switch (notificationType) {
      case 'shared':
        return `${senderName} shared a document with you`;
      case 'updated':
        return `${senderName} updated "${documentName}"`;
      case 'commented':
        return `${senderName} commented on "${documentName}"`;
      case 'signed':
        return `${senderName} signed "${documentName}"`;
      default:
        return `Notification about "${documentName}"`;
    }
  };

  const getHeading = () => {
    switch (notificationType) {
      case 'shared':
        return 'Document Shared With You';
      case 'updated':
        return 'Document Updated';
      case 'commented':
        return 'New Comment on Document';
      case 'signed':
        return 'Document Signed';
      default:
        return 'Document Notification';
    }
  };

  const getMainContent = () => {
    switch (notificationType) {
      case 'shared':
        return `${senderName} has shared the document "${documentName}" with you. You can now view and collaborate on this document.`;
      case 'updated':
        return `${senderName} has made updates to the document "${documentName}". You can review the changes by clicking the button below.`;
      case 'commented':
        return `${senderName} has added a comment to the document "${documentName}". You can view the comment and respond by clicking the button below.`;
      case 'signed':
        return `${senderName} has signed the document "${documentName}". You can view the signed document by clicking the button below.`;
      default:
        return `You have a new notification regarding the document "${documentName}".`;
    }
  };

  const getButtonText = () => {
    switch (notificationType) {
      case 'shared':
        return 'View Document';
      case 'updated':
        return 'Review Changes';
      case 'commented':
        return 'View Comment';
      case 'signed':
        return 'View Signed Document';
      default:
        return 'View Document';
    }
  };

  return (
    <Html>
      <Head />
      <Preview>{getSubject()}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={h1}>{getHeading()}</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>{getMainContent()}</Text>
          
          {message && (
            <Section style={messageContainer}>
              <Text style={messageText}>{message}</Text>
            </Section>
          )}
          
          <Section style={buttonContainer}>
            <Button
              style={{ ...button, padding: '12px 20px' }}
              href={documentLink}
            >
              {getButtonText()}
            </Button>
          </Section>
          
          <Text style={text}>
            If you have any questions, please contact our support team.
          </Text>
          
          <Hr style={hr} />
          
          <Text style={footer}>
            If the button above doesn't work, paste this link into your browser:
          </Text>
          <Text style={link}>
            <Link href={documentLink} style={link}>
              {documentLink}
            </Link>
          </Text>
          <Text style={footer}>
            © {new Date().getFullYear()} NotAMess Forms. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  borderRadius: '4px',
  maxWidth: '600px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '30px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const messageContainer = {
  backgroundColor: '#f9f9f9',
  borderLeft: '4px solid #6366f1',
  padding: '12px 15px',
  margin: '20px 0',
  borderRadius: '4px',
};

const messageText = {
  color: '#4b5563',
  fontSize: '15px',
  lineHeight: '22px',
  margin: '0',
  fontStyle: 'italic',
};

const buttonContainer = {
  margin: '24px 0',
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#6366f1',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
};

const link = {
  color: '#6366f1',
  fontSize: '12px',
  textDecoration: 'underline',
  wordBreak: 'break-all' as const,
};
