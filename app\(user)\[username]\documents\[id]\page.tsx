'use client';

import {
  CollaborationPanel,
  CreateFromTemplateSheet,
  DocumentAttachments,
  DocumentShareSheet,
  ExportSheet,
  LawyerConsultationPanel,
  VersionHistorySheet,
} from '@/components/documents';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sheet } from '@/components/ui/sheet';
import { useDocuments } from '@/lib/hooks';
import { Document } from '@/lib/types/database-modules';
import { format, formatDistanceToNow } from 'date-fns';
import {
  ArrowLeft as ArrowLeftIcon,
  Calendar as CalendarIcon,
  Copy as CopyIcon,
  Download as DownloadIcon,
  File as FileIcon,
  FileText as FileTextIcon,
  History,
  Info as InfoIcon,
  Pencil as PencilIcon,
  Share2 as Share2Icon,
  BookTemplate as TemplateIcon,
  Trash as TrashIcon,
  User as UserIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { lazy, Suspense, useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

// Lazy load the DocumentPreview component
const DocumentPreview = lazy(
  () => import('@/components/documents/preview/DocumentPreview')
);

type DocumentPageProps = {
  params: Promise<{
    id: string;
    username: string;
  }>;
};

export default function DocumentPage(props: DocumentPageProps) {
  const unwrappedParams = React.use(props.params);
  const router = useRouter();
  const { getById, deleteDocument, addDocumentActivity } = useDocuments();

  const [document, setDocument] = useState<Document | null>(null);
  const [error, setError] = useState<Error | null>(null);

  // Dialog/sheet states
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [isVersionHistoryOpen, setIsVersionHistoryOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isCreateFromTemplateOpen, setIsCreateFromTemplateOpen] =
    useState(false);

  // Get username for routes
  const getUserRoute = () => `/${unwrappedParams.username}`;

  // Fetch document function - simplified and robust
  const fetchDocument = useCallback(async () => {
    try {
      // Fetch the document directly
      const doc = await getById(unwrappedParams.id);

      if (doc) {
        setDocument(doc);
        setError(null);

        // Track view activity
        try {
          await addDocumentActivity(doc.id, 'view');
        } catch (activityError) {
          console.error('Error tracking view activity:', activityError);
        }
      } else {
        setDocument(null);
        setError(new Error('Document not found'));
        toast.error('Document not found');
      }
    } catch (err) {
      console.error('Error fetching document:', err);
      setDocument(null);
      setError(
        err instanceof Error ? err : new Error('Failed to fetch document')
      );
      toast.error('Failed to load document');
    }
  }, [unwrappedParams.id, getById, addDocumentActivity]);

  // Fetch document on initial load
  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  // Handle share button click
  const handleShareClick = () => {
    setIsShareDialogOpen(true);
  };

  // Handle export button click
  const handleExportClick = () => {
    setIsExportDialogOpen(true);
  };

  // Handle sidebar toggle
  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Handle version history button click
  const handleVersionHistoryClick = () => {
    setIsVersionHistoryOpen(true);
  };

  // Handle create from template button click
  const handleCreateFromTemplateClick = () => {
    setIsCreateFromTemplateOpen(true);
  };

  // Handle version restore
  const handleRestoreVersion = async (version: any) => {
    if (!document) return;

    try {
      toast.success(`Document restored to version ${version.version}.`);
      setIsVersionHistoryOpen(false);

      // Refresh the document to show the restored version
      fetchDocument();
    } catch (err) {
      console.error('Error restoring document version:', err);
      toast.error('Failed to restore document version.');
    }
  };

  // Handle delete document
  const handleDeleteDocument = async () => {
    if (!document) return;

    if (confirm('Are you sure you want to delete this document?')) {
      try {
        // Create a promise for the delete operation
        const deletePromise = deleteDocument.mutate({ id: document.id });

        // Use toast.promise to show the loading, success, and error states
        toast.promise(deletePromise, {
          loading: 'Deleting document...',
          success: 'Document deleted successfully',
          error: 'Failed to delete document. Please try again.',
        });

        // Wait for the deletion to complete
        await deletePromise;

        // Navigate back to documents page
        router.push(`/${unwrappedParams.username}/documents`);
      } catch (err) {
        console.error('Error deleting document:', err);
        setError(
          err instanceof Error ? err : new Error('Failed to delete document')
        );
      }
    }
  };

  // Helper functions for UI
  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case 'contract':
        return <FileTextIcon className="h-5 w-5" />;
      case 'form':
        return <TemplateIcon className="h-5 w-5" />;
      default:
        return <FileTextIcon className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-200 text-gray-800';
      case 'published':
        return 'bg-green-200 text-green-800';
      case 'archived':
        return 'bg-amber-200 text-amber-800';
      case 'template':
        return 'bg-blue-200 text-blue-800';
      default:
        return 'bg-gray-200 text-gray-800';
    }
  };

  // Show error state if document failed to load
  if (error) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`${getUserRoute()}/documents`)}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Error Loading Document</h1>
        </div>

        <Card className="mb-4">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <p className="text-red-500">{error.message}</p>
              <div className="flex justify-center gap-4">
                <Button onClick={fetchDocument}>Retry</Button>
                <Button
                  variant="outline"
                  onClick={() => router.push(`${getUserRoute()}/documents`)}
                >
                  Back to Documents
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show loading state if document is not yet loaded
  if (!document) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`${getUserRoute()}/documents`)}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Loading Document...</h1>
        </div>
      </div>
    );
  }

  // Show document preview
  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`${getUserRoute()}/documents`)}
          >
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <div className="flex items-center">
            {getDocumentTypeIcon(document.document_type)}
            <h1 className="text-2xl font-bold ml-2">
              {document.title || 'Untitled Document'}
            </h1>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {document.is_template && (
            <Button variant="outline" onClick={handleCreateFromTemplateClick}>
              <CopyIcon className="mr-2 h-4 w-4" />
              Use Template
            </Button>
          )}

          <Button variant="outline" asChild>
            <a href={`${getUserRoute()}/documents/${document.id}/edit`}>
              <PencilIcon className="mr-2 h-4 w-4" />
              Edit
            </a>
          </Button>

          <Button variant="outline" onClick={handleShareClick}>
            <Share2Icon className="mr-2 h-4 w-4" />
            Share
          </Button>

          <Button variant="outline" onClick={handleExportClick}>
            <DownloadIcon className="mr-2 h-4 w-4" />
            Export
          </Button>

          <Button variant="outline" onClick={handleSidebarToggle}>
            <InfoIcon className="mr-2 h-4 w-4" />
            Details
          </Button>

          <Button variant="outline" onClick={handleVersionHistoryClick}>
            <History className="mr-2 h-4 w-4" />
            History
          </Button>

          <Button variant="outline" onClick={handleDeleteDocument}>
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Conditionally render sheets */}
      {document && isShareDialogOpen && (
        <DocumentShareSheet
          documentId={document.id}
          documentTitle={document.title}
          isOpen={isShareDialogOpen}
          onClose={() => setIsShareDialogOpen(false)}
        />
      )}

      {document && isExportDialogOpen && (
        <ExportSheet
          document={document}
          isOpen={isExportDialogOpen}
          onClose={() => setIsExportDialogOpen(false)}
        />
      )}

      {document && isVersionHistoryOpen && (
        <VersionHistorySheet
          documentId={document.id}
          currentVersion={document.version || 1}
          isOpen={isVersionHistoryOpen}
          onClose={() => setIsVersionHistoryOpen(false)}
          onRestoreVersion={handleRestoreVersion}
        />
      )}

      {document && isCreateFromTemplateOpen && (
        <CreateFromTemplateSheet
          templateId={document.id}
          templateTitle={document.title}
          isOpen={isCreateFromTemplateOpen}
          onClose={() => setIsCreateFromTemplateOpen(false)}
          onSuccess={(documentId: string) => {
            router.push(`/${unwrappedParams.username}/documents/${documentId}`);
          }}
        />
      )}

      {/* Right Sidebar Sheet */}
      {document && isSidebarOpen && (
        <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
          <div className="p-6 space-y-6 max-h-[calc(100vh-8rem)] overflow-y-auto pr-2 custom-scrollbar">
            <Sheet.Title>Document Details</Sheet.Title>

            <Suspense
              fallback={
                <div className="p-4">Loading collaboration panel...</div>
              }
            >
              <CollaborationPanel
                documentId={document.id}
                documentTitle={document.title}
                currentUserId={document.owner_id || 'current-user'}
              />
            </Suspense>

            <Suspense
              fallback={
                <div className="p-4">Loading consultation panel...</div>
              }
            >
              <LawyerConsultationPanel
                documentId={document.id}
                documentTitle={document.title}
              />
            </Suspense>

            <Card>
              <CardHeader>
                <CardTitle>Document Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                    <InfoIcon className="h-4 w-4 mr-2" />
                    Status
                  </h3>
                  <Badge className={getStatusColor(document.status)}>
                    {document.status}
                  </Badge>
                  {document.is_template && (
                    <Badge variant="outline" className="ml-2">
                      <TemplateIcon className="mr-1 h-3 w-3" />
                      Template
                    </Badge>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Created
                  </h3>
                  <p className="text-sm">
                    {format(new Date(document.created_at), 'PPP')} (
                    {formatDistanceToNow(new Date(document.created_at), {
                      addSuffix: true,
                    })}
                    )
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Last Updated
                  </h3>
                  <p className="text-sm">
                    {format(new Date(document.updated_at), 'PPP')} (
                    {formatDistanceToNow(new Date(document.updated_at), {
                      addSuffix: true,
                    })}
                    )
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" />
                    Owner
                  </h3>
                  <p className="text-sm">{document.owner_id}</p>
                </div>

                {document.template_id && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                      <TemplateIcon className="h-4 w-4 mr-2" />
                      Created From Template
                    </h3>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm"
                      asChild
                    >
                      <a
                        href={`${getUserRoute()}/documents/${document.template_id}`}
                      >
                        View Original Template
                      </a>
                    </Button>
                  </div>
                )}

                {document.version !== undefined && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                      <InfoIcon className="h-4 w-4 mr-2" />
                      Version
                    </h3>
                    <p className="text-sm">v{document.version}</p>
                  </div>
                )}

                {document.metadata &&
                  Object.keys(document.metadata).length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                        <InfoIcon className="h-4 w-4 mr-2" />
                        Metadata
                      </h3>
                      <div className="bg-muted p-2 rounded text-xs font-mono">
                        <pre>{JSON.stringify(document.metadata, null, 2)}</pre>
                      </div>
                    </div>
                  )}

                {document.shared_with &&
                  typeof document.shared_with === 'object' &&
                  Array.isArray(document.shared_with) &&
                  document.shared_with.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1 flex items-center">
                        <Share2Icon className="h-4 w-4 mr-2" />
                        Shared With
                      </h3>
                      <div className="space-y-2">
                        {(document.shared_with as any[]).map(
                          (user: any, index: number) => (
                            <p
                              key={user.id || `shared-user-${index}`}
                              className="text-sm"
                            >
                              {user.email || user.id || 'Unknown user'}
                            </p>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </CardContent>
            </Card>
          </div>
        </Sheet>
      )}

      <div className="mt-8">
        <div className="shadow-sm rounded-lg bg-background/80 backdrop-blur-sm">
          {document && (
            <div className="min-h-[600px] relative">
              <Suspense
                fallback={
                  <div className="min-h-[600px] flex items-center justify-center">
                    Loading preview...
                  </div>
                }
              >
                <DocumentPreview document={document} />
              </Suspense>
            </div>
          )}

          {/* Document Attachments */}
          {document && (
            <div className="mt-6">
              <DocumentAttachments documentId={document.id} />
            </div>
          )}

          {document && document.file_url && (
            <div className="mt-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-center border rounded-md p-8">
                    <div className="text-center">
                      <FileIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="font-medium">
                        {document.file_url.split('/').pop()}
                      </h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        {document.file_type} ·{' '}
                        {document.file_size
                          ? `${Math.round(document.file_size / 1024)} KB`
                          : 'Unknown size'}
                      </p>
                      <Button asChild>
                        <a
                          href={document.file_url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Download
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
