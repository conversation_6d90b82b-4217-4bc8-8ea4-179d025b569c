import { SVGProps } from "react";

export function Europe(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="213"
      height="213"
      fill="none"
      viewBox="0 0 213 213"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fill="#FFFFFF"
        d="M213 106.492a106.621 106.621 0 01-23.473 66.66 106.54 106.54 0 01-59.996 37.317 106.502 106.502 0 01-70.142-8.431 106.568 106.568 0 01-49.451-50.473A106.709 106.709 0 0122.004 41.698c3.5-4.554 7.357-8.823 11.534-12.765A106.445 106.445 0 01104.14.026a106.442 106.442 0 0171.807 25.76 106.37 106.37 0 0127.348 36.3A106.418 106.418 0 01213 106.492z"
      ></path>
      <path
        fill="url(#paint0_linear_5250_15504)"
        d="M213 106.49c.016 11.52-1.928 22.879-5.587 33.802-1.613-2.695-.672-10.337-.672-10.337a31.169 31.169 0 00-7.292 13.184c-2.322 8.392-4.097 9.99-4.839 9.457-.742-.532-.194-7.488-1.613-9.812-1.42-2.323.177-5.519-2.678-5.874-2.855-.355-1.613-5.003-6.759-5.358-5.146-.355-5.533-1.42-9.808-2.485-3.355-.823-4.178-4.405-4.388-5.922a.454.454 0 00-.085-.215.457.457 0 00-.404-.193.463.463 0 00-.22.069c-.952.645-2.549 2.356-2.033 6.455.71 5.713 1.613 6.584 4.63 5.164 3.016-1.42 6.065-2.856 7.501 2.324s-4.259 7.585-8.614 11.522c-4.356 3.938-11.421 5.358-12.486 2.663a42.998 42.998 0 00-6.049-10.328c-2.678-3.228-3.388-11.297-4.275-12.475-.887-1.178-6.598-.355-3.226 2.857 3.371 3.211 5.355 14.427 7.84 17.09 2.484 2.662 1.419 8.214 7.113 9.101 5.695.888 13.551-10.876 9.453-1.081-4.097 9.796-15.744 18.898-18.599 22.464-2.855 3.567.71 10.893-2.129 11.942-2.839 1.049-7.485 4.842-8.744 8.069-1.258 3.228-12.098 7.472-14.421 9.441-2.323 1.969-8.066.533-10.324-1.791-2.258-2.324-9.679-6.456-5.888-12.652 3.791-6.197 0-13.008-2.678-16.752-2.678-3.744 1.791-7.665-1.419-6.955-3.211.71-3.92-1.791-7.13-3.922-3.21-2.13-16.132 1.259-22.972 0-6.84-1.258-10.533-16.767-12.308-23.222-1.774-6.455 10.615-14.234 11.13-17.752.517-3.518 2.969-6.31 8.534-6.875 5.566-.564 20.31-4.05 23.633-3.728 3.323.323 9.679 10.845 13.018 10.926 3.339.081 3.226-2.098 4.597-3.647 1.371-1.55 11.696 3.857 14.341 2.759 2.646-1.097 4.839-.452 7.146-2.259 2.307-1.807 2.017-5.035 1.613-7.843-.403-2.808-2.097-.694-4.839.161a7.244 7.244 0 01-7.227-1.468c-1.855-1.711 0-3.034 1.097-6.149 1.097-3.114 3.872-2.017 6.453-3.808 2.581-1.792 7.694 2.324 11.888.548 4.195-1.775-5.516-4.357-6.759-7.31-1.242-2.953-8.823-3.647-11.292-.339-2.468 3.308-.709 3.228-1.613 5.89-.903 2.663.629 2.695-.468 3.89-1.096 1.194-1.903-.614-4.242.71-2.339 1.323.581 6.051-2.258 7.907-2.84 1.856-3.453-4.551-3.759-7.343-.307-2.791-3.049-4.502-5.178-5.729-2.13-1.226-5.743-7.536-8.663-5.18s1.775 4.019 2.92 5.616c1.145 1.598 4.92 2.824 6.549 3.357a2.132 2.132 0 011.613 2.437c0 .435-.903.355-1.839.258-.935-.097-2.21-.533-1.419 1.146.79 1.678-.178 2.259-.839 3.324s-1.855.662-1.387-1.242c.468-1.905-2.42-5.213-5.162-6.181-2.743-.969-4.033-6.681-6.453-6.262-2.42.42-2.758 1.469-6.452 1.759-.904 0-1.823-.436-1.79 0a.922.922 0 010 .452c-.598 4.938-3.227 5.229-5.534 6.843-2.307 1.613-1.613 4.502-3.5 6.455-1.888 1.952-16.132 3.227-11.793-3.922 4.34-7.149-3.048-6.971 2.05-9.683 5.097-2.71 10.34-.145 11.194-4.357.855-4.212-3.048-.452-4.645-4.841-1.597-4.39 7.42-5.987 10.372-7.585 2.952-1.598 1.855-5.52 4.63-4.648 2.774.872 4.678-2.856 2.677-5.164-2-2.308 2.694-6.778 3.856-5.325 1.919 2.436-1.436 4.438-.871 8.73.403 3.228 2.613 2.486 5.823 2.776 3.21.29 10.486-6.455 12.647-9.166 2.162-2.712 1.468-4.374 3.791-4.955 2.323-.58 5.098-3.647 3.339-5.1-1.758-1.452-4.517.872-7.694 1.614-3.178.743-2.485-1.162-3.065-4.212-.581-3.05 1.161-2.92 3.226-5.083 2.065-2.163 3.049-4.083.887-3.792a10.98 10.98 0 00-7.42 5.1c-2.485 3.469-1.613 5.066-.452 8.568.597 1.84 1.387 4.164.21 5.89-1.178 1.727-1.097 2.631-3.227 4.245-3.484 2.743-4.484-1.243-4.371-3.954.129-3.889-1.484-4.034-2.726-4.18-1.242-.145-3.049 2.34-5.775 3.228-4.372 1.356-2.597-3.954-3.388-7.165-.79-3.212 9.582-7.375 11.55-14.218A18.24 18.24 0 01116.905 36.5c4.323-1.388 8.776.726 11.841 1.114 6.452.806 9.678 9.682 13.227 9.682 3.549 0 14.98-7.55 17.222-12.489 1.613-3.599 14.781-8.741 16.694-9.08 11.616 10.006 21.087 22.381 27.466 36.326A106.42 106.42 0 01213 106.49z"
      ></path>
      <path
        fill="url(#paint1_linear_5250_15504)"
        d="M82.077 66.436c1.178-.403 3.033.856 3.227.856.193 0-.42 5.97 1.274 7.488 1.694 1.517 1.758 4.841 2.855 4.131s3.033-.42 2.613 1.275c-.419 1.694-4.549 2.727-5.79 3.324-1.243.598-3.888 1.808-4.84 2.47-.952.661 1.436-4.051 2.58-4.842.695-.452-1.612-1.259-1.612-2.695.105-1.152.36-2.286.758-3.373.113-.645-2.5-2.824-3.13-3.953-.629-1.13.872-4.245 2.065-4.68z"
      ></path>
      <path
        fill="url(#paint2_linear_5250_15504)"
        d="M77.706 75.038c.484-.371 1.194-1.065 1.92-.775.725.29 1.032 1.614.967 2.356-.064.743-.903 1.356-.903 2.228 0 .871.71 2.791-.42 3.114-1.129.323-3.113.355-3.532 1.13-.42.774-1.613.968-1.807.161a2.552 2.552 0 01.484-2.485c.742-.742.097-.984-.161-2.082a2.115 2.115 0 01.516-2.13c.452-.517.71.968 1.42.452.439-.705.947-1.364 1.516-1.969z"
      ></path>
      <path
        fill="url(#paint3_linear_5250_15504)"
        d="M154.588 17.698s-2.597 2.211-3.597 2.098c-1.001-.113-2.985 2.001-2.985 3.099 0 1.097.097 3.808 0 5.6-.096 1.79-.613 3.405 1.484 3.905s5.808 2.404 6.098 1.404c.29-1-4.017-2.808-3.855-6.1.161-3.292.564-5.907 2.661-6.794 2.097-.888 2.598-1.711 1.694-2.502-.903-.79-.048-1.807-1.5-.71z"
      ></path>
      <path
        fill="url(#paint4_linear_5250_15504)"
        d="M82.093 9.679c-2.161 3.84-9.566 8.876-10.776 12.91-1.21 4.035-3.064 14.654-9.678 18.753-6.614 4.099-19.261 8.407-22.149 14.41-2.887 6.004-2.145 11.297-4.307 10.555-2.161-.743-8.904-7.908-6.243-15.605 2.662-7.698 7.92-8.07 7.679-14.525-.21-6.245-2.904-7.213-3.226-7.294a106.169 106.169 0 0137.924-22.98s12.938-.081 10.776 3.776z"
      ></path>
      <path
        fill="url(#paint5_linear_5250_15504)"
        d="M16.035 73.1c0 2.162 6.727 6.455 3.614 10.667-3.114 4.212-9.679 1.34-10.566 6.148-.887 4.81 1.613 8.182-1.21 10.103A95.457 95.457 0 000 106.473a105.925 105.925 0 0122.003-64.826l.484-.21s1 6.697 1 7.65c0 .952-2.162 9.682-3.597 11.054-1.436 1.372-7.84 3.696-7.84 6.294 0 2.598 4.307-1.872 4.565 0s-.58 4.502-.58 6.665z"
      ></path>
      <path
        fill="url(#paint6_linear_5250_15504)"
        d="M60.558 49.877c-.532 1.372.775 3.599 0 4.583-.774.985-.21 1.614 2.097 1.453 2.307-.162 2.097 2.356 3.404 1.371a30.659 30.659 0 003.597-3.582c1.71-1.791 3.146-4.616 1.307-5.003-1.839-.387-3.404.597-4.936.597-1.533 0-3.581-1.049-4.307-.694a3.228 3.228 0 00-1.162 1.275z"
      ></path>
      <path
        fill="url(#paint7_linear_5250_15504)"
        d="M42.265 184.906c.755 2.118.84 4.417.242 6.585h-.145a106.73 106.73 0 01-32.424-39.926 3.613 3.613 0 00-.34-1.436.31.31 0 11.485-.387 72.501 72.501 0 007.017 6.455c3.13 2.308 6.92 2.937 8.388 8.166 1.468 5.228 2.5 3.986 6.695 5.035 4.194 1.049 12.808 2.727 12.808 7.552 0 4.826-3.904 4.18-2.726 7.956z"
      ></path>
      <path
        fill="url(#paint8_linear_5250_15504)"
        d="M152.604 188.794s6.791-2.146 7.517-3.825c.726-1.678 2.242-.322.871 1.905a44.98 44.98 0 01-5.824 6.326s-3.113 4.712-4.952 4.228 1.113-3.115 1.436-5.358c.322-2.243-.242-2.066.952-3.276z"
      ></path>
      <defs>
        <linearGradient
          id="paint0_linear_5250_15504"
          x1="61.723"
          x2="213"
          y1="117.334"
          y2="117.334"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_5250_15504"
          x1="79.864"
          x2="92.105"
          y1="76.2"
          y2="76.2"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_5250_15504"
          x1="73.796"
          x2="80.602"
          y1="78.983"
          y2="78.983"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_5250_15504"
          x1="147.88"
          x2="156.518"
          y1="25.654"
          y2="25.654"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint4_linear_5250_15504"
          x1="28.328"
          x2="82.337"
          y1="36.139"
          y2="36.139"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_5250_15504"
          x1="0.001"
          x2="23.488"
          y1="73.955"
          y2="73.955"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_5250_15504"
          x1="60.232"
          x2="71.822"
          y1="53.021"
          y2="53.021"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_5250_15504"
          x1="9.531"
          x2="44.991"
          y1="170.558"
          y2="170.558"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
        <linearGradient
          id="paint8_linear_5250_15504"
          x1="149.617"
          x2="161.546"
          y1="190.841"
          y2="190.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#085078"></stop>
          <stop offset="1" stopColor="#85D8CE"></stop>
        </linearGradient>
      </defs>
    </svg>
  );
}
