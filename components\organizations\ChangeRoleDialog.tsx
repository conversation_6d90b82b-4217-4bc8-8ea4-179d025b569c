'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOrganizations } from '@/lib/hooks';
import { OrganizationMember } from '@/lib/types/database-modules';
import { zodResolver } from '@hookform/resolvers/zod';
import { Crown, Loader2, Shield, User, UserCog } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

const changeRoleSchema = z.object({
  role: z.enum(['owner', 'admin', 'member']),
});

type ChangeRoleFormValues = z.infer<typeof changeRoleSchema>;
type OrganizationRole = 'owner' | 'admin' | 'member';

interface ChangeRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: OrganizationMember;
  onRoleChange: (role: OrganizationRole) => void;
}

export function ChangeRoleDialog({
  open,
  onOpenChange,
  member,
  onRoleChange,
}: ChangeRoleDialogProps) {
  const { updateMemberRole } = useOrganizations();
  const [isUpdating, setIsUpdating] = useState(false);

  const form = useForm<ChangeRoleFormValues>({
    resolver: zodResolver(changeRoleSchema),
    defaultValues: {
      role: member.role,
    },
  });

  async function onSubmit(values: ChangeRoleFormValues) {
    setIsUpdating(true);

    try {
      // Create a promise for the role update process
      const updateRolePromise = (async () => {
        const success = await updateMemberRole(member.id, values.role);

        if (!success) {
          throw new Error('Failed to update role');
        }

        // Call the onRoleChange callback
        onRoleChange(values.role);

        // Close the dialog
        onOpenChange(false);

        return success;
      })();

      // Use toast.promise to handle loading, success, and error states
      toast.promise(updateRolePromise, {
        loading: 'Updating role...',
        success: () => `Role updated to ${values.role}`,
        error: 'Failed to update role',
      });

      await updateRolePromise;
    } catch (error) {
      console.error('Error updating role:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsUpdating(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Change Member Role
          </DialogTitle>
          <DialogDescription>
            Update the role for {member.user?.full_name || 'this member'}.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="space-y-3"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="owner" />
                        </FormControl>
                        <FormLabel className="font-normal flex items-center gap-2">
                          <Crown className="h-4 w-4 text-yellow-600" />
                          <div>
                            <div>Owner</div>
                            <p className="text-xs text-muted-foreground">
                              Full control over the organization, can delete it
                              and manage billing
                            </p>
                          </div>
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="admin" />
                        </FormControl>
                        <FormLabel className="font-normal flex items-center gap-2">
                          <Shield className="h-4 w-4 text-blue-600" />
                          <div>
                            <div>Admin</div>
                            <p className="text-xs text-muted-foreground">
                              Can manage members, teams, and settings, but
                              cannot delete the organization
                            </p>
                          </div>
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="member" />
                        </FormControl>
                        <FormLabel className="font-normal flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-600" />
                          <div>
                            <div>Member</div>
                            <p className="text-xs text-muted-foreground">
                              Can view and use organization resources, but
                              cannot manage settings
                            </p>
                          </div>
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormDescription>
                    Choose the appropriate role for this member
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdating}>
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Update Role'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
