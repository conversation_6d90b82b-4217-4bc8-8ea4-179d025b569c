'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useCollaboration } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { supabase } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import {
  FileText,
  Loader2,
  MessageSquare,
  Paperclip,
  Send,
  Smile,
  User,
  Users,
} from 'lucide-react';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

interface Message {
  id: string;
  content: string;
  sender_id: string;
  sender_name: string;
  sender_avatar: string;
  project_id: string | null;
  created_at: string;
  is_system_message: boolean;
}

interface ChatRoom {
  id: string;
  name: string;
  type: 'project' | 'direct' | 'group';
  last_message?: string;
  last_message_time?: string;
  unread_count: number;
  participants?: {
    id: string;
    name: string;
    avatar: string;
    online?: boolean;
  }[];
}

export default function MessagesPage() {
  const params = useParams();
  const username = params.username as string;
  const { profile } = userStore();
  const {
    projects,
    // We don't need projectsLoading as we're using isLoadingRooms instead
    fetchProjects,
  } = useCollaboration();
  const [activeTab, setActiveTab] = useState('projects');
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(null);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  useEffect(() => {
    if (projects.length > 0) {
      generateChatRooms();
    }
  }, [projects]);

  useEffect(() => {
    if (selectedRoom) {
      fetchMessages(selectedRoom.id);
    }
  }, [selectedRoom]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Generate sample chat rooms based on projects and team members
  const generateChatRooms = () => {
    setIsLoadingRooms(true);

    try {
      const projectRooms: ChatRoom[] = projects.map((project) => ({
        id: project.id,
        name: project.name,
        type: 'project',
        last_message: 'Latest project update...',
        last_message_time: new Date().toISOString(),
        unread_count: Math.floor(Math.random() * 5),
      }));

      // Generate some direct message rooms
      const directRooms: ChatRoom[] = [
        {
          id: 'dm-1',
          name: 'John Doe',
          type: 'direct',
          last_message: 'Can you review the document?',
          last_message_time: new Date().toISOString(),
          unread_count: 2,
          participants: [
            {
              id: 'user-1',
              name: 'John Doe',
              avatar:
                'https://ui-avatars.com/api/?name=John+Doe&background=0D8ABC&color=fff',
              online: true,
            },
          ],
        },
        {
          id: 'dm-2',
          name: 'Jane Smith',
          type: 'direct',
          last_message: 'Meeting scheduled for tomorrow',
          last_message_time: new Date(Date.now() - 3600000).toISOString(),
          unread_count: 0,
          participants: [
            {
              id: 'user-2',
              name: 'Jane Smith',
              avatar:
                'https://ui-avatars.com/api/?name=Jane+Smith&background=0D8ABC&color=fff',
              online: false,
            },
          ],
        },
      ];

      // Generate some group chat rooms
      const groupRooms: ChatRoom[] = [
        {
          id: 'group-1',
          name: 'Legal Team',
          type: 'group',
          last_message: "Let's discuss the contract revisions",
          last_message_time: new Date(Date.now() - 7200000).toISOString(),
          unread_count: 5,
          participants: [
            {
              id: 'user-1',
              name: 'John Doe',
              avatar:
                'https://ui-avatars.com/api/?name=John+Doe&background=0D8ABC&color=fff',
              online: true,
            },
            {
              id: 'user-2',
              name: 'Jane Smith',
              avatar:
                'https://ui-avatars.com/api/?name=Jane+Smith&background=0D8ABC&color=fff',
              online: false,
            },
            {
              id: 'user-3',
              name: 'Robert Johnson',
              avatar:
                'https://ui-avatars.com/api/?name=Robert+Johnson&background=0D8ABC&color=fff',
              online: true,
            },
          ],
        },
        {
          id: 'group-2',
          name: 'Document Review',
          type: 'group',
          last_message: "I've shared the latest draft",
          last_message_time: new Date(Date.now() - 86400000).toISOString(),
          unread_count: 0,
          participants: [
            {
              id: 'user-2',
              name: 'Jane Smith',
              avatar:
                'https://ui-avatars.com/api/?name=Jane+Smith&background=0D8ABC&color=fff',
              online: false,
            },
            {
              id: 'user-4',
              name: 'Emily Davis',
              avatar:
                'https://ui-avatars.com/api/?name=Emily+Davis&background=0D8ABC&color=fff',
              online: true,
            },
          ],
        },
      ];

      setChatRooms([...projectRooms, ...directRooms, ...groupRooms]);

      // Set the first room as selected by default if no room is selected
      if (!selectedRoom && projectRooms.length > 0) {
        setSelectedRoom(projectRooms[0]);
      }
    } catch (error) {
      console.error('Error generating chat rooms:', error);
    } finally {
      setIsLoadingRooms(false);
    }
  };

  // Fetch messages for a chat room
  const fetchMessages = async (roomId: string) => {
    setIsLoadingMessages(true);

    try {
      // In a real app, we would fetch messages from the database
      // For now, we'll generate some sample messages

      // Check if we have project messages in the database
      const { data: projectMessages, error } = await supabase
        .from('project_messages')
        .select('*')
        .eq('project_id', roomId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching project messages:', error);
        // Generate sample messages if we can't fetch from the database
        generateSampleMessages(roomId);
        return;
      }

      if (projectMessages && projectMessages.length > 0) {
        // Format messages from the database
        const formattedMessages: Message[] = await Promise.all(
          projectMessages.map(async (msg) => {
            // Get sender profile
            const { data: senderData } = await supabase
              .from('profiles')
              .select('full_name, avatar_url')
              .eq('id', msg.user_id)
              .single();

            return {
              id: msg.id,
              content: msg.content,
              sender_id: msg.user_id,
              sender_name: senderData?.full_name || 'Unknown User',
              sender_avatar:
                senderData?.avatar_url ||
                `https://ui-avatars.com/api/?name=Unknown+User&background=0D8ABC&color=fff`,
              project_id: msg.project_id,
              created_at: msg.created_at,
              is_system_message: false,
            };
          })
        );

        setMessages(formattedMessages);
      } else {
        // Generate sample messages if no messages in the database
        generateSampleMessages(roomId);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      // Generate sample messages as fallback
      generateSampleMessages(roomId);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  // Generate sample messages for a chat room
  const generateSampleMessages = (roomId: string) => {
    const room = chatRooms.find((room) => room.id === roomId);
    if (!room) return;

    const sampleMessages: Message[] = [];
    const messageCount = 10 + Math.floor(Math.random() * 15); // 10-25 messages

    // System message at the beginning
    sampleMessages.push({
      id: `system-${roomId}-1`,
      content: `Welcome to the ${room.name} chat room!`,
      sender_id: 'system',
      sender_name: 'System',
      sender_avatar: '',
      project_id: room.type === 'project' ? roomId : null,
      created_at: new Date(Date.now() - 86400000 * 7).toISOString(), // 7 days ago
      is_system_message: true,
    });

    // Generate random messages
    for (let i = 0; i < messageCount; i++) {
      const isCurrentUser = Math.random() > 0.6; // 40% chance it's from the current user
      const timestamp = new Date(
        Date.now() - ((messageCount - i) * 3600000) / messageCount
      ).toISOString();

      let senderId, senderName, senderAvatar;

      if (isCurrentUser) {
        senderId = profile?.id || 'current-user';
        senderName = profile?.full_name || username;
        senderAvatar =
          profile?.avatar_url ||
          `https://ui-avatars.com/api/?name=${encodeURIComponent(senderName)}&background=0D8ABC&color=fff`;
      } else if (room.participants && room.participants.length > 0) {
        // Pick a random participant
        const randomParticipant =
          room.participants[
            Math.floor(Math.random() * room.participants.length)
          ];
        senderId = randomParticipant.id;
        senderName = randomParticipant.name;
        senderAvatar = randomParticipant.avatar;
      } else {
        // Fallback for project rooms without explicit participants
        senderId = `user-${Math.floor(Math.random() * 5) + 1}`;
        senderName = [
          'John Doe',
          'Jane Smith',
          'Robert Johnson',
          'Emily Davis',
          'Michael Brown',
        ][Math.floor(Math.random() * 5)];
        senderAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(senderName)}&background=0D8ABC&color=fff`;
      }

      // Generate message content based on room type
      let content = '';
      if (room.type === 'project') {
        const projectMessages = [
          "I've updated the project timeline.",
          'Can someone review the latest document?',
          'Meeting scheduled for tomorrow at 10 AM.',
          'The client approved the latest changes.',
          'We need to address the feedback from the legal team.',
          "I've shared the updated contract draft.",
          "Let's discuss the project scope in our next meeting.",
          'Has anyone contacted the client about the deadline extension?',
          'The new requirements look good to me.',
          "I've added comments to the document for review.",
        ];
        content =
          projectMessages[Math.floor(Math.random() * projectMessages.length)];
      } else if (room.type === 'direct') {
        const directMessages = [
          'Can you review this document for me?',
          'Are you available for a quick call?',
          "I've sent you the information you requested.",
          "Let me know when you're free to discuss.",
          'Thanks for your help with the project.',
          "Did you see the client's feedback?",
          "I'll be out of office tomorrow.",
          'Can we reschedule our meeting?',
          'I need your input on this issue.',
          'Have you had a chance to look at the draft?',
        ];
        content =
          directMessages[Math.floor(Math.random() * directMessages.length)];
      } else {
        const groupMessages = [
          "Let's coordinate on this task.",
          "Who's handling the client meeting?",
          "I've shared the document with everyone.",
          "Can we get everyone's input on this?",
          "Team update: we're on track for the deadline.",
          "Let's brainstorm solutions for this issue.",
          'Everyone please review the latest draft.',
          'Good work on the presentation, team!',
          'We need to finalize this by Friday.',
          'Any volunteers for the client demo?',
        ];
        content =
          groupMessages[Math.floor(Math.random() * groupMessages.length)];
      }

      sampleMessages.push({
        id: `msg-${roomId}-${i}`,
        content,
        sender_id: senderId,
        sender_name: senderName,
        sender_avatar: senderAvatar,
        project_id: room.type === 'project' ? roomId : null,
        created_at: timestamp,
        is_system_message: false,
      });
    }

    setMessages(sampleMessages);
  };

  // Send a new message
  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedRoom || !profile) return;

    setIsSendingMessage(true);

    try {
      const messageData = {
        content: newMessage.trim(),
        user_id: profile.id,
        project_id: selectedRoom.type === 'project' ? selectedRoom.id : null,
        created_at: new Date().toISOString(),
      };

      // If it's a project chat, try to save to the database
      if (selectedRoom.type === 'project') {
        const { data, error } = await supabase
          .from('project_messages')
          .insert(messageData)
          .select()
          .single();

        if (error) {
          console.error('Error sending message:', error);
          toast.error('Failed to send message');
          return;
        }

        if (data) {
          // Add the new message to the messages list
          const newMsg: Message = {
            id: data.id,
            content: data.content,
            sender_id: profile.id,
            sender_name: profile.full_name || username,
            sender_avatar:
              profile.avatar_url ||
              `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.full_name || username)}&background=0D8ABC&color=fff`,
            project_id: data.project_id,
            created_at: data.created_at,
            is_system_message: false,
          };

          setMessages((prev) => [...prev, newMsg]);
          setNewMessage('');
          return;
        }
      }

      // For non-project chats or if database save failed, just add to local state
      const newMsg: Message = {
        id: `msg-${Date.now()}`,
        content: newMessage.trim(),
        sender_id: profile.id,
        sender_name: profile.full_name || username,
        sender_avatar:
          profile.avatar_url ||
          `https://ui-avatars.com/api/?name=${encodeURIComponent(profile.full_name || username)}&background=0D8ABC&color=fff`,
        project_id: selectedRoom.type === 'project' ? selectedRoom.id : null,
        created_at: new Date().toISOString(),
        is_system_message: false,
      };

      setMessages((prev) => [...prev, newMsg]);
      setNewMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsSendingMessage(false);
    }
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      // Today, show time
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffDays === 1) {
      // Yesterday
      return 'Yesterday';
    } else if (diffDays < 7) {
      // Within a week, show day name
      return date.toLocaleDateString([], { weekday: 'long' });
    } else {
      // Older, show date
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Filter chat rooms based on active tab
  const filteredRooms = chatRooms.filter((room) => {
    if (activeTab === 'projects') return room.type === 'project';
    if (activeTab === 'direct') return room.type === 'direct';
    if (activeTab === 'groups') return room.type === 'group';
    return true;
  });
  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <MessageSquare className="size-5 text-neutral-500" />
        <h1 className="text-2xl font-bold">Team Chat</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-12rem)]">
        {/* Chat rooms sidebar */}
        <div className="lg:col-span-1 border rounded-lg overflow-hidden flex flex-col">
          <div className="p-4 border-b">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full">
                <TabsTrigger value="projects" className="flex-1">
                  Projects
                </TabsTrigger>
                <TabsTrigger value="direct" className="flex-1">
                  Direct
                </TabsTrigger>
                <TabsTrigger value="groups" className="flex-1">
                  Groups
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex-1 overflow-y-auto">
            {isLoadingRooms ? (
              <div className="flex items-center justify-center py-10">
                <Loader2 className="size-8 text-neutral-500 animate-spin" />
              </div>
            ) : filteredRooms.length === 0 ? (
              <div className="text-center py-10">
                <p className="text-neutral-500">No chat rooms found</p>
              </div>
            ) : (
              <div className="divide-y">
                {filteredRooms.map((room) => (
                  <div
                    key={room.id}
                    className={cn(
                      'p-3 cursor-pointer hover:bg-neutral-50 transition-colors',
                      selectedRoom?.id === room.id && 'bg-neutral-100'
                    )}
                    onClick={() => setSelectedRoom(room)}
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        {room.type === 'project' ? (
                          <FileText className="size-10 text-neutral-500 p-2 bg-neutral-100 rounded-full" />
                        ) : room.type === 'direct' ? (
                          <User className="size-10 text-neutral-500 p-2 bg-neutral-100 rounded-full" />
                        ) : (
                          <Users className="size-10 text-neutral-500 p-2 bg-neutral-100 rounded-full" />
                        )}

                        {room.unread_count > 0 && (
                          <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full size-5 flex items-center justify-center">
                            {room.unread_count}
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="font-medium truncate">
                            {room.name}
                          </div>
                          {room.last_message_time && (
                            <div className="text-xs text-neutral-500">
                              {formatTimestamp(room.last_message_time)}
                            </div>
                          )}
                        </div>
                        {room.last_message && (
                          <div className="text-sm text-neutral-500 truncate">
                            {room.last_message}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Chat messages */}
        <div className="lg:col-span-3 border rounded-lg overflow-hidden flex flex-col">
          {selectedRoom ? (
            <>
              <div className="p-4 border-b flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {selectedRoom.type === 'project' ? (
                    <FileText className="size-5 text-neutral-500" />
                  ) : selectedRoom.type === 'direct' ? (
                    <User className="size-5 text-neutral-500" />
                  ) : (
                    <Users className="size-5 text-neutral-500" />
                  )}
                  <h2 className="font-medium">{selectedRoom.name}</h2>
                </div>

                {selectedRoom.type === 'direct' ||
                selectedRoom.type === 'group' ? (
                  <div className="flex items-center gap-1">
                    {selectedRoom.participants?.map((participant, index) => (
                      <div
                        key={participant.id}
                        className="relative"
                        style={{ marginLeft: index > 0 ? '-8px' : '0' }}
                      >
                        <div
                          className="size-8 rounded-full bg-cover bg-center border-2 border-white"
                          style={{
                            backgroundImage: `url(${participant.avatar})`,
                          }}
                        ></div>
                        {participant.online && (
                          <div className="absolute bottom-0 right-0 size-2 bg-green-500 rounded-full border border-white"></div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : null}
              </div>

              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {isLoadingMessages ? (
                  <div className="flex items-center justify-center py-10">
                    <Loader2 className="size-8 text-neutral-500 animate-spin" />
                  </div>
                ) : messages.length === 0 ? (
                  <div className="text-center py-10">
                    <MessageSquare className="size-12 text-neutral-300 mx-auto mb-4" />
                    <p className="text-neutral-500">No messages yet</p>
                    <p className="text-sm text-neutral-400">
                      Be the first to send a message!
                    </p>
                  </div>
                ) : (
                  <>
                    {messages.map((message, index) => {
                      const isCurrentUser = message.sender_id === profile?.id;
                      const showAvatar =
                        !isCurrentUser &&
                        (!index ||
                          messages[index - 1].sender_id !== message.sender_id);

                      // Check if we need to show a date separator
                      const showDateSeparator =
                        index === 0 ||
                        new Date(message.created_at).toDateString() !==
                          new Date(
                            messages[index - 1].created_at
                          ).toDateString();

                      return (
                        <div key={message.id}>
                          {showDateSeparator && (
                            <div className="flex items-center justify-center my-4">
                              <div className="text-xs text-neutral-500 bg-white px-2">
                                {new Date(
                                  message.created_at
                                ).toLocaleDateString([], {
                                  weekday: 'long',
                                  month: 'long',
                                  day: 'numeric',
                                })}
                              </div>
                              <div className="flex-1 h-px bg-neutral-200"></div>
                            </div>
                          )}

                          {message.is_system_message ? (
                            <div className="flex items-center justify-center my-4">
                              <div className="text-xs text-neutral-500 bg-neutral-100 px-3 py-1 rounded-full">
                                {message.content}
                              </div>
                            </div>
                          ) : (
                            <div
                              className={cn(
                                'flex gap-3',
                                isCurrentUser && 'flex-row-reverse'
                              )}
                            >
                              {showAvatar ? (
                                <div
                                  className="size-8 rounded-full bg-cover bg-center flex-shrink-0"
                                  style={{
                                    backgroundImage: `url(${message.sender_avatar})`,
                                  }}
                                ></div>
                              ) : (
                                <div className="size-8 flex-shrink-0"></div>
                              )}

                              <div
                                className={cn(
                                  'max-w-[70%]',
                                  isCurrentUser ? 'items-end' : 'items-start'
                                )}
                              >
                                {showAvatar && !isCurrentUser && (
                                  <div className="text-sm font-medium mb-1">
                                    {message.sender_name}
                                  </div>
                                )}

                                <div
                                  className={cn(
                                    'px-3 py-2 rounded-lg',
                                    isCurrentUser
                                      ? 'bg-blue-500 text-white rounded-tr-none'
                                      : 'bg-neutral-100 text-neutral-800 rounded-tl-none'
                                  )}
                                >
                                  {message.content}
                                </div>

                                <div className="text-xs text-neutral-500 mt-1">
                                  {new Date(
                                    message.created_at
                                  ).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                    <div ref={messagesEndRef} />
                  </>
                )}
              </div>

              <div className="p-4 border-t">
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon" type="button">
                    <Smile className="size-5" />
                  </Button>
                  <Button variant="outline" size="icon" type="button">
                    <Paperclip className="size-5" />
                  </Button>

                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                      }
                    }}
                    disabled={isSendingMessage}
                    className="flex-1"
                  />

                  <Button
                    type="button"
                    onClick={sendMessage}
                    disabled={!newMessage.trim() || isSendingMessage}
                  >
                    {isSendingMessage ? (
                      <Loader2 className="size-5 animate-spin" />
                    ) : (
                      <Send className="size-5" />
                    )}
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageSquare className="size-12 text-neutral-300 mx-auto mb-4" />
                <p className="text-neutral-500">
                  Select a chat to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
