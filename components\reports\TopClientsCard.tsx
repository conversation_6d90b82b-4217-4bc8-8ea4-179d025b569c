'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { UserAvatar } from '@/components/ui/user-avatar';
import { ClientData } from '@/lib/types/database-modules';
import { AlertCircle, Users } from 'lucide-react';

interface TopClientsCardProps {
  data: ClientData[] | null;
  loading?: boolean;
}

export function TopClientsCard({ data, loading = false }: TopClientsCardProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between py-2 border-b"
            >
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Top Clients</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No client data available</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate the maximum count for the progress bars
  const maxCount = Math.max(...data.map((client) => client.consultation_count));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Top Clients</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((client, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">{client.client_name}</span>
              </div>
              <span className="text-sm font-medium">
                {client.consultation_count} consultations
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div
                className="bg-primary h-full rounded-full"
                style={{
                  width: `${(client.consultation_count / maxCount) * 100}%`,
                }}
              ></div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
