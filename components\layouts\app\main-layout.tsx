'use client';

import { useState, useEffect } from 'react';
import { AppSidebar } from '@/components/layouts/app/sidebar/app-sidebar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SidebarProvider } from '@/components/ui/sidebar';
import { MobileLayout } from '@/components/mobile/MobileLayout';

interface MainLayoutProps {
  children: React.ReactNode;
  header: React.ReactNode;
}

export default function MainLayout({ children, header }: MainLayoutProps) {
  const [isMobile, setIsMobile] = useState(false);

  // Check if the device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIsMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIsMobile);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // If mobile, use the mobile layout
  if (isMobile) {
    return <MobileLayout>{children}</MobileLayout>;
  }

  // Otherwise, use the desktop layout
  return (
    <SidebarProvider>
      <div className="flex h-screen w-full overflow-hidden">
        <AppSidebar />
        <div className="flex-1 flex flex-col h-screen lg:p-2 overflow-hidden">
          <div className="flex-1 flex flex-col h-full w-full bg-neutral-50 lg:rounded-xl lg:border border-neutral-200 overflow-hidden">
            <div className="flex-shrink-0">{header}</div>
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full w-full">
                <div className="p-4">{children}</div>
              </ScrollArea>
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
