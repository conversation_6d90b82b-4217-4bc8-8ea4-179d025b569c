'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { NameType } from '@/types';
import { Image, ImageMinus } from 'lucide-react';
import { ChangeEvent, useRef, useState } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import BaseButton from '../BaseButton';

type FormFileProps = {
  name: NameType;
  label?: string;
  placeholder?: string;
};

const FormFile = ({ name, label, placeholder }: FormFileProps) => {
  const { control, setValue } = useFormContext();

  const logoImage = useWatch({
    name: name,
    control,
  });

  const [base64Image, setBase64Image] = useState<string>(logoImage ?? '');
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files![0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const base64String = event.target!.result as string;
        setBase64Image(base64String);
        setValue(name, base64String); // Set the value for form submission
      };
      reader.readAsDataURL(file);
    }
  };

  const removeLogo = () => {
    setBase64Image('');
    setValue(name, '');

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <div className="flex flex-col items-start space-y-1">
              <Label className="ml-2 text-sm text-neutral-400">{label}</Label>
              {base64Image ? (
                <img
                  id="logoImage"
                  src={base64Image}
                  style={{
                    objectFit: 'contain',
                    width: '10rem',
                    height: '7rem',
                  }}
                />
              ) : (
                <div
                  style={{
                    objectFit: 'contain',
                    width: '10rem',
                    height: '7rem',
                  }}
                >
                  <Label
                    htmlFor={name}
                    className="flex h-[7rem] w-[10rem] cursor-pointer items-center justify-center rounded-md border border-neutral-400 bg-gray-100 hover:border-blue-500 dark:border-white dark:bg-slate-800"
                  >
                    <>
                      <div className="flex flex-col items-center space-y-4">
                        <Image className="size-5 text-neutral-400" />
                        <p className="text-neutral-400">{placeholder}</p>
                      </div>
                      <FormControl>
                        <input
                          ref={fileInputRef}
                          type="file"
                          id={name}
                          className="hidden"
                          onChange={handleFileChange}
                          accept="image/*"
                        />
                      </FormControl>
                      <FormMessage />
                    </>
                  </Label>
                </div>
              )}
            </div>
          </FormItem>
        )}
      />
      {base64Image && (
        <div>
          <BaseButton variant="shadow_red" onClick={removeLogo}>
            <ImageMinus />
            Remove logo
          </BaseButton>
        </div>
      )}
    </>
  );
};

export default FormFile;
