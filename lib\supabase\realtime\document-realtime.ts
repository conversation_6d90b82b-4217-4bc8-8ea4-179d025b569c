'use client';

import { supabaseClient } from '@/lib/supabase/client';
import { Document, DocumentSummary } from '../database-types';
import RealtimeService from './realtime-service';

/**
 * Subscribe to changes on a specific document
 */
export function subscribeToDocument(
  documentId: string,
  callback: (document: Document) => void
): () => void {
  return RealtimeService.subscribeToDocument(documentId, async (payload) => {
    // When we receive a broadcast event, fetch the full document
    const { data, error } = await supabaseClient
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (error) {
      console.error('Error fetching document:', error);
      return;
    }

    if (data) {
      callback(data as unknown as Document);
    }
  });
}

/**
 * Subscribe to changes on all documents for a user
 */
export function subscribeToUserDocuments(
  userId: string,
  callback: (documents: DocumentSummary[]) => void
): () => void {
  // Create a custom channel for user documents
  const channel = supabaseClient
    .channel(`user-documents-${userId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'documents',
        filter: `owner_id=eq.${userId}`,
      },
      async () => {
        // Fetch all documents for the user
        const { data, error } = await supabaseClient
          .from('document_summaries_view')
          .select('*')
          .eq('owner_id', userId)
          .order('updated_at', { ascending: false });

        if (error) {
          console.error('Error fetching documents:', error);
          return;
        }

        if (data) {
          callback(data as unknown as DocumentSummary[]);
        }
      }
    )
    .subscribe();

  // Return unsubscribe function
  return () => {
    supabaseClient.removeChannel(channel);
  };
}

/**
 * Subscribe to changes on shared documents for a user
 */
export function subscribeToSharedDocuments(
  userId: string,
  callback: (documents: DocumentSummary[]) => void
): () => void {
  // Subscribe to document sharing changes for this user
  return RealtimeService.subscribeToDocumentSharingAsUser(userId, async () => {
    // When we receive a broadcast event, fetch all shared documents
    const { data, error } = await supabaseClient
      .from('document_shares')
      .select(
        `
          id,
          document_id,
          can_edit,
          created_at,
          document:documents(
            id,
            title,
            description,
            document_type,
            status,
            owner_id,
            created_at,
            updated_at,
            thumbnail_url
          )
        `
      )
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching shared documents:', error);
      return;
    }

    if (data && data.length > 0) {
      // Transform the data to match the DocumentSummary interface
      const sharedDocuments = data.map((share) => ({
        id: share.document.id,
        title: share.document.title,
        description: share.document.description,
        document_type: share.document.document_type,
        status: share.document.status,
        owner_id: share.document.owner_id,
        created_at: share.document.created_at,
        updated_at: share.document.updated_at,
        thumbnail_url: share.document.thumbnail_url,
        is_shared: true,
        can_edit: share.can_edit,
      }));

      callback(sharedDocuments as unknown as DocumentSummary[]);
    } else {
      callback([]);
    }
  });
}

/**
 * Cleanup all active subscriptions
 */
export function cleanupAllSubscriptions(): void {
  RealtimeService.unsubscribeAll();
}
