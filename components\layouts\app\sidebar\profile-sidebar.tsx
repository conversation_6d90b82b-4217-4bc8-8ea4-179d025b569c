'use client';
import { DoorOpenIcon, HomeIcon, Cog } from 'lucide-react';

import { Button, buttonVariants } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { userStore } from '@/lib/store/user';
import { useEffect } from 'react';

import { authService } from '@/lib/supabase/auth/auth-service';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  FONT_BIRCOLAGE_GROTESQUE,
  FONT_GEIST_SANS,
  FONT_JETBRAINS_MONO,
} from '@/lib/constants';

export function Profile() {
  const router = useRouter();
  const { profile, user, removeProfile, removeUser } = userStore();

  // Log profile data when it changes
  useEffect(() => {
    if (profile) {
      console.log('Profile data in sidebar:', profile);
    }
  }, [profile]);

  if (!profile || !user) return null;

  async function handleLogOut() {
    try {
      await authService.signOut();
      removeProfile(null);
      removeUser(null);
      router.refresh();
      return 'Done';
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={'ghost'}
          aria-label="Open account menu"
          className="w-full px-2 h-fit space-x-2 justify-between flex items-center"
        >
          <div>
            <p
              className={cn(
                buttonVariants({
                  size: 'sm',
                  variant:
                    profile?.role === 'lawyer' ? 'shadow_accent' : 'default',
                }),
                FONT_JETBRAINS_MONO.className,
                'font-bold uppercase text-xs rounded-md h-6 px-2'
              )}
            >
              {profile?.role === 'lawyer' ? 'Lawyer' : 'User'}
            </p>
          </div>
          <div
            className={cn(
              buttonVariants({ size: 'sm', variant: 'shadow' }),
              'p-1 h-fit'
            )}
          >
            <UserAvatar
              size="sm"
              avatarUrl={profile?.avatar_url}
              fallbackText={profile?.full_name || ''}
            />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className={cn(FONT_GEIST_SANS.className, 'max-w-60 w-48 rounded-2xl')}
      >
        <DropdownMenuLabel className="flex flex-col gap-3 pt-2">
          <div className="flex min-w-0 flex-col">
            <span className="text-neutral-950 truncate text-lg font-medium">
              {profile?.full_name}
            </span>
            <span
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-neutral-500 truncate text-xs font-normal'
              )}
            >
              {profile?.email}
            </span>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link
              href={`/`}
              className="rounded-xl w-full flex items-center  transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit"
            >
              <HomeIcon size={16} className="opacity-60" />
              <span className="text-sm">Home</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link
              href={`/${profile?.username}/settings`}
              className="rounded-xl w-full flex items-center  transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit"
            >
              <Cog size={16} className="opacity-60" />
              <span className="text-sm">Settings</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className={cn(
            'rounded-xl w-full flex items-center shadow-default *:text-red-900 hover:text-red-900 transition-all duration-200 justify-start gap-2 px-2 py-2 h-fit'
          )}
          onClick={() =>
            toast.promise(handleLogOut(), {
              loading: 'Logging Out',
              success: () => 'Logged Out',
              error: (err: any) => `Error: ${err.message}`,
            })
          }
        >
          <DoorOpenIcon
            size={16}
            className="opacity-60 *:text-red-900 hover:text-neutral-600"
          />
          <span
            className={cn(FONT_JETBRAINS_MONO.className, 'font-bold uppercase')}
          >
            Log Out
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
