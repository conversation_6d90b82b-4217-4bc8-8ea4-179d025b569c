'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLawyerAvailability } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

const weekdays = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
];

// Schema for a single day's availability
const dayAvailabilitySchema = z.object({
  isAvailable: z.boolean(),
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Please enter a valid time in 24-hour format (HH:MM)',
  }),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: 'Please enter a valid time in 24-hour format (HH:MM)',
  }),
});

// Schema for the entire week's availability
const availabilityFormSchema = z.object({
  sunday: dayAvailabilitySchema,
  monday: dayAvailabilitySchema,
  tuesday: dayAvailabilitySchema,
  wednesday: dayAvailabilitySchema,
  thursday: dayAvailabilitySchema,
  friday: dayAvailabilitySchema,
  saturday: dayAvailabilitySchema,
  consultationDurations: z.array(z.number()).min(1, {
    message: 'Please select at least one consultation duration',
  }),
  consultationFee: z.number().min(0, {
    message: 'Consultation fee cannot be negative',
  }),
});

type AvailabilityFormValues = z.infer<typeof availabilityFormSchema>;

export function LawyerAvailabilitySettings() {
  const {
    availability,
    loading,
    error,
    fetchAvailability,
    updateAvailability,
    updateConsultationSettings,
  } = useLawyerAvailability();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('schedule');

  // Default form values
  const defaultValues: AvailabilityFormValues = {
    sunday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
    monday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
    tuesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
    wednesday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
    thursday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
    friday: { isAvailable: true, startTime: '09:00', endTime: '17:00' },
    saturday: { isAvailable: false, startTime: '09:00', endTime: '17:00' },
    consultationDurations: [30, 60],
    consultationFee: 100,
  };

  const form = useForm<AvailabilityFormValues>({
    resolver: zodResolver(availabilityFormSchema),
    defaultValues,
  });

  // Load availability data when component mounts
  useEffect(() => {
    fetchAvailability();
  }, [fetchAvailability]);

  // Update form when availability data is loaded
  useEffect(() => {
    if (availability) {
      // Map the availability data to form values
      const formValues: Partial<AvailabilityFormValues> = {};

      // Process each day's availability
      weekdays.forEach((day) => {
        const dayData = availability.find((a) => a.day_of_week === day.value);
        if (dayData) {
          const dayKey =
            day.label.toLowerCase() as keyof AvailabilityFormValues;

          // Create a properly typed day availability object
          const dayAvailability = {
            isAvailable: !!dayData.is_available,
            startTime: dayData.start_time.substring(0, 5), // HH:MM format
            endTime: dayData.end_time.substring(0, 5), // HH:MM format
          };

          // Set the value with proper typing
          if (dayKey === 'sunday') formValues.sunday = dayAvailability;
          else if (dayKey === 'monday') formValues.monday = dayAvailability;
          else if (dayKey === 'tuesday') formValues.tuesday = dayAvailability;
          else if (dayKey === 'wednesday')
            formValues.wednesday = dayAvailability;
          else if (dayKey === 'thursday') formValues.thursday = dayAvailability;
          else if (dayKey === 'friday') formValues.friday = dayAvailability;
          else if (dayKey === 'saturday') formValues.saturday = dayAvailability;
        }
      });

      // Set consultation settings
      if (availability.length > 0 && availability[0].lawyer) {
        formValues.consultationDurations = availability[0].lawyer
          .consultation_duration_options || [30, 60];
        formValues.consultationFee =
          availability[0].lawyer.consultation_fee || 100;
      }

      // Update form values
      Object.entries(formValues).forEach(([key, value]) => {
        form.setValue(key as any, value as any);
      });
    }
  }, [availability, form]);

  const onSubmit = async (data: AvailabilityFormValues) => {
    setIsSubmitting(true);
    try {
      // Format the availability data for the API
      const availabilityData = weekdays.map((day) => {
        const dayKey = day.label.toLowerCase() as keyof AvailabilityFormValues;
        const dayData = data[dayKey] as any;

        return {
          day_of_week: day.value,
          is_available: dayData.isAvailable,
          start_time: `${dayData.startTime}:00`, // Add seconds for DB format
          end_time: `${dayData.endTime}:00`, // Add seconds for DB format
        };
      });

      // Create a promise for updating availability
      const updateAvailabilityPromise = async () => {
        // Update availability
        const availabilitySuccess = await updateAvailability(availabilityData);

        // Update consultation settings
        const settingsSuccess = await updateConsultationSettings(
          data.consultationDurations,
          data.consultationFee
        );

        if (!availabilitySuccess || !settingsSuccess) {
          throw new Error('Failed to update one or more settings');
        }

        return { availabilitySuccess, settingsSuccess };
      };

      // Use toast.promise for better user feedback
      toast.promise(updateAvailabilityPromise(), {
        loading: 'Updating availability settings...',
        success:
          'Your availability and consultation settings have been updated.',
        error: (err) =>
          `Failed to update settings: ${err.message || 'Please try again or contact support if the problem persists.'}`,
      });

      // Execute the promise
      await updateAvailabilityPromise();
    } catch (error) {
      console.error('Error updating availability:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Availability Settings</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Availability Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-destructive mb-4">
              Error loading availability settings
            </p>
            <Button onClick={() => fetchAvailability()}>Retry</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Availability Settings</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="schedule">Weekly Schedule</TabsTrigger>
            <TabsTrigger value="settings">Consultation Settings</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <TabsContent value="schedule" className="space-y-6">
                <div className="grid gap-6">
                  {weekdays.map((day) => {
                    const dayKey =
                      day.label.toLowerCase() as keyof AvailabilityFormValues;

                    return (
                      <div key={day.value} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-medium">{day.label}</h3>
                          <FormField
                            control={form.control}
                            name={`${dayKey}.isAvailable` as any}
                            render={({ field }) => (
                              <FormItem className="flex items-center space-x-2">
                                <FormLabel>Available</FormLabel>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`${dayKey}.startTime` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Start Time</FormLabel>
                                <FormControl>
                                  <Input
                                    type="time"
                                    {...field}
                                    disabled={
                                      !form.watch(
                                        `${dayKey}.isAvailable` as any
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`${dayKey}.endTime` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>End Time</FormLabel>
                                <FormControl>
                                  <Input
                                    type="time"
                                    {...field}
                                    disabled={
                                      !form.watch(
                                        `${dayKey}.isAvailable` as any
                                      )
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>

              <TabsContent value="settings" className="space-y-6">
                <FormField
                  control={form.control}
                  name="consultationFee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Consultation Fee ($ per hour)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="5"
                          {...field}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Set your hourly rate for consultations. This will be
                        prorated based on the consultation duration.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultationDurations"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Available Consultation Durations (minutes)
                      </FormLabel>
                      <div className="flex flex-wrap gap-2">
                        {[15, 30, 45, 60, 90, 120].map((duration) => (
                          <Button
                            key={duration}
                            type="button"
                            variant={
                              field.value.includes(duration)
                                ? 'default'
                                : 'outline'
                            }
                            onClick={() => {
                              const newValue = field.value.includes(duration)
                                ? field.value.filter((d) => d !== duration)
                                : [...field.value, duration].sort(
                                    (a, b) => a - b
                                  );
                              field.onChange(newValue);
                            }}
                            className="h-10"
                          >
                            {duration} min
                          </Button>
                        ))}
                      </div>
                      <FormDescription>
                        Select the consultation durations you want to offer to
                        clients.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </Button>
              </div>
            </form>
          </Form>
        </Tabs>
      </CardContent>
    </Card>
  );
}
