/* TipTap Editor Custom Styles */

/* Remove borders only from the editable content area */
.ProseMirror {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Keep borders on the editor container */
.tiptap-editor-container {
  border: 1px solid #e5e7eb !important;
  /* Light gray border */
  border-radius: 0.375rem !important;
  /* rounded-md */
  overflow: hidden !important;
  background-color: white;
}

/* Keep borders on the toolbar with a bottom separator */
.tiptap-toolbar {
  border-bottom: 1px solid #e5e7eb !important;
  background-color: #f9fafb !important;
}

/* Keep borders on popover elements - no specific styles needed */

/* Remove borders from the editable content area only */
.tiptap .ProseMirror {
  border: none !important;
  outline: none !important;
}

/* Remove outline when focused */
.ProseMirror-focused {
  outline: none !important;
  border: none !important;
}

/* Remove borders from all contenteditable elements */
[contenteditable="true"] {
  border: none !important;
  outline: none !important;
}

/* Target only input and textarea elements inside the editor */
.tiptap-editor-container input,
.tiptap-editor-container textarea,
.tiptap-editor-container [contenteditable="true"] {
  border: none !important;
  outline: none !important;
}

/* Target the TipTap editor content specifically */
.tiptap .ProseMirror,
.tiptap-editor-container .ProseMirror {
  border: none !important;
  outline: none !important;
}

/* Add a subtle shadow to the toolbar for visual separation */
.tiptap-toolbar:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
  pointer-events: none;
}

/* Override any potential border styles for the editable content */
.tiptap-editor-container .ProseMirror,
.tiptap-editor-container .ProseMirror-focused,
.tiptap-editor-container .ProseMirror:focus,
  .tiptap-editor-container .ProseMirror:focus-visible,
  .tiptap-editor-container .ProseMirror:focus-within {
  border: none !important;
  outline: none !important;
}

/* Target only contenteditable textarea elements, not Textarea.Root */
[contenteditable="true"] textarea,
.ProseMirror textarea {
  border: none !important;
  outline: none !important;
}

/* Textarea.Root now has the same styling as Input - no additional CSS needed */

/* Target all input elements in the application */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="url"],
input[type="tel"],
input[type="number"] {
  border: none !important;
  outline: none !important;
}
