'use client';

import DocumentPreview from '@/components/documents/preview/DocumentPreview';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { supabaseClient } from '@/lib/supabase/client';
import { Document, DocumentShareLink } from '@/lib/types/database-modules';
import { useEffect, useState } from 'react';

interface DirectDocumentViewerProps {
  token: string;
  pin?: string;
  onDocumentLoaded?: (document: Document, permission: string) => void;
  onError?: (error: string) => void;
}

export function DirectDocumentViewer({
  token,
  pin,
  onDocumentLoaded,
  onError,
}: DirectDocumentViewerProps) {
  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocument = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use the consistent Supabase client from the project
        const supabase = supabaseClient;

        // First, get the share link details
        // Use any to bypass type checking for the table name
        let { data: shareLinkData, error: shareLinkError } = await (
          supabase as any
        )
          .from('document_share_links')
          .select('*')
          .eq('token', token)
          .single();

        // If we found a link but it's not active, provide a specific error message
        if (shareLinkData && !shareLinkData.is_active) {
          const errorMsg =
            'This share link has been disabled by the document owner.';
          setError(errorMsg);
          if (onError) onError(errorMsg);
          return;
        }

        if (shareLinkError) {
          console.error('Error fetching share link:', shareLinkError);

          // Check if the error is due to the link being disabled
          if (shareLinkError.code === 'PGRST116') {
            // This is a "not found" error from PostgREST
            const errorMsg =
              'This share link has been disabled or does not exist.';
            setError(errorMsg);
            if (onError) onError(errorMsg);
          } else if (shareLinkError.code === 'PGRST301') {
            // This is a row-level security error
            const errorMsg =
              'You do not have permission to access this document.';
            setError(errorMsg);
            if (onError) onError(errorMsg);
          } else {
            // Generic error message for other cases
            const errorMsg =
              'Unable to find the shared document. The link may be invalid or expired.';
            setError(errorMsg);
            if (onError) onError(errorMsg);
          }
          return;
        }

        // Cast the data to the correct type
        const shareLink = shareLinkData as DocumentShareLink;

        // Check if the link has expired
        if (
          shareLink.expires_at &&
          new Date(shareLink.expires_at) < new Date()
        ) {
          const errorMsg = 'This share link has expired';
          setError(errorMsg);
          if (onError) onError(errorMsg);
          return;
        }

        // Check if PIN protection is required
        if (shareLink.access_type === 'pin_protected') {
          // If no PIN is provided, return an error
          if (!pin) {
            const errorMsg = 'This document requires a PIN to access';
            setError(errorMsg);
            if (onError) onError(errorMsg);
            return;
          }

          // If PIN is provided but incorrect, return an error
          if (pin !== shareLink.access_pin) {
            const errorMsg = 'Incorrect PIN provided';
            setError(errorMsg);
            if (onError) onError(errorMsg);
            return;
          }
        }

        // Track access to the share link BEFORE fetching the document
        // This ensures views are recorded even if there's an error with the document
        try {
          console.log('Tracking access for share link ID:', shareLink.id);

          // Use any to bypass type checking for the RPC function
          const { data: trackResult, error: trackError } = await (
            supabase as any
          ).rpc('track_share_link_access', {
            link_id: shareLink.id,
            provided_pin: pin || null,
          });

          if (trackError) {
            // Log but don't fail if tracking fails
            console.error('Error tracking share link access:', trackError);
            console.error(
              'Track error details:',
              JSON.stringify(trackError, null, 2)
            );

            // Fallback: Try to update the access count directly if the RPC fails
            try {
              const { error: updateError } = await supabase
                .from('document_share_links')
                .update({
                  access_count: shareLink.access_count + 1,
                  last_accessed_at: new Date().toISOString(),
                })
                .eq('id', shareLink.id);

              if (updateError) {
                console.error('Fallback update failed:', updateError);
              } else {
                console.log('Fallback access tracking successful');
              }
            } catch (fallbackError) {
              console.error('Exception in fallback tracking:', fallbackError);
            }
          } else {
            console.log('Access tracked successfully:', trackResult);
          }
        } catch (trackError) {
          // Log but don't fail if tracking fails
          console.error('Exception tracking share link access:', trackError);
          if (trackError instanceof Error) {
            console.error('Error message:', trackError.message);
            console.error('Error stack:', trackError.stack);
          }
        }

        // Get the document
        try {
          const { data: document, error: documentError } = await supabase
            .from('documents')
            .select('*')
            .eq('id', shareLink.document_id)
            .single();

          if (documentError) {
            console.error('Error fetching document:', documentError);
            const errorMsg = 'Unable to retrieve the document';
            setError(errorMsg);
            if (onError) onError(errorMsg);
            return;
          }

          // Set the document
          setDocument(document);

          // Call the onDocumentLoaded callback if provided
          if (onDocumentLoaded) {
            onDocumentLoaded(document, shareLink.permission || 'view');
          }
        } catch (docError) {
          console.error('Error in document fetching:', docError);
          const errorMsg = 'An error occurred while loading the document';
          setError(errorMsg);
          if (onError) onError(errorMsg);
          return;
        }
      } catch (error) {
        console.error('Error in direct document fetching:', error);
        const errorMsg = 'An error occurred while loading the document';
        setError(errorMsg);
        if (onError) onError(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
  }, [token, pin, onDocumentLoaded]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-6">
            <div>
              <Skeleton className="h-5 w-1/3 mb-3" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full mt-1" />
              <Skeleton className="h-4 w-2/3 mt-1" />
            </div>

            <div>
              <Skeleton className="h-5 w-1/4 mb-3" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full mt-1" />
              <Skeleton className="h-4 w-3/4 mt-1" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-red-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            Access Error
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="bg-red-50 p-4 rounded-md">
            <p className="text-red-800">{error}</p>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            <p>
              If you believe this is an error, please contact the document
              owner.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!document) {
    return (
      <Card className="border-amber-200">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-amber-700">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            Document Not Available
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="bg-amber-50 p-4 rounded-md">
            <p className="text-amber-800">
              The document could not be loaded. This could be because:
            </p>
            <ul className="list-disc pl-5 mt-2 space-y-1 text-amber-700">
              <li>The share link has been disabled</li>
              <li>The document has been deleted</li>
              <li>You don't have permission to view this document</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <DocumentPreview document={document} />
      </CardContent>
    </Card>
  );
}
