import { SVGProps } from "react";

export function CubeSettingsFill(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      height="18"
      width="18"
      viewBox="0 0 18 18"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g fill="currentColor">
        <path
          d="M17,13h-.878c-.044-.138-.098-.271-.164-.397l.62-.621c.293-.293,.293-.768,0-1.061-.294-.293-.769-.292-1.061,0l-.62,.621c-.127-.066-.259-.121-.397-.164v-.878c0-.414-.336-.75-.75-.75s-.75,.336-.75,.75v.878c-.138,.044-.271,.098-.397,.164l-.62-.621c-.292-.292-.767-.293-1.061,0-.293,.292-.293,.768,0,1.061l.62,.621c-.066,.127-.12,.259-.164,.397h-.878c-.414,0-.75,.336-.75,.75s.336,.75,.75,.75h.878c.044,.138,.098,.271,.164,.397l-.62,.621c-.293,.293-.293,.768,0,1.061,.146,.146,.339,.22,.53,.22,.192,0,.384-.073,.53-.22l.62-.621c.127,.066,.259,.121,.397,.164v.878c0,.414,.336,.75,.75,.75s.75-.336,.75-.75v-.878c.138-.044,.271-.098,.397-.164l.62,.621c.146,.146,.338,.22,.53,.22,.191,0,.384-.073,.53-.22,.293-.292,.293-.768,0-1.061l-.62-.621c.066-.127,.12-.259,.164-.397h.878c.414,0,.75-.336,.75-.75s-.336-.75-.75-.75Zm-2.25,.75c0,.551-.448,1-1,1s-1-.449-1-1,.448-1,1-1,1,.449,1,1Z"
          fill="currentColor"
        />
        <path
          d="M8.75,14.983v-5.551l4.75-2.755v1.383c0,.414,.336,.75,.75,.75s.75-.336,.75-.75v-1.533c0-.977-.524-1.888-1.37-2.379L9.38,1.683c-.852-.493-1.908-.493-2.76,0L2.369,4.148c-.845,.491-1.369,1.402-1.369,2.379v4.945c0,.977,.524,1.888,1.37,2.379l4.25,2.465c.425,.246,.896,.37,1.368,.37,.449,0,.9-.112,1.315-.335,.365-.197,.501-.651,.305-1.016-.169-.314-.529-.444-.858-.352Zm-5.627-2.428c-.385-.223-.623-.638-.623-1.082V6.677l4.75,2.755v5.516l-4.127-2.394Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
}
