'use client';

import { DocumentOrganizer } from '@/components/documents/organizer/DocumentOrganizer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FolderTree, Save, Tag } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export default function DocumentOrganizePage() {
  const { username } = useParams();
  const router = useRouter();

  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedTagId, setSelectedTagId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('folders');

  const handleSelectFolder = (folderId: string | null) => {
    setSelectedFolderId(folderId);
  };

  const handleSelectTag = (tagId: string | null) => {
    setSelectedTagId(tagId);
  };

  const handleSaveOrganization = () => {
    // In a real implementation, this would save the organization settings
    toast.success('Organization saved', {
      description: 'Your document organization settings have been saved',
    });
  };

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.push(`/${username}/documents`)}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Document Organization</h1>
        </div>

        <Button onClick={handleSaveOrganization}>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Organization</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="folders" onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger
                    value="folders"
                    className="flex items-center gap-2"
                  >
                    <FolderTree className="h-4 w-4" />
                    Folders
                  </TabsTrigger>
                  <TabsTrigger value="tags" className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Tags
                  </TabsTrigger>
                </TabsList>

                <div className="mt-4">
                  <DocumentOrganizer
                    onSelectFolder={handleSelectFolder}
                    onSelectTag={handleSelectTag}
                    selectedFolderId={selectedFolderId}
                    selectedTagId={selectedTagId}
                  />
                </div>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'folders'
                  ? 'Folder Contents'
                  : 'Tagged Documents'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {activeTab === 'folders' ? (
                selectedFolderId ? (
                  <div>
                    <p className="text-muted-foreground mb-4">
                      Showing documents in the selected folder. In a real
                      implementation, this would display the documents in the
                      folder.
                    </p>
                    <div className="border rounded-md p-8 text-center">
                      <FolderTree className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">Folder View</h3>
                      <p className="text-muted-foreground max-w-md mx-auto">
                        This area would display the documents contained in the
                        selected folder, allowing you to manage and organize
                        them.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FolderTree className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">
                      Select a Folder
                    </h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      Select a folder from the left panel to view and manage its
                      contents.
                    </p>
                  </div>
                )
              ) : selectedTagId ? (
                <div>
                  <p className="text-muted-foreground mb-4">
                    Showing documents with the selected tag. In a real
                    implementation, this would display the tagged documents.
                  </p>
                  <div className="border rounded-md p-8 text-center">
                    <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Tag View</h3>
                    <p className="text-muted-foreground max-w-md mx-auto">
                      This area would display the documents tagged with the
                      selected tag, allowing you to manage and organize them.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Tag className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Select a Tag</h3>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    Select a tag from the left panel to view and manage
                    documents with that tag.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
