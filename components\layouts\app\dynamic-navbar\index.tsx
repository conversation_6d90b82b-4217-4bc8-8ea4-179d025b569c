'use client';

import { SBg } from '@/components/ui/stripped-bg';
import { useClickOutside } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import { AnimatePresence, MotionConfig, motion } from 'motion/react';
import dynamic from 'next/dynamic';
import { usePathname } from 'next/navigation';
import { useRef, useState } from 'react';
import { AccountMenu } from './menus/account';
import { HMenu } from './menus/menu';
import { MenuButton } from './menus/menuButton';

const transition = {
  type: 'spring',
  bounce: 0.1,
  duration: 0.2,
};

const actionComponents = {
  home: {
    url: '[username]',
    actions: [],
  },
  onboarding: {
    url: '[username]/onboarding',
    actions: ['onboarding'],
  },
  forms: {
    url: '[username]/forms',
    actions: ['dashboard'],
  },
  templates: {
    url: '[username]/templates',
    actions: [],
  },
  settings: {
    url: '[username]/settings',
    actions: ['onboarding'],
  },
};

const DynamicNav = () => {
  const pathname = usePathname();
  const containerRef = useRef<HTMLDivElement>(
    null
  ) as React.RefObject<HTMLDivElement>;
  const [menuOpen, setMenuOpen] = useState(false);
  const [accountOpen, setAccountOpen] = useState(false);

  const secondSegment = pathname?.split('/')[1] || '';

  useClickOutside(containerRef, () => {
    setMenuOpen(false);
  });

  function handleClose() {
    setMenuOpen(false);
    setAccountOpen(false);
  }
  function handleMenu() {
    setMenuOpen(true);
    setAccountOpen(false);
  }

  function handleAccount() {
    setMenuOpen(false);
    setAccountOpen(true);
  }

  // Get current page actions based on pathname
  // Get current page actions based on pathname
  const getCurrentPageActions = () => {
    const currentPath = Object.values(actionComponents).find((route) => {
      const routePath = route.url.replace('[username]', secondSegment);
      return pathname === `/${routePath}`;
    });
    return currentPath?.actions || [];
  };

  // Dynamically import action components
  const renderActionComponent = (actionName: string) => {
    const ActionComponent = dynamic(
      () =>
        import(
          `@/components/layouts/app/dynamic-navbar/components/${actionName}`
        ),
      {
        loading: () => <div className=""></div>,
        ssr: false,
      }
    );
    return <ActionComponent key={actionName} />;
  };
  return (
    <nav
      ref={containerRef}
      className={cn(
        'fixed bottom-6 left-1/2 -translate-x-1/2 transform animate-in z-400 w-fit select-none overflow-hidden rounded-3xl border border-neutral-200 bg-white/60 backdrop-blur-lg backdrop-filter'
      )}
    >
      <MotionConfig transition={transition}>
        <motion.div
          animate={{
            width: menuOpen ? '200px' : accountOpen ? '220px' : 'fit-content',
            height: menuOpen ? '380px' : accountOpen ? '460px' : 'fit-content',
          }}
          className="flex flex-col justify-end"
        >
          {menuOpen ? (
            <HMenu
              handleClose={handleClose}
              setMenuOpen={setMenuOpen}
              // handleFeedback={handleFeedback}
              // handleInfo={handleInfo}
              handleAccount={handleAccount}
              // handlePricing={handlePricing}
            />
          ) : accountOpen ? (
            <AccountMenu
              handleMenu={handleMenu}
              handleClose={handleClose}
              setAccountOpen={setAccountOpen}
            />
          ) : (
            <div className="h-0 w-full"></div>
          )}
          {/* menu and dynamic actions */}
          <div
            className={cn(
              'w-full flex h-10 overflow-hidden',
              menuOpen || accountOpen
                ? 'border-t border-dashed border-neutral-200'
                : ''
            )}
          >
            <MenuButton
              menuOpen={menuOpen || accountOpen}
              setMenuOpen={setMenuOpen}
            />
            <div className="p-[2px] flex flex-col w-full ">
              <div
                className={cn(
                  'relative size-full flex flex-col px-4 items-center justify-center border border-neutral-200 overflow-hidden',
                  !menuOpen &&
                    !accountOpen &&
                    getCurrentPageActions().length === 0 &&
                    'rounded-e-[1.3rem]',
                  // When menu and account are closed but there are actions
                  !menuOpen &&
                    !accountOpen &&
                    getCurrentPageActions().length > 0 &&
                    'rounded-none',
                  // When either menu or account is open
                  (menuOpen || accountOpen) && 'rounded-br-[1.3rem]'
                )}
              >
                <SBg />
                <div
                  className={cn(
                    'relative w-full flex flex-col size-full items-center justify-center'
                  )}
                >
                  <p className={cn('')}>
                    {menuOpen || accountOpen ? (
                      <span></span>
                    ) : (
                      <span>
                        {pathname.split('/').length > 2
                          ? pathname.split('/')[2].charAt(0).toUpperCase() +
                            pathname.split('/')[2].slice(1)
                          : secondSegment.charAt(0).toUpperCase() +
                            secondSegment.slice(1)}
                      </span>
                    )}
                  </p>
                </div>
              </div>
            </div>
            <AnimatePresence key={pathname} mode="sync">
              {menuOpen ||
                accountOpen ||
                (getCurrentPageActions().length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, filter: 'blur(10px)' }}
                    animate={{ opacity: 1, filter: 'blur(0px)' }}
                    exit={{ opacity: 0, filter: 'blur(10px)' }}
                    transition={{ duration: 0.2 }}
                    className="relative w-full flex border-l border-dashed border-neutral-200 p-2 flex-col size-full items-center justify-center"
                  >
                    {getCurrentPageActions().map((actionName) => {
                      console.log(actionName);
                      // return <p>{actionName}</p>;
                      return renderActionComponent(actionName);
                    })}
                  </motion.div>
                ))}
            </AnimatePresence>
          </div>
        </motion.div>
      </MotionConfig>
      {/* dynamic menu */}
    </nav>
  );
};

export default DynamicNav;
