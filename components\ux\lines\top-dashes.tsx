export function TopDashedBg() {
  return (
    <svg
      className='h-4 -mb-2 -z-10 w-full border-t border-dashed border-gray-300 stroke-gray-300'
      // style={{
      //   maskImage:
      //     "linear-gradient(transparent, white 10rem, white calc(100% - 10rem), transparent)",
      // }}
    >
      <defs>
        <pattern
          id='diagonal-footer-pattern'
          patternUnits='userSpaceOnUse'
          width='64'
          height='64'
        >
          {Array.from({ length: 17 }, (_, i) => {
            const offset = i * 8;
            return (
              <path
                key={i}
                d={`M${-106 + offset} 110L${22 + offset} -18`}
                stroke=''
                strokeWidth='1'
              />
            );
          })}
        </pattern>
      </defs>
      <rect
        stroke='none'
        width='100%'
        height='100%'
        fill='url(#diagonal-footer-pattern)'
      />
    </svg>
  );
}
