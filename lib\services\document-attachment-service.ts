'use client';

import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import {
  DocumentAttachment,
  DocumentAttachmentInsert,
} from '@/lib/types/database-modules';
import { toast } from 'sonner';

/**
 * Storage bucket name for document attachments
 */
const STORAGE_BUCKET = 'document-attachments';

/**
 * Interface for file upload options
 */
interface UploadOptions {
  cacheControl?: string;
  upsert?: boolean;
  contentType?: string;
}

/**
 * Service for managing document attachments
 * Handles uploading, fetching, and deleting attachments
 */
/**
 * Helper function to handle errors consistently
 * @param error The error object
 * @param message Custom error message
 * @param shouldToast Whether to show a toast notification
 */
const handleError = (error: any, message: string, shouldToast = true): void => {
  console.error(`${message}:`, error);
  if (shouldToast) {
    toast.error(message);
  }
};

export const documentAttachmentService = {
  /**
   * Upload a file to Supabase storage and create a document attachment record
   * @param file The file to upload
   * @param documentId The document ID to attach the file to
   * @param description Optional description for the attachment
   */
  uploadAttachment: async (
    file: File,
    documentId: string,
    description?: string
  ): Promise<DocumentAttachment | null> => {
    try {
      // First, check if the user is authenticated
      const { data: sessionData } = await supabaseClient.auth.getSession();
      if (!sessionData.session) {
        handleError(
          new Error('No active session'),
          'You must be logged in to upload attachments'
        );
        return null;
      }

      // Get user ID from session or store
      const userId =
        sessionData.session.user.id || userStore.getState().user?.id;
      if (!userId) {
        handleError(new Error('User ID not found'), 'Authentication error');
        return null;
      }

      // Create a unique filename to avoid collisions
      // Format: documentId/timestamp_originalFilename
      const timestamp = Date.now();
      const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const filePath = `${documentId}/${timestamp}_${sanitizedFilename}`;

      // Log the file details for debugging
      console.log('Uploading attachment for document:', documentId);
      console.log('File details:', {
        name: file.name,
        type: file.type,
        size: file.size,
        path: filePath,
      });

      // Upload the file to Supabase storage
      const uploadResult = await supabaseClient.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false, // Don't overwrite existing files
          contentType: file.type,
        } as UploadOptions);

      if (uploadResult.error) {
        const error = uploadResult.error;

        // Log detailed error information
        console.error('Storage upload error details:', {
          message: error.message || 'No message',
          name: error.name || 'Unknown error',
          stack: error.stack || 'No stack trace',
        });

        // Show a more specific error message based on the error
        let errorMessage = 'Failed to upload attachment';
        if (error.message?.includes('permission')) {
          errorMessage = 'Permission denied when uploading attachment';
        } else if (error.message?.includes('not found')) {
          errorMessage = 'Storage bucket not found';
        } else if (error.message) {
          errorMessage = `Upload error: ${error.message}`;
        }

        toast.error(errorMessage);
        return null;
      }

      // Get the public URL
      const { data: publicUrlData } = supabaseClient.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(filePath);

      if (!publicUrlData) {
        console.error('Failed to get public URL');
        toast.error('Could not generate public URL for attachment');
        return null;
      }

      const publicUrl = publicUrlData.publicUrl;

      // Create a record in the document_attachments table
      const attachmentData: DocumentAttachmentInsert = {
        document_id: documentId,
        file_name: file.name,
        file_path: filePath,
        file_type: file.type,
        file_size: file.size,
        file_url: publicUrl,
        uploaded_by: userId,
        description: description || null,
      };

      const { data: attachment, error: insertError } = await supabaseClient
        .from('document_attachments')
        .insert(attachmentData)
        .select('*')
        .single();

      if (insertError) {
        handleError(insertError, 'Failed to create attachment record');

        // Try to clean up the uploaded file to avoid orphaned storage files
        try {
          await supabaseClient.storage.from(STORAGE_BUCKET).remove([filePath]);
        } catch (cleanupError) {
          // Just log cleanup errors, don't show to user
          console.warn('Failed to clean up uploaded file:', cleanupError);
        }

        return null;
      }

      return attachment as DocumentAttachment;
    } catch (error: any) {
      handleError(error, 'Failed to upload attachment');
      return null;
    }
  },

  /**
   * Get all attachments for a document
   * @param documentId The document ID
   */
  getAttachments: async (documentId: string): Promise<DocumentAttachment[]> => {
    try {
      const { data, error } = (await supabaseClient
        .from('document_attachments')
        .select('*')
        .eq('document_id', documentId)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false })) as {
        data: DocumentAttachment[] | null;
        error: any;
      };

      if (error) {
        handleError(error, 'Failed to fetch attachments');
        return [];
      }

      return data as DocumentAttachment[];
    } catch (error) {
      handleError(error, 'Failed to fetch attachments');
      return [];
    }
  },

  /**
   * Get a single attachment by ID
   * @param attachmentId The attachment ID
   */
  getAttachment: async (
    attachmentId: string
  ): Promise<DocumentAttachment | null> => {
    try {
      const { data, error } = await supabaseClient
        .from('document_attachments')
        .select('*')
        .eq('id', attachmentId)
        .single();

      if (error) {
        handleError(error, 'Failed to fetch attachment');
        return null;
      }

      return data as DocumentAttachment;
    } catch (error) {
      handleError(error, 'Failed to fetch attachment');
      return null;
    }
  },

  /**
   * Delete an attachment
   * @param attachmentId The attachment ID
   */
  deleteAttachment: async (attachmentId: string): Promise<boolean> => {
    try {
      // First get the attachment to get the file path
      const attachment =
        await documentAttachmentService.getAttachment(attachmentId);

      if (!attachment) {
        // Error already handled in getAttachment
        return false;
      }

      // Soft delete the attachment record
      const { error: updateError } = await supabaseClient
        .from('document_attachments')
        .update({ is_deleted: true })
        .eq('id', attachmentId);

      if (updateError) {
        handleError(updateError, 'Failed to delete attachment');
        return false;
      }

      // Delete the file from storage (optional - can be done in a background job)
      const { error: storageError } = await supabaseClient.storage
        .from(STORAGE_BUCKET)
        .remove([attachment.file_path as string]);

      if (storageError) {
        // Log but don't show toast or fail the operation
        handleError(
          storageError,
          'Error deleting attachment file from storage',
          false
        );
        // Don't return false here, as the record was successfully updated
      }

      return true;
    } catch (error) {
      handleError(error, 'Failed to delete attachment');
      return false;
    }
  },
};
