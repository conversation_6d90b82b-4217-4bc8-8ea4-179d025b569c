'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>lipboard<PERSON>heck,
  FolderKanban,
  MessageSquare,
  Network,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';

export default function CollaborationLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const params = useParams();
  const username = params.username as string;

  const isActive = (path: string) => {
    return pathname.includes(path);
  };

  return (
    <div className="flex flex-col">
      <div className="border-b">
        <div className="container flex items-center gap-4 py-2">
          <Link href={`/${username}/collaboration/hub`}>
            <Button
              variant={
                isActive('/hub') || pathname === `/${username}/collaboration`
                  ? 'default'
                  : 'ghost'
              }
              className="gap-2"
            >
              <Network className="size-4" />
              <span>Hub</span>
            </Button>
          </Link>
          <Link href={`/${username}/collaboration/projects`}>
            <Button
              variant={isActive('/projects') ? 'default' : 'ghost'}
              className="gap-2"
            >
              <FolderKanban className="size-4" />
              <span>Projects</span>
            </Button>
          </Link>
          <Link href={`/${username}/collaboration/tasks`}>
            <Button
              variant={isActive('/tasks') ? 'default' : 'ghost'}
              className="gap-2"
            >
              <ClipboardCheck className="size-4" />
              <span>Tasks</span>
            </Button>
          </Link>
          <Link href={`/${username}/collaboration/collaborators`}>
            <Button
              variant={isActive('/collaborators') ? 'default' : 'ghost'}
              className="gap-2"
            >
              <Users className="size-4" />
              <span>Collaborators</span>
            </Button>
          </Link>
          <Link href={`/${username}/collaboration/messages`}>
            <Button
              variant={isActive('/messages') ? 'default' : 'ghost'}
              className="gap-2"
            >
              <MessageSquare className="size-4" />
              <span>Messages</span>
            </Button>
          </Link>
        </div>
      </div>
      <div className="container">{children}</div>
    </div>
  );
}
