import { Skeleton } from '@/components/ui/skeleton';
import React, { ComponentType, lazy, Suspense } from 'react';

/**
 * Creates a lazy-loaded component with a fallback loading state
 * @param importFn Function that imports the component
 * @param LoadingComponent Component to show while loading
 * @returns Lazy-loaded component with Suspense
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  LoadingComponent: React.ReactNode
) {
  const LazyComponent = lazy(importFn);

  const LazyComponentWrapper = (props: React.ComponentProps<T>) => (
    <Suspense fallback={LoadingComponent}>
      <LazyComponent {...props} />
    </Suspense>
  );

  // Add display name
  LazyComponentWrapper.displayName = `LazyLoaded(${importFn.name || 'Component'})`;

  return LazyComponentWrapper;
}

/**
 * Creates a memoized component to prevent unnecessary re-renders
 * @param Component Component to memoize
 * @returns Memoized component
 */
export function createMemoizedComponent<T extends ComponentType<any>>(
  Component: T
) {
  return React.memo(Component);
}

/**
 * Default loading state for card-based components
 */
export const DefaultCardLoadingState: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: 3 }).map((_, i) => (
      <div key={i} className="border rounded-md p-4 space-y-4">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <div className="flex justify-between pt-4">
          <Skeleton className="h-8 w-20" />
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      </div>
    ))}
  </div>
);

// Add display name
DefaultCardLoadingState.displayName = 'DefaultCardLoadingState';

/**
 * Default loading state for table-based components
 */
export const DefaultTableLoadingState: React.FC = () => (
  <div className="rounded-md border">
    <div className="border-b h-10 px-4 flex items-center">
      <Skeleton className="h-4 w-full max-w-[300px]" />
    </div>
    <div className="divide-y">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="p-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[200px]" />
              <Skeleton className="h-3 w-[150px]" />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Add display name
DefaultTableLoadingState.displayName = 'DefaultTableLoadingState';

/**
 * Debounce function to limit how often a function can be called
 * @param fn Function to debounce
 * @param delay Delay in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return function (...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
}

/**
 * Creates a cached fetch function that stores results in localStorage
 * @param fetchFn Function that fetches data
 * @param cacheKey Key to use for localStorage
 * @param ttl Time to live in milliseconds
 * @returns Function that fetches data with caching
 */
export function createCachedFetch<T>(
  fetchFn: () => Promise<T>,
  cacheKey: string,
  ttl: number = 15 * 60 * 1000 // 15 minutes default
): () => Promise<T> {
  return async () => {
    // Try to get from cache
    if (typeof window !== 'undefined') {
      try {
        const cachedData = localStorage.getItem(cacheKey);
        if (cachedData) {
          const { data, timestamp } = JSON.parse(cachedData);
          // Check if cache is still valid
          if (Date.now() - timestamp < ttl) {
            console.log(`Using cached data for ${cacheKey}`);
            return data as T;
          }
        }
      } catch (error) {
        console.error('Error reading from cache:', error);
      }
    }

    // Fetch fresh data
    const data = await fetchFn();

    // Save to cache
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(
          cacheKey,
          JSON.stringify({
            data,
            timestamp: Date.now(),
          })
        );
      } catch (error) {
        console.error('Error saving to cache:', error);
      }
    }

    return data;
  };
}
