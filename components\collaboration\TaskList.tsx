'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useCollaboration } from '@/lib/hooks';
import { cn } from '@/lib/utils';
import {
  CheckCircle2,
  Clock,
  Edit,
  Loader2,
  MoreHorizontal,
  Plus,
  Trash2,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Local Task interface that maps to the database ProjectTask
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo?: string;
  dueDate?: string;
  createdBy: string;
  createdAt: string;
}

// Local Member interface that maps to the database ProjectMember with profile info
export interface Member {
  id: string;
  userId: string;
  fullName?: string;
  email: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  createdAt: string;
  avatarUrl?: string | null;
}

interface TaskListProps {
  projectId: string;
}

export function TaskList({ projectId }: TaskListProps) {
  const {
    tasks: projectTasks,
    members: projectMembers,
    loading: collaborationLoading,
    fetchTasks,
    fetchMembers,
    updateTask,
    deleteTask,
    addTask,
  } = useCollaboration(projectId);

  const [isCreateTaskOpen, setIsCreateTaskOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [filterPriority, setFilterPriority] = useState<string | null>(null);
  const [filterAssignee, setFilterAssignee] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load tasks and members when component mounts
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([fetchTasks(projectId), fetchMembers(projectId)]);
      } catch (err) {
        console.error('Error loading task data:', err);
        toast.error('Failed to load tasks');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [projectId, fetchTasks, fetchMembers]);

  // Map database tasks to our Task interface
  const tasks = (projectTasks || []).map((task: any) => ({
    id: task.id,
    title: task.title,
    description: task.description || undefined,
    status: task.status as 'todo' | 'in_progress' | 'review' | 'done',
    priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
    assignedTo: task.assigned_to || undefined,
    dueDate: task.due_date || undefined,
    createdBy: task.created_by,
    createdAt: task.created_at,
  }));

  // Map database members to our Member interface
  const members = (projectMembers || []).map((member: any) => ({
    id: member.id,
    userId: member.user_id,
    fullName: member.user?.full_name || undefined,
    email: member.user?.email || '',
    role: member.role as 'owner' | 'admin' | 'member' | 'viewer',
    createdAt: member.created_at,
    avatarUrl: member.user?.avatar_url || null,
  }));

  // Filter tasks based on selected filters
  const filteredTasks = tasks.filter((task: Task) => {
    if (filterStatus && task.status !== filterStatus) return false;
    if (filterPriority && task.priority !== filterPriority) return false;
    if (filterAssignee && task.assignedTo !== filterAssignee) return false;
    return true;
  });

  // Group tasks by status
  const tasksByStatus = {
    todo: filteredTasks.filter((task: Task) => task.status === 'todo'),
    in_progress: filteredTasks.filter(
      (task: Task) => task.status === 'in_progress'
    ),
    review: filteredTasks.filter((task: Task) => task.status === 'review'),
    done: filteredTasks.filter((task: Task) => task.status === 'done'),
  };

  // Get member by ID
  const getMemberById = (userId?: string) => {
    if (!userId) return null;
    return members.find((member: Member) => member.userId === userId) || null;
  };

  // Handle task status change
  const handleStatusChange = async (
    taskId: string,
    newStatus: 'todo' | 'in_progress' | 'review' | 'done'
  ) => {
    try {
      await toast.promise(updateTask(taskId, { status: newStatus }), {
        loading: 'Updating task status...',
        success: 'Task status updated',
        error: 'Failed to update task status',
      });
    } catch (err) {
      console.error('Error updating task status:', err);
    }
  };

  // Handle task deletion
  const handleDeleteTask = async (taskId: string) => {
    try {
      await toast.promise(deleteTask(taskId), {
        loading: 'Deleting task...',
        success: 'Task deleted',
        error: 'Failed to delete task',
      });
    } catch (err) {
      console.error('Error deleting task:', err);
    }
  };

  // Handle task creation
  const handleCreateTask = async (taskData: any) => {
    try {
      await toast.promise(
        addTask(
          projectId,
          taskData.title,
          taskData.description || '',
          taskData.assignedTo,
          taskData.dueDate,
          taskData.priority as 'low' | 'medium' | 'high'
        ),
        {
          loading: 'Creating task...',
          success: 'Task created',
          error: 'Failed to create task',
        }
      );
      setIsCreateTaskOpen(false);
    } catch (err) {
      console.error('Error creating task:', err);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-2" />
          <p className="text-muted-foreground">Loading tasks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Status: {filterStatus ? filterStatus.replace('_', ' ') : 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterStatus(null)}>
                All
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterStatus('todo')}>
                To Do
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('in_progress')}>
                In Progress
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('review')}>
                In Review
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus('done')}>
                Done
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Priority: {filterPriority ? filterPriority : 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterPriority(null)}>
                All
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterPriority('low')}>
                Low
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterPriority('medium')}>
                Medium
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterPriority('high')}>
                High
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterPriority('urgent')}>
                Urgent
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Assignee:{' '}
                {filterAssignee
                  ? getMemberById(filterAssignee)?.fullName ||
                    getMemberById(filterAssignee)?.email
                  : 'All'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterAssignee(null)}>
                All
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {members.map((member: Member) => (
                <DropdownMenuItem
                  key={member.userId}
                  onClick={() => setFilterAssignee(member.userId)}
                >
                  {member.fullName || member.email}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Button className="gap-1" onClick={() => setIsCreateTaskOpen(true)}>
          <Plus className="size-4" />
          <span>Add Task</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* To Do Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>To Do</span>
            <Badge variant="outline">{tasksByStatus.todo.length}</Badge>
          </h3>
          <div className="bg-neutral-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.todo.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.todo.map((task: Task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={getMemberById(task.assignedTo)}
                    onStatusChange={handleStatusChange}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* In Progress Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>In Progress</span>
            <Badge variant="outline">{tasksByStatus.in_progress.length}</Badge>
          </h3>
          <div className="bg-neutral-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.in_progress.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.in_progress.map((task: Task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={getMemberById(task.assignedTo)}
                    onStatusChange={handleStatusChange}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* In Review Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>In Review</span>
            <Badge variant="outline">{tasksByStatus.review.length}</Badge>
          </h3>
          <div className="bg-neutral-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.review.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.review.map((task: Task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={getMemberById(task.assignedTo)}
                    onStatusChange={handleStatusChange}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Done Column */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-neutral-500 flex items-center gap-2">
            <span>Done</span>
            <Badge variant="outline">{tasksByStatus.done.length}</Badge>
          </h3>
          <div className="bg-neutral-50 rounded-lg p-2 min-h-[200px]">
            {tasksByStatus.done.length === 0 ? (
              <div className="text-center py-8 text-neutral-400 text-sm">
                No tasks
              </div>
            ) : (
              <div className="space-y-2">
                {tasksByStatus.done.map((task: Task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    member={getMemberById(task.assignedTo)}
                    onStatusChange={handleStatusChange}
                    onDelete={() => handleDeleteTask(task.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

interface TaskCardProps {
  task: Task;
  member: Member | null;
  onStatusChange: (
    taskId: string,
    status: 'todo' | 'in_progress' | 'review' | 'done'
  ) => void;
  onDelete: () => void;
}

function TaskCard({ task, member, onStatusChange, onDelete }: TaskCardProps) {
  return (
    <div className="bg-white rounded-md border p-3 shadow-sm">
      <div className="flex justify-between items-start">
        <div className="space-y-1">
          <h4 className="font-medium text-sm">{task.title}</h4>
          {task.description && (
            <p className="text-xs text-neutral-500 line-clamp-2">
              {task.description}
            </p>
          )}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2">
              <Edit className="size-4" />
              <span>Edit</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onStatusChange(task.id, 'todo')}
              disabled={task.status === 'todo'}
            >
              Move to To Do
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onStatusChange(task.id, 'in_progress')}
              disabled={task.status === 'in_progress'}
            >
              Move to In Progress
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onStatusChange(task.id, 'review')}
              disabled={task.status === 'review'}
            >
              Move to Review
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onStatusChange(task.id, 'done')}
              disabled={task.status === 'done'}
            >
              Mark as Done
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600 gap-2"
              onClick={onDelete}
            >
              <Trash2 className="size-4" />
              <span>Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="mt-3 flex flex-wrap gap-2">
        <Badge
          variant="outline"
          className={cn(
            task.priority === 'urgent'
              ? 'border-red-200 text-red-700'
              : task.priority === 'high'
                ? 'border-orange-200 text-orange-700'
                : task.priority === 'medium'
                  ? 'border-amber-200 text-amber-700'
                  : 'border-green-200 text-green-700'
          )}
        >
          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
        </Badge>

        {task.dueDate && (
          <Badge variant="outline" className="gap-1">
            <Clock className="size-3" />
            <span>{new Date(task.dueDate).toLocaleDateString()}</span>
          </Badge>
        )}
      </div>

      {member && (
        <div className="mt-3 flex items-center gap-2">
          <UserAvatar
            size="sm"
            avatarUrl={member.avatarUrl}
            fallbackText={member.fullName || member.email}
          />
          <span className="text-xs text-neutral-500">
            {member.fullName || member.email.split('@')[0]}
          </span>
        </div>
      )}

      {task.status === 'done' && (
        <div className="mt-2 flex items-center gap-1 text-green-600 text-xs">
          <CheckCircle2 className="size-3" />
          <span>Completed</span>
        </div>
      )}
    </div>
  );
}
