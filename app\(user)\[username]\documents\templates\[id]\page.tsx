'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDocuments } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import { Document, DocumentType } from '@/lib/types/database-modules';
import { ArrowLeft, FileText } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function TemplatePreviewPage() {
  const params = useParams();
  const username = params.username as string;
  const router = useRouter();
  const { getById, createDocument } = useDocuments();
  const supabase = supabaseClient;

  const [template, setTemplate] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      setLoading(true);
      setError(null);
      try {
        const templateId = params.id as string;

        if (!templateId) {
          setError('Invalid template ID');
          setLoading(false);
          return;
        }

        console.log('Fetching template with ID:', templateId);

        // First try to get it as a regular document template
        let doc = null;
        try {
          doc = await getById(templateId);
          console.log(
            'Document template fetch result:',
            doc ? 'Found' : 'Not found'
          );
        } catch (docError) {
          console.error('Error fetching from documents table:', docError);
          // Continue to try the templates table
        }

        // If not found, try to get it from the templates table
        if (!doc) {
          console.log('Trying templates table for ID:', templateId);
          try {
            const { data, error } = await supabase
              .from('templates')
              .select('*')
              .eq('id', templateId)
              .single();

            if (error) {
              console.error('Error fetching from templates table:', error);
              if (error.code === 'PGRST116') {
                // Not found in templates table either
                setError('Template not found in any table');
                setLoading(false);
                return;
              }
              throw error;
            }

            if (data) {
              console.log(
                'Template found in templates table:',
                data.id,
                data.title
              );
              // Convert the template to Document format
              doc = {
                id: data.id,
                title: data.title,
                description: data.description,
                document_type: data.document_type as DocumentType,
                status: 'template',
                is_template: true,
                created_at: data.created_at,
                updated_at: data.updated_at || data.created_at,
                owner_id: data.created_by || '',
                content: data.content,
                version: 1,
                metadata: null,
                file_url: null,
                file_type: null,
                file_size: null,
                shared_with: [],
                template_id: null,
              };
            }
          } catch (templateError) {
            console.error(
              'Exception fetching from templates table:',
              templateError
            );
            throw templateError;
          }
        }

        if (doc) {
          console.log('Setting template:', doc.id, doc.title);
          setTemplate(doc);
        } else {
          console.error('Template not found in either table');
          setError('Template not found');
        }
      } catch (err) {
        console.error('Error fetching template:', err);
        let errorMessage = 'Failed to load template preview';

        if (err instanceof Error) {
          errorMessage += `: ${err.message}`;
          console.error('Error stack:', err.stack);
        } else {
          console.error('Unknown error type:', typeof err);
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplate();
  }, [params.id, getById, supabase]);

  const handleUseTemplate = async () => {
    if (!template) return;

    try {
      let createdDocument;

      // Check if this is a global template (from templates table)
      if (template.owner_id === '') {
        // Use the RPC function for global templates
        const { user } = userStore.getState();
        if (!user) {
          toast.error('You must be logged in to use templates');
          return;
        }

        // Call the RPC function directly
        const { data: documentId, error: rpcError } = await supabase.rpc(
          'create_document_from_template',
          {
            template_id: template.id,
            user_id: user.id,
            title: `${template.title} - Copy`,
            description: template.description || '',
          }
        );

        if (rpcError) {
          console.error('Error creating document from template:', rpcError);
          throw new Error(`Failed to create document: ${rpcError.message}`);
        }

        if (documentId) {
          createdDocument = { id: documentId };
        } else {
          throw new Error('Failed to create document from template');
        }
      } else {
        // Regular document template
        createdDocument = await createDocument.mutate({
          title: `${template.title} - Copy`,
          description: template.description || '',
          document_type: template.document_type,
          status: 'draft',
          is_template: false,
          content: template.content,
          template_id: template.id,
        });
      }

      if (createdDocument) {
        toast.success('Template copied to documents', {
          description: 'You can now edit your new document',
          action: {
            label: 'Edit',
            onClick: () =>
              router.push(`/${username}/documents/${createdDocument.id}`),
          },
        });
        router.push(`/${username}/documents/${createdDocument.id}`);
      } else {
        throw new Error('Failed to create document from template');
      }
    } catch (err) {
      console.error('Error using template:', err);
      toast.error('Failed to use template', {
        description:
          err instanceof Error ? err.message : 'Unknown error occurred',
      });
    }
  };

  // Parse template content
  const parseTemplateContent = () => {
    if (!template?.content) {
      return { sections: [] };
    }

    try {
      // If content is already an object
      if (typeof template.content === 'object') {
        // Check if it has sections property
        const content = template.content as any;
        if (content.sections && Array.isArray(content.sections)) {
          return content;
        }
      }

      // If content is a JSON string
      if (typeof template.content === 'string') {
        try {
          const parsed = JSON.parse(template.content);
          if (parsed && typeof parsed === 'object' && parsed.sections) {
            return parsed;
          }
          // If parsed but no sections, create a section from the parsed content
          return {
            sections: [
              {
                title: 'Content',
                content: JSON.stringify(parsed, null, 2),
              },
            ],
          };
        } catch (e) {
          // Not valid JSON, treat as plain text
          return {
            sections: [
              {
                title: 'Content',
                content: template.content as string,
              },
            ],
          };
        }
      }

      // Default: treat as a single section with plain text
      return {
        sections: [
          {
            title: 'Content',
            content:
              typeof template.content === 'string'
                ? template.content
                : JSON.stringify(template.content, null, 2),
          },
        ],
      };
    } catch (error) {
      console.error('Error parsing template content:', error);
      return { sections: [] };
    }
  };

  // Default font settings

  // Create a styled HTML document for preview
  const generatePreviewHTML = () => {
    if (!template) return '';

    let previewHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${template.title}</title>
        <style>
          body {
            font-family: 'Times New Roman', Times, serif;
            line-height: 1.5;
            color: #000;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #fff;
          }
          h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            text-align: center;
          }
          h2 {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
          }
          h3 {
            font-size: 1.1rem;
            font-weight: bold;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
          }
          p {
            margin-bottom: 0.75rem;
            text-align: justify;
          }
          .document-header {
            text-align: center;
            margin-bottom: 2rem;
          }
          .document-section {
            margin-bottom: 1.5rem;
          }
          .document-footer {
            margin-top: 2rem;
            padding-top: 0.75rem;
            border-top: 1px solid #000;
            font-size: 0.8rem;
          }
          @media print {
            body {
              padding: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="document-header">
          <h1>${template.title}</h1>
          ${template.description ? `<p>${template.description}</p>` : ''}
        </div>
    `;

    const parsedContent = parseTemplateContent();

    parsedContent.sections.forEach((section: any) => {
      previewHTML += `
        <div class="document-section">
          ${section.title ? `<h2>${section.title}</h2>` : ''}
          <div style="white-space: pre-wrap;">${section.content}</div>
        </div>
      `;
    });

    previewHTML += `
        <div class="document-footer">
          <p>Template ID: ${template.id}</p>
          <p>Document Type: ${template.document_type}</p>
          <p>Created by Forms Notamess | ${new Date().toLocaleDateString()}</p>
        </div>
      </body>
      </html>
    `;

    return previewHTML;
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="w-full h-[600px]" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !template) {
    return (
      <div className="container py-8">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Template Preview</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="p-4 border rounded-md bg-gray-50 min-h-[400px] flex items-center justify-center">
              <div className="text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-center text-muted-foreground">
                  {error || 'Template not found'}
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => router.back()}
                >
                  Go Back
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            Template Preview: {template.title}
          </h1>
        </div>
        <Button onClick={handleUseTemplate}>Use Template</Button>
      </div>

      <Card>
        <CardContent className="pt-6">
          <iframe
            title="Template Preview"
            srcDoc={generatePreviewHTML()}
            className="w-full h-[600px] border rounded-md"
            sandbox="allow-same-origin"
          />
        </CardContent>
      </Card>
    </div>
  );
}
