'use client';

import { LawyerReviews } from '@/components/lawyer/LawyerReviews';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RatingDisplay, RatingInput } from '@/components/ui/rating';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers } from '@/lib/hooks';
import {
  BookOpen,
  BriefcaseBusiness,
  Calendar,
  CheckCircle2,
  Clock,
  FileText,
  Gavel,
  GraduationCap,
  Languages,
  Mail,
  MessageSquare,
  Phone,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function LawyerProfilePage() {
  const params = useParams();
  const username = params.username as string;
  const lawyerId = params.id as string;
  const router = useRouter();

  // Get lawyer data from the hook
  const { getLawyerById, reviewLawyer, fetchLawyerReviews } = useLawyers();
  const [lawyer, setLawyer] = useState<any>(null);
  const [loadingLawyer, setLoadingLawyer] = useState(true);
  const [userRating, setUserRating] = useState(0);
  const [reviewText, setReviewText] = useState('');
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [reviews, setReviews] = useState<any[]>([]);
  const [loadingReviews, setLoadingReviews] = useState(true);
  const [activeTab, setActiveTab] = useState('about');

  // Fetch lawyer data and reviews on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingLawyer(true);
        setLoadingReviews(true);

        // Fetch lawyer profile
        const lawyerData = await getLawyerById(lawyerId);
        setLawyer(lawyerData);

        // Fetch lawyer reviews
        const reviewsData = await fetchLawyerReviews(lawyerId);
        setReviews(reviewsData || []);
      } catch (error) {
        console.error('Error fetching lawyer data:', error);
        toast.error('Failed to load lawyer profile');
      } finally {
        setLoadingLawyer(false);
        setLoadingReviews(false);
      }
    };

    if (lawyerId) {
      fetchData();
    }
  }, [lawyerId, getLawyerById, fetchLawyerReviews]);

  // Handle submitting a review
  const handleSubmitReview = async () => {
    if (userRating === 0) {
      toast.error('Please select a rating');
      return;
    }

    try {
      setIsSubmittingReview(true);
      await reviewLawyer(lawyerId, userRating, reviewText);

      // Refresh lawyer data to show updated rating
      const updatedLawyer = await getLawyerById(lawyerId);
      setLawyer(updatedLawyer);

      // Refresh reviews to show the new review
      const updatedReviews = await fetchLawyerReviews(lawyerId);
      setReviews(updatedReviews || []);

      // Reset form
      setUserRating(0);
      setReviewText('');

      // Switch to reviews tab to show the new review
      setActiveTab('reviews');

      toast.success('Review submitted successfully');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    } finally {
      setIsSubmittingReview(false);
    }
  };

  if (loadingLawyer) {
    return (
      <div className="container py-6 max-w-4xl">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/3">
            <Card>
              <CardContent className="p-6 flex flex-col items-center">
                <Skeleton className="h-32 w-32 rounded-full" />
                <Skeleton className="h-6 w-40 mt-4" />
                <Skeleton className="h-4 w-24 mt-2" />
                <Skeleton className="h-4 w-32 mt-1" />
                <div className="flex gap-2 mt-4">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="md:w-2/3">
            <Card>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-40 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-6" />

                <Skeleton className="h-6 w-32 mb-3" />
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>

                <Skeleton className="h-6 w-32 mb-3" />
                <div className="flex gap-2 mb-6">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!lawyer) {
    return (
      <div className="container py-6 max-w-4xl text-center">
        <Gavel className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">Lawyer not found</h3>
        <p className="text-muted-foreground mt-2">
          The lawyer profile you're looking for doesn't exist or has been
          removed
        </p>
        <Button
          className="mt-4"
          onClick={() => router.push(`/${username}/lawyer/find`)}
        >
          Back to Lawyer Search
        </Button>
      </div>
    );
  }

  return (
    <div className="container py-6 max-w-4xl">
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/3">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center">
                <div className="relative">
                  <UserAvatar
                    fallbackText={lawyer.full_name}
                    avatarUrl={lawyer.avatar_url || lawyer.profile_image_url}
                    size="xl"
                    className="h-32 w-32"
                  />
                  {lawyer.is_verified && (
                    <div className="absolute bottom-0 right-0 rounded-full bg-white p-0.5">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                    </div>
                  )}
                </div>

                <h2 className="text-xl font-semibold mt-4">
                  {lawyer.full_name}
                </h2>

                <div className="mt-1">
                  <RatingDisplay
                    rating={lawyer.average_rating || 0}
                    reviewCount={reviews.length}
                    size="sm"
                  />
                </div>

                <div className="flex items-center gap-1 text-sm text-neutral-500 mt-1">
                  <Gavel className="h-3.5 w-3.5" />
                  <span>
                    {lawyer.specialization?.join(', ') || 'General Practice'}
                  </span>
                </div>

                <div className="flex gap-2 mt-6 w-full">
                  <Button
                    className="flex-1"
                    onClick={() =>
                      router.push(
                        `/${username}/lawyer/book?lawyer_id=${lawyer.id}`
                      )
                    }
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    Book
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Message
                  </Button>
                </div>

                <Separator className="my-6" />

                <div className="w-full space-y-4">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Email</h3>
                      <p className="text-sm">
                        {lawyer.email || 'Not provided'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Phone</h3>
                      <p className="text-sm">
                        {lawyer.phone || 'Not provided'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Languages className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Languages</h3>
                      <p className="text-sm">
                        {lawyer.languages?.join(', ') || 'Not specified'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-neutral-500 mt-0.5" />
                    <div>
                      <h3 className="text-sm font-medium">Experience</h3>
                      <p className="text-sm">
                        {lawyer.years_experience
                          ? `${lawyer.years_experience} years`
                          : 'Not specified'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:w-2/3">
          <Card className="mb-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <CardHeader>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="about">About</TabsTrigger>
                  <TabsTrigger value="expertise">Expertise</TabsTrigger>
                  <TabsTrigger value="reviews">
                    Reviews ({reviews.length})
                  </TabsTrigger>
                </TabsList>
              </CardHeader>
              <CardContent className="p-6">
                <TabsContent value="about" className="mt-0">
                  <h3 className="text-lg font-medium mb-3">About</h3>
                  <p className="text-neutral-700 whitespace-pre-line">
                    {lawyer.bio || 'No bio available for this lawyer.'}
                  </p>
                </TabsContent>

                <TabsContent value="expertise" className="mt-0">
                  <h3 className="text-lg font-medium mb-4">Expertise</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start gap-3">
                      <BriefcaseBusiness className="h-5 w-5 text-neutral-500 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium">Specializations</h4>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {lawyer.specialization?.map((spec: string) => (
                            <Badge key={spec} variant="outline">
                              {spec}
                            </Badge>
                          )) || (
                            <span className="text-sm text-neutral-500">
                              Not specified
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <GraduationCap className="h-5 w-5 text-neutral-500 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium">Education</h4>
                        <p className="text-sm">
                          {lawyer.education || 'Not specified'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <FileText className="h-5 w-5 text-neutral-500 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium">Practice Areas</h4>
                        <p className="text-sm">
                          {lawyer.specialization?.join(', ') || 'Not specified'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3">
                      <BookOpen className="h-5 w-5 text-neutral-500 mt-0.5" />
                      <div>
                        <h4 className="text-sm font-medium">Publications</h4>
                        <p className="text-sm">Not available</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="reviews" className="mt-0">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">Client Reviews</h3>
                  </div>
                  <LawyerReviews reviews={reviews} loading={loadingReviews} />
                </TabsContent>
              </CardContent>
            </Tabs>
          </Card>

          <Card className="mb-6">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Consultation Options</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Video Consultation</h4>
                      <p className="text-sm text-neutral-500">30-60 minutes</p>
                    </div>
                    <Badge>Recommended</Badge>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-baseline gap-1">
                      <span className="text-lg font-semibold">
                        ${lawyer.consultation_fee || 0}
                      </span>
                      <span className="text-sm text-neutral-500">flat fee</span>
                    </div>
                  </div>
                  <Button
                    className="w-full"
                    onClick={() =>
                      router.push(
                        `/${username}/lawyer/book?lawyer_id=${lawyer.id}&type=video`
                      )
                    }
                  >
                    Book Video Consultation
                  </Button>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">Hourly Services</h4>
                      <p className="text-sm text-neutral-500">
                        Ongoing legal work
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-baseline gap-1">
                      <span className="text-lg font-semibold">
                        ${lawyer.hourly_rate || 0}
                      </span>
                      <span className="text-sm text-neutral-500">/hour</span>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() =>
                      router.push(
                        `/${username}/lawyer/book?lawyer_id=${lawyer.id}&type=hourly`
                      )
                    }
                  >
                    Request Hourly Services
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Rate & Review</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <RatingInput
                    value={userRating}
                    onChange={setUserRating}
                    label="Your Rating"
                    size="md"
                  />
                </div>

                <div>
                  <label
                    htmlFor="review-text"
                    className="block text-sm font-medium mb-1"
                  >
                    Your Review (Optional)
                  </label>
                  <textarea
                    id="review-text"
                    className="w-full min-h-[100px] p-3 border rounded-md"
                    placeholder="Share your experience with this lawyer..."
                    value={reviewText}
                    onChange={(e) => setReviewText(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleSubmitReview}
                  disabled={userRating === 0 || isSubmittingReview}
                  className="w-full md:w-auto"
                >
                  {isSubmittingReview ? 'Submitting...' : 'Submit Review'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
