'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CheckCircle, XCircle } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';

// Loading component to show while suspense is active
function VerifyPageLoading() {
  return (
    <div className="container flex items-center justify-center min-h-screen py-12">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">
            Email Verification
          </CardTitle>
          <CardDescription>Verifying your email...</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <div className="h-12 w-12 rounded-full border-4 border-t-accent-500 animate-spin" />
        </CardContent>
      </Card>
    </div>
  );
}

// Client component that uses useSearchParams
function VerifyPageClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [verificationStatus, setVerificationStatus] = useState<
    'loading' | 'success' | 'error'
  >('loading');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    // The verification is handled automatically by Supabase when the user clicks the link
    // We just need to check if there's an error parameter in the URL
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      setVerificationStatus('error');
      setErrorMessage(errorDescription || 'Verification failed');
    } else {
      setVerificationStatus('success');

      // Redirect to login after a delay
      const timer = setTimeout(() => {
        router.push('/login');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [searchParams, router]);

  return (
    <div className="container flex items-center justify-center min-h-screen py-12">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">
            Email Verification
          </CardTitle>
          <CardDescription>
            {verificationStatus === 'loading' && 'Verifying your email...'}
            {verificationStatus === 'success' &&
              'Your email has been verified successfully.'}
            {verificationStatus === 'error' &&
              'There was a problem verifying your email.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-8">
          {verificationStatus === 'loading' && (
            <div className="h-12 w-12 rounded-full border-4 border-t-accent-500 animate-spin" />
          )}
          {verificationStatus === 'success' && (
            <CheckCircle className="h-16 w-16 text-green-500" />
          )}
          {verificationStatus === 'error' && (
            <>
              <XCircle className="h-16 w-16 text-red-500" />
              <p className="mt-4 text-center text-sm text-muted-foreground">
                {errorMessage}
              </p>
            </>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          {verificationStatus === 'success' && (
            <div className="text-center">
              <p className="mb-4 text-sm text-muted-foreground">
                You will be redirected to the login page in a few seconds.
              </p>
              <Button asChild>
                <Link href="/login">Go to Login</Link>
              </Button>
            </div>
          )}
          {verificationStatus === 'error' && (
            <Button asChild>
              <Link href="/login">Back to Login</Link>
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

// Main page component with Suspense boundary
export default function VerifyPage() {
  return (
    <Suspense fallback={<VerifyPageLoading />}>
      <VerifyPageClient />
    </Suspense>
  );
}
