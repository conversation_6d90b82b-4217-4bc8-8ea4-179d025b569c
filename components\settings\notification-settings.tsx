'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSettings } from '@/lib/hooks';
import { UserSettings } from '@/lib/types/database-modules';
import { Bell, Loader2, Mail } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface NotificationSettingsProps {
  initialSettings: UserSettings | null;
}

export function NotificationSettings({
  initialSettings,
}: NotificationSettingsProps) {
  const { updateNotificationSettings, sendTestNotification } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);

  // State for notification settings
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(
    initialSettings?.notifications_enabled ?? true
  );

  const [emailSettings, setEmailSettings] = useState({
    document_updates:
      initialSettings?.email_notifications?.document_updates ?? true,
    signatures: initialSettings?.email_notifications?.signatures ?? true,
    consultations: initialSettings?.email_notifications?.consultations ?? true,
    payments: initialSettings?.email_notifications?.payments ?? true,
    marketing: initialSettings?.email_notifications?.marketing ?? false,
    comment_added: initialSettings?.email_notifications?.comment_added ?? true,
    task_assigned: initialSettings?.email_notifications?.task_assigned ?? true,
    document_shared:
      initialSettings?.email_notifications?.document_shared ?? true,
    document_updated:
      initialSettings?.email_notifications?.document_updated ?? true,
  });

  // Handle saving notification settings
  const saveNotificationSettings = async () => {
    setIsSaving(true);

    const updatePromise = updateNotificationSettings({
      notifications_enabled: notificationsEnabled,
      email_notifications: emailSettings,
    });

    toast.promise(updatePromise, {
      loading: 'Saving notification settings...',
      success: 'Notification settings saved successfully!',
      error: 'Failed to save notification settings',
    });

    try {
      await updatePromise;
    } catch (error) {
      console.error('Error saving notification settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle sending test notification
  const handleSendTestNotification = async (type: 'email' | 'app' | 'sms') => {
    setIsSendingTest(true);

    const testPromise = sendTestNotification(type);

    toast.promise(testPromise, {
      loading: `Sending test ${type} notification...`,
      success: `Test ${type} notification sent successfully!`,
      error: `Failed to send test ${type} notification`,
    });

    try {
      await testPromise;
    } catch (error) {
      console.error(`Error sending test ${type} notification:`, error);
    } finally {
      setIsSendingTest(false);
    }
  };

  // If no settings are provided, show skeleton
  if (!initialSettings) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-center justify-between">
              <Skeleton className="h-4 w-1/3" />
              <Skeleton className="h-6 w-10" />
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Settings</CardTitle>
        <CardDescription>
          Manage how and when you receive notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="notifications-enabled">Enable Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Turn all notifications on or off
            </p>
          </div>
          <Switch
            id="notifications-enabled"
            checked={notificationsEnabled}
            onCheckedChange={setNotificationsEnabled}
          />
        </div>

        {notificationsEnabled && (
          <Tabs defaultValue="email" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email">
                <Mail className="mr-2 h-4 w-4" />
                Email
              </TabsTrigger>
              <TabsTrigger value="app">
                <Bell className="mr-2 h-4 w-4" />
                In-App
              </TabsTrigger>
            </TabsList>

            <TabsContent value="email" className="space-y-4 pt-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email-document-updates">
                    Document Updates
                  </Label>
                  <Switch
                    id="email-document-updates"
                    checked={emailSettings.document_updates}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        document_updates: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-signatures">Signatures</Label>
                  <Switch
                    id="email-signatures"
                    checked={emailSettings.signatures}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        signatures: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-consultations">Consultations</Label>
                  <Switch
                    id="email-consultations"
                    checked={emailSettings.consultations}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        consultations: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-payments">Payments</Label>
                  <Switch
                    id="email-payments"
                    checked={emailSettings.payments}
                    onCheckedChange={(checked) =>
                      setEmailSettings({ ...emailSettings, payments: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-comments">Comments</Label>
                  <Switch
                    id="email-comments"
                    checked={emailSettings.comment_added}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        comment_added: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-tasks">Tasks</Label>
                  <Switch
                    id="email-tasks"
                    checked={emailSettings.task_assigned}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        task_assigned: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="email-document-shared">
                    Document Sharing
                  </Label>
                  <Switch
                    id="email-document-shared"
                    checked={emailSettings.document_shared}
                    onCheckedChange={(checked) =>
                      setEmailSettings({
                        ...emailSettings,
                        document_shared: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-marketing">Marketing</Label>
                    <p className="text-xs text-muted-foreground">
                      Receive updates about new features and promotions
                    </p>
                  </div>
                  <Switch
                    id="email-marketing"
                    checked={emailSettings.marketing}
                    onCheckedChange={(checked) =>
                      setEmailSettings({ ...emailSettings, marketing: checked })
                    }
                  />
                </div>
              </div>

              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSendTestNotification('email')}
                  disabled={isSendingTest}
                >
                  {isSendingTest ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      Send Test Email
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="app" className="space-y-4 pt-4">
              <p className="text-sm text-muted-foreground">
                In-app notifications are always enabled for important updates.
              </p>

              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSendTestNotification('app')}
                  disabled={isSendingTest}
                >
                  {isSendingTest ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Bell className="mr-2 h-4 w-4" />
                      Send Test Notification
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={saveNotificationSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
