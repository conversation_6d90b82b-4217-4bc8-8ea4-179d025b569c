'use client';

import { useWizard } from 'react-use-wizard';
import { ArrowLef<PERSON>, ArrowRight } from 'lucide-react';
import BaseButton from '@/components/ux/comp/BaseButton';

const WizardNavigation = () => {
  const { isFirstStep, isLastStep, handleStep, previousStep, nextStep } =
    useWizard();

  return (
    <div className='flex justify-end gap-5'>
      {!isFirstStep && (
        <BaseButton tooltipLabel='Previous step' onClick={previousStep}>
          <ArrowLeft className='size-4' />
          <span>Back</span>
        </BaseButton>
      )}
      <BaseButton
        tooltipLabel='next step'
        disabled={isLastStep}
        onClick={nextStep}
      >
        <span>Next</span>
        <ArrowRight className='size-4' />
      </BaseButton>
    </div>
  );
};

export default WizardNavigation;
