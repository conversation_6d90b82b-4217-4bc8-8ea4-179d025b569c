import { Database } from '@/lib/supabase/database-types';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: async (name) => (await cookies()).get(name)?.value,
          set: async (name, value, options) => {
            (await cookies()).set({ name, value, ...options });
          },
          remove: async (name, options) => {
            (await cookies()).set({ name, value: '', ...options });
          },
        },
      }
    );
    const { token } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Token is required' }, { status: 400 });
    }

    // Get the password from the request body
    const { password } = await request.json();

    if (!password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }

    // Get the share link details
    const { data: shareLink, error: shareLinkError } = await supabase
      .from('document_share_links')
      .select('id, access_type, edit_password, permission, expires_at')
      .eq('token', token)
      .eq('is_active', true)
      .single();

    if (shareLinkError) {
      if (shareLinkError.code === 'PGRST116') {
        // No rows returned - link not found
        return NextResponse.json(
          { error: 'This share link is invalid or has been removed' },
          { status: 404 }
        );
      }

      // Other database errors
      console.error('Database error fetching share link:', shareLinkError);
      return NextResponse.json(
        { error: 'Unable to verify the share link' },
        { status: 500 }
      );
    }

    if (!shareLink) {
      return NextResponse.json(
        { error: 'Invalid share link' },
        { status: 404 }
      );
    }

    // Check if the link has expired
    if (shareLink.expires_at && new Date(shareLink.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This share link has expired' },
        { status: 403 }
      );
    }

    // Check if the link allows editing and requires a password
    if (
      shareLink.permission !== 'edit' ||
      shareLink.access_type !== 'password_protected'
    ) {
      return NextResponse.json(
        { error: 'This link does not support password-protected editing' },
        { status: 403 }
      );
    }

    // Ensure edit_password is present
    if (!shareLink.edit_password) {
      return NextResponse.json(
        { error: 'No password is set for this share link' },
        { status: 400 }
      );
    }

    // Verify the password
    const { data: verifyResult, error: verifyError } = await supabase.rpc(
      'verify_password',
      {
        password,
        hash: shareLink.edit_password as string,
      }
    );

    if (verifyError) {
      console.error('Error verifying password:', verifyError);
      return NextResponse.json(
        { error: 'Failed to verify password' },
        { status: 500 }
      );
    }

    // Return the result
    return NextResponse.json({
      isValid: verifyResult === true,
    });
  } catch (error) {
    console.error('Error in verify password API:', error);
    return NextResponse.json(
      {
        error:
          'We encountered an error while processing your request. Please try again later.',
      },
      { status: 500 }
    );
  }
}
