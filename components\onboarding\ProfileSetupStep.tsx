'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ImageCropper } from '@/components/ui/image-cropper';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { avatarService } from '@/lib/services/avatar-service';
import { userStore } from '@/lib/store/user';
import { supabaseClient as supabase } from '@/lib/supabase/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Define the type for our form values
interface ProfileFormValues {
  full_name: string;
  username: string;
  bio?: string; // We'll collect this but store it in the lawyer profile if needed
  avatar_url?: string;
}

interface ProfileSetupStepProps {
  onComplete: (data: ProfileFormValues) => void;
  onFieldUpdate?: (field: string, value: any) => Promise<void>;
}

// Define the validation schema
const createValidationSchema = (currentUsername: string | null | undefined) => {
  return z.object({
    full_name: z.string().min(1, 'Full name is required'),
    username: z
      .string()
      .min(3, 'Username must be at least 3 characters')
      .regex(
        /^[a-z0-9_-]+$/,
        'Username can only contain lowercase letters, numbers, underscores, and hyphens'
      )
      .refine(
        async (username) => {
          // Skip validation if username is the same as the current user's
          if (currentUsername === username) return true;

          // Check if username is already taken
          const { data } = await supabase
            .from('profiles')
            .select('username')
            .eq('username', username)
            .maybeSingle();

          return !data; // Return true if no data (username not taken)
        },
        {
          message: 'Username is already taken',
        }
      ),
    bio: z.string().optional(),
    avatar_url: z.string().optional(),
  });
};

export function ProfileSetupStep({
  onComplete,
  onFieldUpdate,
}: ProfileSetupStepProps) {
  const { profile } = userStore();
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [cropperOpen, setCropperOpen] = useState(false);

  // Initialize the form with default values
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(createValidationSchema(profile?.username)),
    defaultValues: {
      full_name: profile?.full_name || '',
      username: profile?.username || '',
      bio: '',
      avatar_url: profile?.avatar_url || '',
    },
  });

  // Update form values when profile changes
  useEffect(() => {
    if (profile) {
      form.reset({
        full_name: profile.full_name || '',
        username: profile.username || '',
        bio: '',
        avatar_url: profile.avatar_url || '',
      });
    }
  }, [profile, form]);

  // Add field change handlers for auto-saving
  const handleFieldChange = async (field: string, value: any) => {
    if (!onFieldUpdate) return;

    try {
      // Only save if the value is different from the current profile value
      if (profile && profile[field as keyof typeof profile] !== value) {
        await onFieldUpdate(field, value);
      }
    } catch (error) {
      console.error(`Error saving ${field}:`, error);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !profile) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    setSelectedFile(file);
    setCropperOpen(true);
  };

  const handleCropComplete = async (croppedImageBlob: Blob) => {
    if (!profile) return;

    setAvatarLoading(true);

    try {
      // Convert blob to file
      const file = new File([croppedImageBlob], 'profile-image.jpg', {
        type: 'image/jpeg',
      });

      // Upload the file using the avatar service
      // This will use a consistent filename pattern and handle cleanup
      const avatarUrl = await avatarService.uploadAvatar(file, profile.id);

      if (!avatarUrl) {
        throw new Error('Failed to upload avatar');
      }

      // Update the form value
      form.setValue('avatar_url', avatarUrl);

      // Save the avatar URL to the database if onFieldUpdate is provided
      if (onFieldUpdate) {
        await onFieldUpdate('avatar_url', avatarUrl);
      }

      toast.success('Profile picture uploaded successfully');
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error('Failed to upload profile picture');
    } finally {
      setAvatarLoading(false);
      setSelectedFile(null);
    }
  };

  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    try {
      // If we're here, the username validation has already passed
      onComplete(data);
    } catch (error) {
      console.error('Error submitting profile:', error);
      toast.error('Failed to save profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If profile is not loaded yet, show a loading state
  if (!profile) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Set Up Your Profile</h1>
        <p className="text-neutral-500">
          Let's personalize your experience with some basic information.
        </p>
      </div>

      {/* Image Cropper */}
      <ImageCropper
        open={cropperOpen}
        onClose={() => setCropperOpen(false)}
        onCropComplete={handleCropComplete}
        imageFile={selectedFile}
        aspectRatio={1} // Square crop
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="flex flex-col items-center gap-4">
            <Avatar className="h-24 w-24">
              <AvatarImage
                src={
                  form.getValues('avatar_url') ||
                  `https://ui-avatars.com/api/?name=${encodeURIComponent(
                    form.getValues('full_name') || 'User'
                  )}&background=random`
                }
                alt={form.getValues('full_name')}
              />
              <AvatarFallback>
                {(form.getValues('full_name') || 'U')
                  .split(' ')
                  .map((n) => n[0])
                  .join('')
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>

            <div>
              <label
                htmlFor="avatar-upload"
                className="cursor-pointer inline-flex items-center gap-2 px-4 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-md transition-colors"
              >
                {avatarLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
                <span className="text-sm font-medium">
                  {avatarLoading ? 'Uploading...' : 'Upload Photo'}
                </span>
              </label>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileSelect}
                disabled={avatarLoading}
              />
            </div>
          </div>

          <div className="space-y-4">
            <FormField
              control={form.control}
              name="full_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John Doe"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleFieldChange('full_name', e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Your name as it will appear on your profile.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="johndoe"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        // We'll use onBlur for username to avoid too many validation checks
                      }}
                      onBlur={(e) => {
                        field.onBlur();
                        handleFieldChange('username', e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Your unique username for NotAMess Forms. This will be used
                    in your profile URL.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bio</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Tell us a little about yourself"
                      className="resize-none"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        // Use debounce for bio to avoid too many updates
                        // We'll save on blur instead
                      }}
                      onBlur={(e) => {
                        field.onBlur();
                        handleFieldChange('bio', e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    A brief description about yourself (optional).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting || avatarLoading}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Profile'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
