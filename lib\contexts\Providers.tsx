'use client';

import { UserProvider } from '@/components/providers/user-provider';
import RealtimeProvider from '@/components/providers/realtime-provider';
import React from 'react';
import AuthProvider from './AuthProvider';

type ProvidersProps = {
  children: React.ReactNode;
};

const Providers = ({ children }: ProvidersProps) => {
  return (
    <AuthProvider>
      <UserProvider>
        <RealtimeProvider>{children}</RealtimeProvider>
      </UserProvider>
    </AuthProvider>
  );
};

export default Providers;
