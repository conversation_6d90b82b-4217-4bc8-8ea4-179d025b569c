'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card<PERSON>ontent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DocCard } from '@/components/ux/comp/doc-card';
import { DocumentType } from '@/lib/types/database-modules';
import { createMemoizedComponent } from '@/lib/utils/performance-optimizations';
import { formatDistanceToNow } from 'date-fns';
import { Eye as EyeIcon, FileText as FileTextIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

// Template interface
export interface Template {
  id: string;
  title: string;
  description: string | null;
  document_type: DocumentType;
  content: any;
  created_at: string;
  is_global: boolean;
  created_by: string | null;
  category: string | null;
  // thumbnail_url is no longer used
}

interface TemplateCardProps {
  template: Template;
  username: string;
  onUseTemplate: (template: Template) => void;
}

function TemplateCardComponent({
  template,
  username,
  onUseTemplate,
}: TemplateCardProps) {
  const router = useRouter();

  return (
    <DocCard className="mb-4">
      <div className="h-full flex flex-col">
        <CardHeader className="pb-2 pt-6 px-6">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">{template.title}</CardTitle>
              <CardDescription className="line-clamp-2 mt-1">
                {template.description || 'No description'}
              </CardDescription>
            </div>
            {template.is_global && (
              <Badge variant="outline" className="bg-blue-50">
                Global
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="flex-grow px-6 py-4">
          <div className="aspect-video bg-neutral-50 rounded-md flex items-center justify-center mb-4 shadow-sm">
            <FileTextIcon className="h-12 w-12 text-neutral-300" />
          </div>
          <div className="flex flex-wrap gap-2 mt-3">
            <Badge variant="secondary">{template.document_type}</Badge>
            {template.category && (
              <Badge variant="outline">{template.category}</Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="pt-4 pb-6 px-6 flex justify-between border-t mt-auto">
          <div className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(template.created_at), {
              addSuffix: true,
            })}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                router.push(`/${username}/documents/templates/${template.id}`)
              }
            >
              <EyeIcon className="h-3 w-3 mr-1" />
              Preview
            </Button>
            <Button size="sm" onClick={() => onUseTemplate(template)}>
              Use Template
            </Button>
          </div>
        </CardFooter>
      </div>
    </DocCard>
  );
}

// Export a memoized version of the component
export const TemplateCard = createMemoizedComponent(TemplateCardComponent);
