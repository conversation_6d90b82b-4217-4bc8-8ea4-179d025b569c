'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOrganizations } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Crown, Loader2, Shield, User, UserCog } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Define the types we need
type OrganizationRole = 'owner' | 'admin' | 'member';

// Extended member type to include profile information
interface ExtendedOrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: OrganizationRole;
  created_at: string;
  updated_at: string;
  full_name?: string;
  email?: string;
  avatar_url?: string | null;
}

const changeRoleSchema = z.object({
  role: z.enum(['owner', 'admin', 'member']),
});

type ChangeRoleFormValues = z.infer<typeof changeRoleSchema>;

export default function EditMemberRolePage() {
  const router = useRouter();
  const { username, id, memberId } = useParams();
  const { getOrganization, updateMemberRole } = useOrganizations();
  const [isUpdating, setIsUpdating] = useState(false);
  const [member, setMember] = useState<ExtendedOrganizationMember | null>(null);
  const [organizationName, setOrganizationName] = useState('');
  const [loading, setLoading] = useState(true);

  const form = useForm<ChangeRoleFormValues>({
    resolver: zodResolver(changeRoleSchema),
    defaultValues: {
      role: 'member',
    },
  });

  // Fetch member and organization data
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        // Ensure we have valid IDs
        if (!id || !memberId) {
          toast.error('Invalid organization or member ID');
          setLoading(false);
          return;
        }

        const orgId = Array.isArray(id) ? id[0] : id;
        const mId = Array.isArray(memberId) ? memberId[0] : memberId;

        // Get organization with members using the hook
        const organization = await getOrganization(orgId);

        if (!organization) {
          toast.error('Organization not found');
          setLoading(false);
          return;
        }

        // Set organization name
        setOrganizationName(organization.name);

        // Find the specific member
        const foundMember = organization.members.find((m) => m.id === mId);

        if (foundMember) {
          // Create a properly typed member object
          const formattedMember: ExtendedOrganizationMember = {
            id: foundMember.id,
            organization_id: foundMember.organization_id,
            user_id: foundMember.user_id,
            role: foundMember.role as OrganizationRole,
            created_at: foundMember.created_at,
            updated_at: foundMember.updated_at,
            // Use the user object from the member
            full_name: foundMember.user?.full_name,
            email: foundMember.user?.email,
            avatar_url: foundMember.user?.avatar_url,
          };

          setMember(formattedMember);
          form.setValue('role', formattedMember.role);
        } else {
          toast.error('Member not found');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load member data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [id, memberId, form, getOrganization]);

  async function onSubmit(values: ChangeRoleFormValues) {
    if (!member) return;

    setIsUpdating(true);

    try {
      // Use toast.promise for better UX
      toast.promise(
        // Create a promise that we can await
        async () => {
          const success = await updateMemberRole(member.id, values.role);
          if (!success) {
            throw new Error('Failed to update role');
          }
          return success;
        },
        {
          loading: 'Updating role...',
          success: () => {
            // Navigate back to the members page after success
            const orgId = Array.isArray(id) ? id[0] : id;
            router.push(`/${username}/organizations/${orgId}/members`);
            return `Role updated to ${values.role}`;
          },
          error: 'Failed to update role',
        }
      );
    } catch (error) {
      console.error('Error updating role:', error);
    } finally {
      setIsUpdating(false);
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`}
          >
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Loading...</h1>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <div className="h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-10 w-full bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!member) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`}
          >
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Member Not Found</h1>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <UserCog className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Member Not Found</h3>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              The member you're looking for doesn't exist or you don't have
              access to it.
            </p>
            <Link
              href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`}
            >
              <Button>Go Back to Members</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link
          href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`}
        >
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Edit Member Role</h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Change Role for {member.full_name || 'Member'}
          </CardTitle>
          <CardDescription>
            Update the role for this member in {organizationName}.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Member Information</h3>
                <div className="flex items-center gap-3 p-3 border rounded-md">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-neutral-100 flex items-center justify-center overflow-hidden">
                    {member.avatar_url ? (
                      <img
                        src={member.avatar_url}
                        alt={member.full_name || 'Member'}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <User className="h-5 w-5 text-neutral-500" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">
                      {member.full_name || 'Unknown User'}
                    </div>
                    <div className="text-sm text-neutral-500">
                      {member.email || 'No email'}
                    </div>
                  </div>
                </div>
              </div>

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="space-y-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="owner" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center gap-2">
                            <Crown className="h-4 w-4 text-yellow-600" />
                            <div>
                              <div>Owner</div>
                              <p className="text-xs text-muted-foreground">
                                Full control over the organization, can delete
                                it and manage billing
                              </p>
                            </div>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="admin" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center gap-2">
                            <Shield className="h-4 w-4 text-blue-600" />
                            <div>
                              <div>Admin</div>
                              <p className="text-xs text-muted-foreground">
                                Can manage members, teams, and settings, but
                                cannot delete the organization
                              </p>
                            </div>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="member" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-600" />
                            <div>
                              <div>Member</div>
                              <p className="text-xs text-muted-foreground">
                                Can view and use organization resources, but
                                cannot manage settings
                              </p>
                            </div>
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormDescription>
                      Choose the appropriate role for this member
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    router.push(
                      `/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`
                    )
                  }
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Role'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
