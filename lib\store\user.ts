import { User } from '@supabase/supabase-js';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Database } from '../supabase/db';

type user = User | null;
export type profile = Database['public']['Tables']['profiles']['Row'] | null;

type State = {
  user: user;
  profile: profile;
};

type Action = {
  updateUser: (user: State['user']) => void;
  removeUser: (user: State['user']) => void;
  updateProfile: (profile: State['profile']) => void;
  removeProfile: (profile: State['profile']) => void;
};

// Create your store, which includes both state and (optionally) actions
export const userStore = create(
  persist<State & Action>(
    (set) => ({
      user: null,
      profile: null,
      updateUser: (user) => set(() => ({ user: user })),
      removeUser: () => set(() => ({ user: null })),
      updateProfile: (profile) => set(() => ({ profile: profile })),
      removeProfile: () => set(() => ({ profile: null })),
    }),
    {
      name: 'user-store', // unique name
    }
  )
);
