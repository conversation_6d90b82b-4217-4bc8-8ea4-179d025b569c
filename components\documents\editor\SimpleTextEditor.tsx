'use client';

import * as Textarea from '@/components/ui/textarea';
import { useEffect, useState } from 'react';

interface SimpleTextEditorProps {
  initialContent?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  className?: string;
  maxLength?: number;
}

/**
 * A simple text editor component that uses the Textarea.Root component.
 * This is useful for simple text editing without rich text formatting.
 */
export function SimpleTextEditor({
  initialContent = '',
  placeholder = 'Start writing your content...',
  onChange,
  className,
  maxLength = 2000,
}: SimpleTextEditorProps) {
  const [content, setContent] = useState(initialContent);

  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);

    if (onChange) {
      onChange(newContent);
    }
  };

  return (
    <Textarea.Root
      value={content}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      rows={8}
    >
      <Textarea.CharCounter current={content.length} max={maxLength} />
    </Textarea.Root>
  );
}
