import { createClient } from '@supabase/supabase-js';
import CryptoJS from 'crypto-js';

export interface DocumentVersion {
  id: string;
  documentId: string;
  versionNumber: number;
  content: string;
  createdAt: Date;
  createdBy: string;
  changeDescription: string;
  contentHash: string;
}

export interface DocumentComparison {
  added: string[];
  removed: string[];
  changed: Array<{
    original: string;
    updated: string;
  }>;
  unchanged: string[];
}

export class DocumentVersionService {
  private static instance: DocumentVersionService;
  private supabase;

  private constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
    );
  }

  public static getInstance(): DocumentVersionService {
    if (!DocumentVersionService.instance) {
      DocumentVersionService.instance = new DocumentVersionService();
    }
    return DocumentVersionService.instance;
  }

  /**
   * Creates a hash of document content for integrity validation
   */
  private generateContentHash(content: string): string {
    return CryptoJS.SHA256(content).toString();
  }

  /**
   * Creates a new version of a document
   */
  public async createVersion(
    documentId: string,
    content: string,
    createdBy: string,
    changeDescription: string
  ): Promise<DocumentVersion> {
    // Get the latest version number
    const { data: latestVersions, error: fetchError } = await this.supabase
      .from('document_versions')
      .select('versionNumber')
      .eq('documentId', documentId)
      .order('versionNumber', { ascending: false })
      .limit(1);

    if (fetchError) {
      throw new Error(`Failed to fetch latest version: ${fetchError.message}`);
    }

    const versionNumber = latestVersions?.length
      ? latestVersions[0].versionNumber + 1
      : 1;

    const contentHash = this.generateContentHash(content);
    const versionId = `${documentId}_v${versionNumber}`;

    const { data, error } = await this.supabase
      .from('document_versions')
      .insert({
        id: versionId,
        documentId,
        versionNumber,
        content,
        createdAt: new Date().toISOString(),
        createdBy,
        changeDescription,
        contentHash,
      });

    if (error) {
      throw new Error(`Failed to create version: ${error.message}`);
    }

    return {
      id: versionId,
      documentId,
      versionNumber,
      content,
      createdAt: new Date(),
      createdBy,
      changeDescription,
      contentHash,
    };
  }

  /**
   * Retrieves a specific version of a document
   */
  public async getVersion(
    documentId: string,
    versionNumber: number
  ): Promise<DocumentVersion | null> {
    const { data, error } = await this.supabase
      .from('document_versions')
      .select('*')
      .eq('documentId', documentId)
      .eq('versionNumber', versionNumber)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Version not found
      }
      throw new Error(`Failed to fetch version: ${error.message}`);
    }

    return {
      ...data,
      createdAt: new Date(data.createdAt),
    };
  }

  /**
   * Gets all versions of a document
   */
  public async getVersionHistory(
    documentId: string
  ): Promise<DocumentVersion[]> {
    const { data, error } = await this.supabase
      .from('document_versions')
      .select('*')
      .eq('documentId', documentId)
      .order('versionNumber', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch version history: ${error.message}`);
    }

    return (data || []).map((version) => ({
      ...version,
      createdAt: new Date(version.createdAt),
    }));
  }

  /**
   * Compares two versions of a document
   */
  public async compareVersions(
    documentId: string,
    versionA: number,
    versionB: number
  ): Promise<DocumentComparison> {
    const versionADoc = await this.getVersion(documentId, versionA);
    const versionBDoc = await this.getVersion(documentId, versionB);

    if (!versionADoc || !versionBDoc) {
      throw new Error('One or both versions not found');
    }

    const linesA = versionADoc.content.split('\n');
    const linesB = versionBDoc.content.split('\n');

    const added: string[] = [];
    const removed: string[] = [];
    const changed: Array<{ original: string; updated: string }> = [];
    const unchanged: string[] = [];

    // Simple line-by-line comparison
    // For a more sophisticated diff, a library like 'diff' could be used
    const maxLines = Math.max(linesA.length, linesB.length);

    for (let i = 0; i < maxLines; i++) {
      const lineA = linesA[i];
      const lineB = linesB[i];

      if (lineA === undefined) {
        added.push(lineB);
      } else if (lineB === undefined) {
        removed.push(lineA);
      } else if (lineA !== lineB) {
        changed.push({ original: lineA, updated: lineB });
      } else {
        unchanged.push(lineA);
      }
    }

    return {
      added,
      removed,
      changed,
      unchanged,
    };
  }

  /**
   * Verifies the integrity of a document version
   */
  public async verifyVersionIntegrity(
    documentId: string,
    versionNumber: number
  ): Promise<boolean> {
    const version = await this.getVersion(documentId, versionNumber);
    if (!version) {
      return false;
    }

    const computedHash = this.generateContentHash(version.content);
    return computedHash === version.contentHash;
  }

  /**
   * Restores a previous version as the latest version
   */
  public async restoreVersion(
    documentId: string,
    versionNumber: number,
    restoredBy: string
  ): Promise<DocumentVersion> {
    const versionToRestore = await this.getVersion(documentId, versionNumber);
    if (!versionToRestore) {
      throw new Error(`Version ${versionNumber} not found`);
    }

    return this.createVersion(
      documentId,
      versionToRestore.content,
      restoredBy,
      `Restored from version ${versionNumber}`
    );
  }
}
