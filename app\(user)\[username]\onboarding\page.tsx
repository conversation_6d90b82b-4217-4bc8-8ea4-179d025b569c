'use client';

import { PlanSelectionStep } from '@/components/onboarding/PlanSelectionStep';
import { ProfileSetupStep } from '@/components/onboarding/ProfileSetupStep';
import { RoleSetupStep } from '@/components/onboarding/RoleSetupStep';
import { FormsLogo } from '@/components/ux/icons/logo';
import { LogoWithNotamess, NotamessLogo } from '@/components/ux/icons/logotext';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/client';
import { pageProps } from '@/types';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

// Define the types
type PlanType = 'Free' | 'Standard' | 'Enterprise'; // Match the database enum
type UserRole = 'user' | 'lawyer';

interface ProfileData {
  full_name: string;
  username: string;
  bio?: string; // We collect this but don't store it in profiles table (used for lawyer profile)
  avatar_url?: string;
}

interface LawyerProfileData {
  full_name: string;
  email: string;
  specialization: string[];
  bio: string;
  years_experience: number;
  hourly_rate: number;
  consultation_fee: number;
  languages: string[];
}

export default function OnboardingPage({ params }: pageProps) {
  const [step, setStep] = useState(1);
  const totalSteps = 3; // Profile setup, Plan selection, Role-specific setup
  const { user, profile, updateProfile } = userStore();
  const router = useRouter();

  // State to store data from each step
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<PlanType>('Free'); // Use 'Free' instead of 'free'
  const [selectedRole, setSelectedRole] = useState<UserRole>('user');
  const [username, setUsername] = useState<string>(''); // Track username from params and changes
  const [lawyerProfileData, setLawyerProfileData] =
    useState<LawyerProfileData | null>(null);
  const [existingLawyerProfile, setExistingLawyerProfile] =
    useState<LawyerProfileData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingLawyerProfile, setIsLoadingLawyerProfile] = useState(false);

  // Get the username from params
  useEffect(() => {
    const fetchParams = async () => {
      const unwrappedParams = await params;
      setUsername(unwrappedParams.username);
    };

    fetchParams();
  }, [params]);

  // Handle username changes - refresh the page when username changes
  useEffect(() => {
    // Only redirect if we have a profile and the username has changed
    if (
      profile &&
      username &&
      profile.username !== username &&
      profile.username
    ) {
      // Wait a bit to ensure the database update has completed
      const timer = setTimeout(() => {
        console.log('Username changed, redirecting to:', username);
        router.replace(`/${username}/onboarding`);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [username, profile, router]);

  // Check if the user is already onboarded
  useEffect(() => {
    if (profile?.is_onboarded) {
      // Redirect to dashboard if already onboarded
      router.replace(`/${profile.username}`);
    }
  }, [profile, router]);

  // Fetch existing lawyer profile data if available
  useEffect(() => {
    const getLawyerProfile = async () => {
      if (!user) return;

      setIsLoadingLawyerProfile(true);
      try {
        // Check if user is a lawyer - query the lawyers table directly
        const { data: lawyerData, error: lawyerError } = await supabaseClient
          .from('lawyers')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (lawyerError && lawyerError.code !== 'PGRST116') {
          // PGRST116 means not found, which is fine
          throw lawyerError;
        }

        if (lawyerData) {
          // Convert to our LawyerProfileData format
          const profileData: LawyerProfileData = {
            full_name: lawyerData.full_name || '',
            email: lawyerData.email || '',
            specialization: lawyerData.specialization || [],
            bio: lawyerData.bio || '',
            years_experience: lawyerData.years_experience || 0,
            hourly_rate: lawyerData.hourly_rate || 0,
            consultation_fee: lawyerData.consultation_fee || 0,
            languages: lawyerData.languages || ['English'],
          };

          setExistingLawyerProfile(profileData);
          console.log('Existing lawyer profile loaded:', profileData);
        }
      } catch (error) {
        console.error('Error fetching lawyer profile:', error);
        toast.error('Failed to fetch lawyer profile data');
      } finally {
        setIsLoadingLawyerProfile(false);
      }
    };

    getLawyerProfile();
  }, [user]);

  // Handle profile setup completion - Save to database immediately
  const handleProfileSetupComplete = async (data: ProfileData) => {
    console.log('Profile setup complete:', data);
    setIsSubmitting(true);

    try {
      if (!user) {
        throw new Error('User not found');
      }

      // Update the profile using the Supabase client directly
      const { data: updatedProfile, error } = await supabaseClient
        .from('profiles')
        .update({
          full_name: data.full_name,
          username: data.username,
          avatar_url: data.avatar_url || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (!updatedProfile) {
        throw new Error('Failed to update profile');
      }

      // Update local state
      setProfileData(data);

      // Update the profile in the store
      if (profile) {
        updateProfile({
          ...profile,
          full_name: data.full_name,
          username: data.username,
          avatar_url: data.avatar_url || profile.avatar_url,
          updated_at: new Date().toISOString(),
        });
      }

      toast.success('Profile updated successfully');
      setStep(2); // Move to plan selection
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('Failed to save profile');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle plan selection completion - Save to database immediately
  const handlePlanSelectionComplete = async (plan: PlanType) => {
    setIsSubmitting(true);

    try {
      if (!user) {
        throw new Error('User not found');
      }

      // Update the profile directly with the plan
      const { data: updatedProfile, error } = await supabaseClient
        .from('profiles')
        .update({
          plan: plan,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      const success = !error && updatedProfile;

      if (!success) {
        throw new Error('Failed to update plan');
      }

      // Update local state
      setSelectedPlan(plan);

      // Update the profile in the store
      if (profile) {
        updateProfile({
          ...profile,
          plan: plan,
          updated_at: new Date().toISOString(),
        });
      }

      toast.success('Plan updated successfully');
      setStep(3); // Move to role setup
    } catch (error) {
      console.error('Error saving plan:', error);
      toast.error('Failed to save plan');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle role setup completion - Complete the onboarding process
  const handleRoleSetupComplete = async (
    role: UserRole,
    lawyerProfile?: LawyerProfileData
  ) => {
    setSelectedRole(role);
    if (lawyerProfile) {
      setLawyerProfileData(lawyerProfile);
    }

    // Now complete the onboarding process
    await handleComplete();
  };

  // Final completion handler
  const handleComplete = async () => {
    if (!user || !profileData) return;

    setIsSubmitting(true);

    try {
      // Check if username has changed
      const usernameChanged = profile?.username !== profileData.username;
      const oldUsername = profile?.username;

      // First update the user profile
      // Note: 'bio' field is not in the profiles table, so we don't include it
      const { data: updatedProfile, error: profileError } = await supabaseClient
        .from('profiles')
        .update({
          full_name: profileData.full_name,
          username: profileData.username,
          avatar_url: profileData.avatar_url || null,
          user_role: selectedRole, // Column is named user_role in the database
          plan: selectedPlan,
          is_onboarded: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (profileError) {
        throw new Error(`Error updating profile: ${profileError.message}`);
      }

      // If the user selected the lawyer role and provided lawyer profile data
      if (selectedRole === 'lawyer' && lawyerProfileData) {
        // Check if a lawyer profile already exists
        const { data: existingLawyer, error: checkError } = await supabaseClient
          .from('lawyers')
          .select('id')
          .eq('user_id', user.id)
          .single();

        if (checkError && checkError.code !== 'PGRST116') {
          throw new Error(
            `Error checking lawyer profile: ${checkError.message}`
          );
        }

        if (existingLawyer) {
          // Update existing lawyer profile
          const { error: lawyerError } = await supabaseClient
            .from('lawyers')
            .update({
              full_name: lawyerProfileData.full_name,
              email: lawyerProfileData.email,
              specialization: lawyerProfileData.specialization,
              bio: lawyerProfileData.bio,
              years_experience: lawyerProfileData.years_experience,
              hourly_rate: lawyerProfileData.hourly_rate,
              consultation_fee: lawyerProfileData.consultation_fee,
              languages: lawyerProfileData.languages,
              profile_complete: true,
              updated_at: new Date().toISOString(),
            })
            .eq('id', existingLawyer.id);

          if (lawyerError) {
            throw new Error(
              `Error updating lawyer profile: ${lawyerError.message}`
            );
          }
        } else {
          // Create new lawyer profile
          const { error: lawyerError } = await supabaseClient
            .from('lawyers')
            .insert({
              user_id: user.id,
              full_name: lawyerProfileData.full_name,
              email: lawyerProfileData.email,
              specialization: lawyerProfileData.specialization,
              bio: lawyerProfileData.bio,
              years_experience: lawyerProfileData.years_experience,
              hourly_rate: lawyerProfileData.hourly_rate,
              consultation_fee: lawyerProfileData.consultation_fee,
              languages: lawyerProfileData.languages,
              profile_complete: true,
              status: 'active',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            });

          if (lawyerError) {
            throw new Error(
              `Error creating lawyer profile: ${lawyerError.message}`
            );
          }
        }
      }

      // Update the local profile state
      if (profile) {
        updateProfile({
          ...profile,
          full_name: profileData.full_name,
          username: profileData.username,
          avatar_url: profileData.avatar_url || profile.avatar_url,
          role: selectedRole, // This matches the Profile interface property
          plan: selectedPlan,
          is_onboarded: true,
          updated_at: new Date().toISOString(),
        });
      }

      toast.success('Onboarding completed successfully!');

      // If username changed, redirect to the new URL
      if (usernameChanged && oldUsername) {
        toast.info(
          'Your username has changed. Redirecting to your new profile page...'
        );

        // Redirect to the dashboard with the new username
        router.replace(`/${profileData.username}`);
      } else {
        // Redirect to the dashboard
        router.replace(`/${profileData.username}`);
      }
    } catch (error) {
      console.error('Error during onboarding completion:', error);
      toast.error('Failed to complete onboarding. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrevious = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  // Handle role changes for auto-saving
  const handleRoleChange = async (role: UserRole) => {
    if (!user) {
      toast.error('User not found');
      return;
    }

    try {
      // Update the role in the database (column is named user_role, not role)
      const { error } = await supabaseClient
        .from('profiles')
        .update({
          user_role: role,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        throw new Error(`Error updating role: ${error.message}`);
      }

      // Update the profile in the store (property is named role in the Profile interface)
      if (profile) {
        updateProfile({
          ...profile,
          role: role, // This matches the Profile interface property
          updated_at: new Date().toISOString(),
        });
      }

      // Update local state
      setSelectedRole(role);

      toast.success(`Role updated to ${role}`);
    } catch (error) {
      console.error('Error saving role:', error);
      toast.error('Failed to update role');
    }
  };

  // Handle plan changes for auto-saving
  const handlePlanChange = async (plan: PlanType) => {
    if (!user) {
      toast.error('User not found');
      return;
    }

    try {
      // Update the plan in the database
      const { error } = await supabaseClient
        .from('profiles')
        .update({
          plan: plan,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        throw new Error(`Error updating plan: ${error.message}`);
      }

      // Update the profile in the store
      if (profile) {
        updateProfile({
          ...profile,
          plan: plan,
          updated_at: new Date().toISOString(),
        });
      }

      // Update local state
      setSelectedPlan(plan);

      toast.success(`Plan updated to ${plan}`);
    } catch (error) {
      console.error('Error saving plan:', error);
      toast.error('Failed to update plan');
    }
  };

  // Handle individual field updates for auto-saving
  const handleProfileFieldUpdate = async (field: string, value: any) => {
    if (!user) {
      toast.error('User not found');
      return;
    }

    try {
      // For username changes, we need to check if it's already taken
      if (field === 'username') {
        // Check if username is already taken
        const { data } = await supabaseClient
          .from('profiles')
          .select('username')
          .eq('username', value)
          .neq('id', user.id) // Exclude current user
          .maybeSingle();

        if (data) {
          toast.error('Username is already taken');
          return;
        }

        // If username is changing, we'll need to redirect
        if (profile?.username !== value) {
          // Store the new username for later redirect
          setUsername(value);
        }
      }

      // Update the field in the database
      const { error } = await supabaseClient
        .from('profiles')
        .update({
          [field]: value,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        throw new Error(`Error updating ${field}: ${error.message}`);
      }

      // Update the profile in the store
      if (profile) {
        updateProfile({
          ...profile,
          [field]: value,
          updated_at: new Date().toISOString(),
        });
      }

      // Show success message for important fields
      if (field === 'username') {
        toast.success('Username updated successfully');
      } else if (field === 'full_name') {
        toast.success('Name updated successfully');
      }
    } catch (error) {
      console.error(`Error updating ${field}:`, error);
      toast.error(`Failed to update ${field}`);
    }
  };

  // Create the header component
  const Header = (
    <div className="flex items-center justify-between w-full px-6 h-16 border-b border-dashed border-neutral-200">
      <div className="flex items-center gap-2">
        <FormsLogo className="h-8 w-8" />
        <p className="text-base">
          <LogoWithNotamess /> <span className="text-neutral-500">by </span>
          <Link href={'https://notamess.com'} className="relative group">
            <NotamessLogo />
            <span className="absolute bottom-0 left-0 h-px w-0 transition-all duration-200 ease-in-out group-hover:w-full rounded-full bg-notamess-100" />
          </Link>
        </p>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-neutral-500">
          Step {step} of {totalSteps}
        </span>
      </div>
    </div>
  );

  // Return the onboarding content
  return (
    <div className="flex flex-col h-full">
      {Header}
      <div className="p-6 flex flex-col h-full">
        <div className="flex-1">
          {/* Step 1: Profile Setup */}
          {step === 1 && (
            <ProfileSetupStep
              key="profile-setup"
              onComplete={handleProfileSetupComplete}
              onFieldUpdate={handleProfileFieldUpdate}
            />
          )}

          {/* Step 2: Plan Selection */}
          {step === 2 && (
            <PlanSelectionStep
              key="plan-selection"
              onComplete={handlePlanSelectionComplete}
              onPlanChange={handlePlanChange}
            />
          )}

          {/* Step 3: Role-specific Setup */}
          {step === 3 && (
            <RoleSetupStep
              key="role-setup"
              selectedPlan={selectedPlan}
              onComplete={handleRoleSetupComplete}
              profileBio={profileData?.bio} // Pass the bio from profile setup
              onRoleChange={handleRoleChange}
              existingLawyerProfile={existingLawyerProfile}
              isLoadingLawyerProfile={isLoadingLawyerProfile}
            />
          )}
        </div>

        {/* Navigation buttons */}
        <div className="flex items-center justify-between mt-8 pt-4 border-t border-dashed border-neutral-200">
          <div>
            {step > 1 && (
              <button
                className="px-4 py-2 rounded-md border border-neutral-200 hover:bg-neutral-50 transition-colors"
                onClick={handlePrevious}
                disabled={isSubmitting}
              >
                Back
              </button>
            )}
          </div>
          <div>
            {/* No Next button here since each step has its own submit button */}
          </div>
        </div>
      </div>
    </div>
  );
}
