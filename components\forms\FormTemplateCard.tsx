import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ArrowRight,
  BookOpen,
  Briefcase,
  Check,
  Edit,
  Eye,
  FilePlus,
  FileSignature,
  FileText,
  GraduationCap,
  Lightbulb,
  Share2,
  Shield,
  Sparkles,
} from 'lucide-react';
import { useState } from 'react';

interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  popular?: boolean;
  blockchain?: boolean;
  ai?: boolean;
  jurisdiction?: string;
  language?: string;
  lastUpdated?: Date;
  usageCount?: number;
}

interface FormTemplateCardProps {
  template: FormTemplate;
  isSelected?: boolean;
  onClick?: (templateId: string) => void;
  onSelect?: (templateId: string) => void;
  variant?: 'default' | 'compact' | 'detailed';
}

export function FormTemplateCard({
  template,
  isSelected = false,
  onClick,
  onSelect,
  variant = 'default',
}: FormTemplateCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getIconForCategory = (category: string) => {
    switch (category) {
      case 'legal':
        return <FileSignature className="h-4 w-4" />;
      case 'business':
        return <Briefcase className="h-4 w-4" />;
      case 'education':
        return <GraduationCap className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getFormattedDate = (date?: Date) => {
    if (!date) return 'N/A';

    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;

    return date.toLocaleDateString();
  };

  if (variant === 'compact') {
    return (
      <Card
        className={`cursor-pointer hover:shadow-md transition-all flex flex-row h-20 overflow-hidden ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
        onClick={() => onClick?.(template.id)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="bg-primary/10 w-16 flex items-center justify-center">
          {template.icon}
        </div>
        <div className="flex flex-col justify-center px-4 py-2 flex-1">
          <h3 className="font-medium text-sm line-clamp-1">{template.name}</h3>
          <p className="text-xs text-muted-foreground line-clamp-1">
            {template.description}
          </p>
          <div className="flex gap-1 mt-1">
            {template.popular && (
              <Badge variant="secondary" className="text-[10px] px-1 py-0 h-4">
                <Sparkles className="h-2.5 w-2.5 mr-0.5" /> Popular
              </Badge>
            )}
            {template.blockchain && (
              <Badge
                variant="outline"
                className="text-[10px] px-1 py-0 h-4 bg-emerald-50 text-emerald-800 border-emerald-200"
              >
                <Shield className="h-2.5 w-2.5 mr-0.5" /> Blockchain
              </Badge>
            )}
          </div>
        </div>
        <div className="flex items-center pr-3 bg-muted/20">
          <Button
            variant="ghost"
            className="h-8 w-8 p-0 rounded-full"
            onClick={(e) => {
              e.stopPropagation();
              onSelect?.(template.id);
            }}
          >
            {isSelected ? (
              <Check className="h-4 w-4" />
            ) : isHovered ? (
              <ArrowRight className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card
        className={`overflow-hidden hover:shadow-md transition-all ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
      >
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 p-2 rounded-full">
                {template.icon}
              </div>
              <div>
                <CardTitle className="text-lg">{template.name}</CardTitle>
                <CardDescription className="flex items-center mt-1">
                  <Badge variant="outline" className="mr-2">
                    {getIconForCategory(template.category)} {template.category}
                  </Badge>
                  {template.language && (
                    <span className="text-xs text-muted-foreground">
                      {template.language}
                    </span>
                  )}
                </CardDescription>
              </div>
            </div>
            <div>
              {template.usageCount !== undefined && (
                <Badge variant="outline" className="bg-muted">
                  <BookOpen className="h-3 w-3 mr-1" /> {template.usageCount}{' '}
                  uses
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            {template.description}
          </p>
          <div className="flex flex-wrap gap-2">
            {template.popular && (
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="h-3 w-3 mr-1" /> Popular
              </Badge>
            )}
            {template.blockchain && (
              <Badge
                variant="outline"
                className="text-xs bg-emerald-50 text-emerald-800 border-emerald-200"
              >
                <Shield className="h-3 w-3 mr-1" /> Blockchain Verified
              </Badge>
            )}
            {template.ai && (
              <Badge
                variant="outline"
                className="text-xs bg-amber-50 text-amber-800 border-amber-200"
              >
                <Lightbulb className="h-3 w-3 mr-1" /> AI Enhanced
              </Badge>
            )}
            {template.jurisdiction && (
              <Badge variant="outline" className="text-xs">
                {template.jurisdiction}
              </Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="bg-muted/20 border-t justify-between">
          <div className="text-xs text-muted-foreground">
            Updated {getFormattedDate(template.lastUpdated)}
          </div>
          <div className="space-x-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => onSelect?.(template.id)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => onSelect?.(template.id)}
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => onSelect?.(template.id)}
            >
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // Default variant
  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary' : ''
      }`}
      onClick={() => onClick?.(template.id)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center space-x-2">
          <div className="bg-primary/10 p-2 rounded-full">{template.icon}</div>
          <div>
            <CardTitle>{template.name}</CardTitle>
            <div className="flex gap-2 mt-1">
              {template.popular && (
                <Badge variant="secondary" className="text-xs">
                  <Sparkles className="h-3 w-3 mr-1" /> Popular
                </Badge>
              )}
              {template.blockchain && (
                <Badge
                  variant="outline"
                  className="text-xs bg-emerald-50 text-emerald-800 border-emerald-200"
                >
                  <Shield className="h-3 w-3 mr-1" /> Blockchain
                </Badge>
              )}
              {template.ai && (
                <Badge
                  variant="outline"
                  className="text-xs bg-amber-50 text-amber-800 border-amber-200"
                >
                  <Lightbulb className="h-3 w-3 mr-1" /> AI
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-2">
        <p className="text-muted-foreground text-sm">{template.description}</p>
      </CardContent>
      <CardFooter className="pt-2">
        <Button
          variant={isSelected ? 'default' : 'outline'}
          className="w-full"
          onClick={(e) => {
            e.stopPropagation();
            onSelect?.(template.id);
          }}
        >
          {isSelected ? (
            <>
              <FilePlus className="mr-2 h-4 w-4" />
              Create Form
            </>
          ) : (
            'Select'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
