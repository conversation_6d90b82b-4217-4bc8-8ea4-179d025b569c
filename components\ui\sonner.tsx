'use client';

import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { Toaster as Sonner } from 'sonner';

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      // theme={theme as ToasterProps['theme']}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: cn(
            FONT_BIRCOLAGE_GROTESQUE.className,
            'toast group group-[.toaster]:rounded-3xl rounded-3xl group-[.toaster]:border-gray-200 group-[.toaster]:bg-white group-[.toaster]:text-gray-950 group-[.toaster]:shadow-xs dark:group-[.toaster]:border-gray-800 dark:group-[.toaster]:bg-gray-950 dark:group-[.toaster]:text-gray-50'
          ),
          description:
            'group-[.toast]:text-gray-500 dark:group-[.toast]:text-gray-400',
          actionButton:
            'group-[.toast]:bg-gray-900 group-[.toast]:text-gray-50 dark:group-[.toast]:bg-gray-50 dark:group-[.toast]:text-gray-900',
          cancelButton:
            'group-[.toast]:bg-gray-100 group-[.toast]:text-gray-500 dark:group-[.toast]:bg-gray-800 dark:group-[.toast]:text-gray-400',
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
