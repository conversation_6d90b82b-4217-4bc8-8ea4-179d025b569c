import { createClient } from '@supabase/supabase-js';
import { Database } from './database-types';

// Create a Supabase client for server-side operations
export async function supabaseServer() {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

// Export a createServerClient function for compatibility
export const createServerClient = supabaseServer;
