'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useOrganizations } from '@/lib/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Loader2, Shield, User, UserPlus } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Define the organization role type
type OrganizationRole = 'member' | 'admin';

const addMemberSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  role: z.enum(['admin', 'member']),
});

type AddMemberFormValues = z.infer<typeof addMemberSchema>;

export default function AddMemberPage() {
  const router = useRouter();
  const { username, id } = useParams();
  const { addMember, getOrganization } = useOrganizations();
  const [isAdding, setIsAdding] = useState(false);
  const [organizationName, setOrganizationName] = useState('');

  // NOTE: This is a placeholder function that would need to be implemented
  // In a real application, you would create an API endpoint to find users by email
  // or add this functionality to the useUsers hook
  const findUserByEmail = async (email: string) => {
    console.log('Finding user with email:', email);
    // Mock implementation - in a real app, this would call an API endpoint
    return { id: 'user-id-' + Math.random().toString(36).substring(2, 9) };
  };

  const form = useForm<AddMemberFormValues>({
    resolver: zodResolver(addMemberSchema),
    defaultValues: {
      email: '',
      role: 'member',
    },
  });

  // Fetch organization name
  useEffect(() => {
    async function fetchOrganizationName() {
      if (!id) return;

      const orgId = Array.isArray(id) ? id[0] : id;

      try {
        const organization = await getOrganization(orgId);
        if (organization) {
          setOrganizationName(organization.name);
        }
      } catch (error) {
        console.error('Error fetching organization name:', error);
        toast.error('Failed to load organization details');
      }
    }

    fetchOrganizationName();
  }, [id, getOrganization]);

  async function onSubmit(values: AddMemberFormValues) {
    setIsAdding(true);

    try {
      if (!id) {
        toast.error('Organization ID is missing');
        setIsAdding(false);
        return;
      }

      const orgId = Array.isArray(id) ? id[0] : id;

      // Get the organization to check if it exists
      const organization = await getOrganization(orgId);
      if (!organization) {
        toast.error('Organization not found');
        setIsAdding(false);
        return;
      }

      // Check if the user exists using our findUserByEmail function
      const user = await findUserByEmail(values.email);

      if (!user) {
        toast.error('User not found', {
          description: 'No user with this email address exists in the system.',
        });
        setIsAdding(false);
        return;
      }

      // Check if the user is already a member
      const isMember = organization.members.some(
        (member) => member.user_id === user.id
      );

      if (isMember) {
        toast.error('User is already a member', {
          description: 'This user is already a member of the organization.',
        });
        setIsAdding(false);
        return;
      }

      // Add the user to the organization using the hook
      const success = await addMember(
        orgId,
        user.id,
        values.role as OrganizationRole
      );

      if (success) {
        // Reset the form
        form.reset();

        toast.success('Member added successfully', {
          description: `${values.email} has been added to the organization as a ${values.role}.`,
        });

        // Navigate back to the members page
        if (username && orgId) {
          router.push(`/${username}/organizations/${orgId}/members`);
        }
      }
    } catch (error) {
      console.error('Error adding member:', error);
      toast.error('Failed to add member');
    } finally {
      setIsAdding(false);
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        {username && id && (
          <Link
            href={`/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`}
          >
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
        )}
        <h1 className="text-2xl font-bold">Add Member to {organizationName}</h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Add New Member
          </CardTitle>
          <CardDescription>
            Add a new member to your organization.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter email address"
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      The user must already have an account in the system
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="space-y-3"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="admin" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center gap-2">
                            <Shield className="h-4 w-4 text-blue-600" />
                            <div>
                              <div>Admin</div>
                              <p className="text-xs text-muted-foreground">
                                Can manage members, teams, and settings
                              </p>
                            </div>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="member" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-600" />
                            <div>
                              <div>Member</div>
                              <p className="text-xs text-muted-foreground">
                                Can view and use organization resources
                              </p>
                            </div>
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormDescription>
                      Choose the appropriate role for this member
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (username && id) {
                      router.push(
                        `/${username}/organizations/${Array.isArray(id) ? id[0] : id}/members`
                      );
                    }
                  }}
                  disabled={isAdding}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isAdding}>
                  {isAdding ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    'Add Member'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
