'use client';
import { But<PERSON> } from '@/components/ui/button';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { UserAvatar } from '@/components/ui/user-avatar';
import { LockFill } from '@/components/ux/icons';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import { userStore } from '@/lib/store/user';
import { authService } from '@/lib/supabase/auth/auth-service';
import { cn } from '@/lib/utils';
import {
  Bell,
  Building2,
  Calendar,
  ChevronLeft,
  Cog,
  DoorOpenIcon,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface SettingsContentProps {
  direction?: number;
}

export default function SettingsContent({
  direction = 0,
}: SettingsContentProps) {
  const { profile, user, removeProfile, removeUser } = userStore();
  const [settingsHovered, setSettingsHovered] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  async function handleLogOut() {
    try {
      setIsLoggingOut(true);

      // Import the safeDbOperation utility
      const { safeDbOperation } = await import('@/lib/utils/db-operation');

      // Use safeDbOperation to handle the logout process
      await safeDbOperation(
        async () => {
          await authService.signOut();
          removeProfile(null);
          removeUser(null);
          return 'Done';
        },
        {
          loadingMessage: 'Logging Out...',
          successMessage: 'Logged Out Successfully',
          errorMessage: 'Failed to log out',
          retryCount: 1,
        }
      );

      // Navigate after successful logout
      router.refresh();
      return 'Done';
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    } finally {
      setIsLoggingOut(false);
    }
  }

  // Workspace settings group
  const workspaceSettings = [
    {
      name: 'General',
      url: `/${profile?.username}/settings`,
      icon: Cog,
      active: pathname === `/${profile?.username}/settings`,
    },
    {
      name: 'Notifications',
      url: `/${profile?.username}/settings/notifications`,
      icon: Bell,
      active: pathname.includes(`/${profile?.username}/settings/notifications`),
    },
    {
      name: 'Security',
      url: `/${profile?.username}/settings/security`,
      icon: LockFill,
      active: pathname.includes(`/${profile?.username}/settings/security`),
    },
    {
      name: 'Calendar',
      url: `/${profile?.username}/settings/calendar`,
      icon: Calendar,
      active: pathname.includes(`/${profile?.username}/settings/calendar`),
    },
    {
      name: 'Organizations',
      url: `/${profile?.username}/organizations`,
      icon: Building2,
      active: pathname.includes(`/${profile?.username}/organizations`),
    },
  ];

  return (
    <motion.div
      key="settings"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: 20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
      className="w-full overflow-hidden flex flex-col h-full"
    >
      <div className="py-2 px-4 flex items-center gap-2 border-b h-10 border-dashed border-neutral-300/60">
        <Link
          href={`/${profile?.username}`}
          className={cn(
            'flex items-center gap-1 transition-all duration-200 ease-out',
            settingsHovered ? 'text-neutral-600' : 'text-neutral-400'
          )}
          onMouseEnter={() => setSettingsHovered(true)}
          onMouseLeave={() => setSettingsHovered(false)}
        >
          <motion.span
            initial={{ x: 0 }}
            animate={{ x: settingsHovered ? -3 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronLeft className="size-4" />
          </motion.span>
          <span className={cn('text-sm font-medium uppercase')}>Settings</span>
        </Link>
      </div>

      {/* User Profile Section */}
      <div className="p-4 border-b border-dashed border-neutral-300/60">
        <div className="flex flex-col items-start space-y-3">
          <UserAvatar
            size="xl"
            avatarUrl={profile?.avatar_url}
            fallbackText={profile?.full_name || ''}
            className="border-2 border-accent-100 h-24 w-24"
          />
          <div className="text-left">
            <h3 className="font-medium text-sm">{profile?.full_name}</h3>
            <p
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-neutral-500 text-xs truncate max-w-[180px]'
              )}
            >
              {profile?.email || user?.email}
            </p>
          </div>
        </div>
      </div>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Workspace
        </SidebarGroupLabel>
        <SidebarMenu>
          {workspaceSettings.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      {/* Logout Button */}
      <div className="mt-auto p-2 pr-3 pl-4 border-t border-dashed border-neutral-300/60">
        <Button
          variant="shadow_red"
          size="sm"
          className={cn(
            'w-full flex items-center justify-center gap-2 rounded-lg uppercase text-xs'
          )}
          onClick={handleLogOut}
          disabled={isLoggingOut}
        >
          <DoorOpenIcon className="h-4 w-4" />
          <span className={cn(FONT_JETBRAINS_MONO.className, 'font-bold')}>
            Log Out
          </span>
        </Button>
      </div>
    </motion.div>
  );
}
