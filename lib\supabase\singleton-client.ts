'use client';

import { createBrowserClient } from '@supabase/ssr';
import { Database } from './database-types';

// Create a singleton Supabase client for browser usage
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create the client instance only once
let supabaseInstance: ReturnType<typeof createBrowserClient<Database>> | null =
  null;

export function getSupabaseClient() {
  if (!supabaseInstance) {
    supabaseInstance = createBrowserClient<Database>(
      supabaseUrl,
      supabaseAnonKey
    );
  }
  return supabaseInstance;
}

// Export the singleton client
export const supabaseClient = getSupabaseClient();
