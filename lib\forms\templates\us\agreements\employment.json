{"metadata": {"id": "employment-us-standard-v1", "version": "1.0.0", "name": "Standard Employment Agreement", "description": "A comprehensive employment agreement template covering essential terms of employment relationship.", "category": "agreement", "jurisdiction": "us", "language": "en", "tags": ["employment", "labor", "compensation", "standard"], "lastUpdated": "2024-03-21T00:00:00.000Z", "isPublished": true}, "sections": [{"id": "parties", "title": "Parties", "content": "This Employment Agreement (this \"Agreement\") is made and entered into as of {{effectiveDate}} by and between:\n\n{{employerName}}, a {{companyType}} with its principal place of business at {{employerAddress}} (the \"Employer\")\n\nand\n\n{{employeeName}}, residing at {{employeeAddress}} (the \"Employee\")", "isRequired": true, "order": 1, "variables": [{"name": "effectiveDate", "type": "date", "description": "The date when employment begins", "isRequired": true}, {"name": "employerName", "type": "text", "description": "Legal name of the employer", "isRequired": true}, {"name": "companyType", "type": "text", "description": "Legal structure of the company (e.g., Corporation, LLC)", "isRequired": true}, {"name": "employerAddress", "type": "text", "description": "Legal address of the employer", "isRequired": true}, {"name": "employeeName", "type": "text", "description": "Full legal name of the employee", "isRequired": true}, {"name": "employee<PERSON>ddress", "type": "text", "description": "Current address of the employee", "isRequired": true}]}, {"id": "position", "title": "Position and Duties", "content": "The Employee shall be employed as {{jobTitle}} and shall report to {{reportsTo}}. The Employee's duties shall include:\n\n{{jobDuties}}\n\nThe Employee agrees to devote their full business time and attention to the performance of these duties.", "isRequired": true, "order": 2, "variables": [{"name": "jobTitle", "type": "text", "description": "Employee's job title", "isRequired": true}, {"name": "reportsTo", "type": "text", "description": "Title of the person or role the employee reports to", "isRequired": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "description": "Detailed description of job responsibilities", "isRequired": true}]}, {"id": "compensation", "title": "Compensation and Benefits", "content": "Base Salary: The Employee shall receive an annual base salary of {{baseSalary}} USD, payable in accordance with the Employer's standard payroll practices.\n\nBenefits: The Employee shall be eligible to participate in the following benefits:\n{{benefits}}\n\nBonus: {{bonusStructure}}", "isRequired": true, "order": 3, "variables": [{"name": "baseSalary", "type": "number", "description": "Annual base salary in USD", "isRequired": true, "validationRules": [{"type": "min", "value": 0, "message": "Salary must be a positive number"}]}, {"name": "benefits", "type": "text", "description": "List of benefits provided", "isRequired": true}, {"name": "bonusStructure", "type": "text", "description": "Description of bonus or variable compensation", "isRequired": false}]}, {"id": "term", "title": "Term and Termination", "content": "Term: This Agreement shall commence on the Effective Date and continue {{employmentType}}.\n\nTermination: Either party may terminate this Agreement {{terminationTerms}}.\n\nSeverance: {{severanceTerms}}", "isRequired": true, "order": 4, "variables": [{"name": "employmentType", "type": "text", "description": "Type of employment (e.g., 'indefinitely', 'for X years')", "isRequired": true}, {"name": "terminationTerms", "type": "text", "description": "Terms for termination of employment", "isRequired": true}, {"name": "severanceTerms", "type": "text", "description": "Terms for severance pay if applicable", "isRequired": false}]}], "legalRequirements": ["Must comply with federal and state labor laws", "Must specify compensation and benefits clearly", "Must define working hours and location", "Must include termination provisions", "Must address confidentiality and intellectual property rights"], "formattingGuidelines": ["Use clear section headings", "Number all paragraphs", "Define key terms in quotes on first use", "Use consistent formatting for monetary values"], "validationRules": [{"field": "effectiveDate", "rule": "must be current or future date", "message": "Start date cannot be in the past"}, {"field": "baseSalary", "rule": "must be greater than minimum wage", "message": "Salary must meet minimum wage requirements"}], "versions": [{"id": "v1-initial", "version": "1.0.0", "changes": [{"type": "added", "description": "Initial version of US Standard Employment Agreement template", "date": "2024-03-21T00:00:00.000Z"}], "createdAt": "2024-03-21T00:00:00.000Z", "createdBy": "system"}]}