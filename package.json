{"name": "notamess_forms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "download-fonts": "node scripts/download-fonts.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@react-pdf/renderer": "^4.3.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/dompurify": "^3.2.0", "ai": "^4.3.16", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "docx": "^9.5.0", "dompurify": "^3.2.5", "groq-sdk": "^0.22.0", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "isomorphic-dompurify": "^2.24.0", "jspdf": "^3.0.1", "lenis": "^1.3.3", "lucide-react": "^0.511.0", "motion": "^12.12.1", "next": "15.3.2", "openai": "^4.100.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "react-image-crop": "^11.0.10", "react-use-measure": "^2.1.7", "react-use-wizard": "^2.3.0", "react-window": "^1.8.11", "resend": "^4.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5.8.3", "vitest": "^3.1.3"}}