'use client';

import { useState } from 'react';
// import { CalendarEvent } from '@/lib/types/calendar-types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserAvatar } from '@/components/ui/user-avatar';
import { CalendarEvent } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { formatDate } from '@/lib/utils/calendar-utils';
import { addDays, endOfDay, isToday, startOfDay } from 'date-fns';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  FileText,
  MapPin,
  Video,
} from 'lucide-react';

interface AgendaViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  isReadOnly?: boolean;
  daysToShow?: number;
}

export function AgendaView({
  currentDate,
  events,
  onEventClick,
  isReadOnly = false,
  daysToShow = 7,
}: AgendaViewProps) {
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date>(startOfDay(currentDate));

  // Handle event click
  const handleEventClick = (event: CalendarEvent) => {
    if (isReadOnly) {
      setSelectedEvent(event);
      setIsEventDialogOpen(true);
    } else {
      onEventClick(event);
    }
  };

  // Navigate to previous period
  const handlePrevious = () => {
    setStartDate(addDays(startDate, -daysToShow));
  };

  // Navigate to next period
  const handleNext = () => {
    setStartDate(addDays(startDate, daysToShow));
  };

  // Generate days to display
  const days = Array.from({ length: daysToShow }, (_, i) =>
    addDays(startDate, i)
  );

  // Group events by day
  const eventsByDay = days.map((day) => {
    const dayStart = startOfDay(day);
    const dayEnd = endOfDay(day);

    return {
      date: day,
      events: events
        .filter((event) => {
          const eventStart = new Date(event.start);
          return eventStart >= dayStart && eventStart <= dayEnd;
        })
        .sort(
          (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
        ),
    };
  });

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <Button variant="outline" size="sm" onClick={handlePrevious}>
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>
        <div className="text-sm font-medium">
          {formatDate(startDate, 'MMMM d')} -{' '}
          {formatDate(addDays(startDate, daysToShow - 1), 'MMMM d, yyyy')}
        </div>
        <Button variant="outline" size="sm" onClick={handleNext}>
          Next
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>

      <div className="space-y-6">
        {eventsByDay.map(({ date, events }) => (
          <div key={date.toISOString()}>
            <h3
              className={cn(
                'text-lg font-medium mb-2 sticky top-0 bg-background py-2',
                isToday(date) && 'text-primary'
              )}
            >
              {formatDate(date, 'EEEE, MMMM d, yyyy')}
              {isToday(date) && (
                <Badge className="ml-2 bg-primary text-primary-foreground">
                  Today
                </Badge>
              )}
            </h3>

            {events.length === 0 ? (
              <Card>
                <CardContent className="p-4 text-center text-muted-foreground">
                  No events scheduled for this day
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {events.map((event) => (
                  <Card
                    key={event.id}
                    className={cn(
                      'overflow-hidden cursor-pointer hover:shadow-md transition-shadow',
                      event.status === 'cancelled' && 'opacity-60'
                    )}
                    onClick={() => handleEventClick(event)}
                  >
                    <CardContent className="p-4">
                      <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <div
                              className={cn(
                                'w-3 h-3 rounded-full',
                                event.status === 'scheduled'
                                  ? 'bg-blue-500'
                                  : event.status === 'confirmed'
                                    ? 'bg-green-500'
                                    : event.status === 'completed'
                                      ? 'bg-purple-500'
                                      : 'bg-red-500'
                              )}
                            ></div>
                            <h4 className="font-medium">{event.title}</h4>
                          </div>

                          <div className="flex flex-wrap gap-x-4 gap-y-1 text-sm">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                              <span>
                                {formatDate(new Date(event.start), 'h:mm a')} -
                                {formatDate(new Date(event.end), 'h:mm a')}
                              </span>
                            </div>

                            {event.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{event.location}</span>
                              </div>
                            )}

                            <div className="flex items-center gap-1">
                              {event.consultationType === 'video' ? (
                                <Video className="h-3.5 w-3.5 text-muted-foreground" />
                              ) : (
                                <FileText className="h-3.5 w-3.5 text-muted-foreground" />
                              )}
                              <span>
                                {event.consultationType === 'video'
                                  ? 'Video Consultation'
                                  : 'Document Review'}
                              </span>
                            </div>
                          </div>

                          {event.description && (
                            <p className="text-sm text-muted-foreground mt-2 line-clamp-2">
                              {event.description}
                            </p>
                          )}
                        </div>

                        <div className="flex items-center gap-4">
                          {event.client && (
                            <div className="flex items-center gap-2">
                              <UserAvatar
                                userId={event.client.id}
                                avatarUrl={event.client.avatar_url}
                                fallbackText={event.client.full_name}
                                className="h-8 w-8"
                              />
                              <div className="hidden md:block">
                                <p className="text-sm font-medium">
                                  {event.client.full_name}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Client
                                </p>
                              </div>
                            </div>
                          )}

                          {event.lawyer && (
                            <div className="flex items-center gap-2">
                              <UserAvatar
                                userId={event.lawyer.id}
                                avatarUrl={event.lawyer.avatar_url}
                                fallbackText={event.lawyer.full_name}
                                className="h-8 w-8"
                              />
                              <div className="hidden md:block">
                                <p className="text-sm font-medium">
                                  {event.lawyer.full_name}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Lawyer
                                </p>
                              </div>
                            </div>
                          )}

                          {event.status && (
                            <Badge
                              variant="outline"
                              className={
                                event.status === 'scheduled'
                                  ? 'bg-blue-50 text-blue-700 border-blue-200'
                                  : event.status === 'confirmed'
                                    ? 'bg-green-50 text-green-700 border-green-200'
                                    : event.status === 'completed'
                                      ? 'bg-purple-50 text-purple-700 border-purple-200'
                                      : 'bg-red-50 text-red-700 border-red-200'
                              }
                            >
                              {event.status.charAt(0).toUpperCase() +
                                event.status.slice(1)}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Event details dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedEvent && (
              <>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(
                      new Date(selectedEvent.start),
                      'EEEE, MMMM d, yyyy'
                    )}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(new Date(selectedEvent.start), 'h:mm a')} -
                    {formatDate(new Date(selectedEvent.end), 'h:mm a')}
                  </span>
                </div>

                {selectedEvent.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.location}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  {selectedEvent.consultationType === 'video' ? (
                    <Video className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span>
                    {selectedEvent.consultationType === 'video'
                      ? 'Video Consultation'
                      : 'Document Review'}
                  </span>
                </div>

                {selectedEvent.status && (
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={
                        selectedEvent.status === 'scheduled'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : selectedEvent.status === 'confirmed'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : selectedEvent.status === 'completed'
                              ? 'bg-purple-50 text-purple-700 border-purple-200'
                              : 'bg-red-50 text-red-700 border-red-200'
                      }
                    >
                      {selectedEvent.status.charAt(0).toUpperCase() +
                        selectedEvent.status.slice(1)}
                    </Badge>
                  </div>
                )}

                {(selectedEvent.client || selectedEvent.lawyer) && (
                  <div className="flex items-center gap-2 mt-4">
                    {selectedEvent.client && (
                      <div className="flex items-center gap-2">
                        <UserAvatar
                          userId={selectedEvent.client.id}
                          avatarUrl={selectedEvent.client.avatar_url}
                          fallbackText={selectedEvent.client.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.client.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Client
                          </p>
                        </div>
                      </div>
                    )}

                    {selectedEvent.lawyer && (
                      <div className="flex items-center gap-2 ml-auto">
                        <UserAvatar
                          userId={selectedEvent.lawyer.id}
                          avatarUrl={selectedEvent.lawyer.avatar_url}
                          fallbackText={selectedEvent.lawyer.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.lawyer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Lawyer
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {selectedEvent.description && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {selectedEvent.description}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => setIsEventDialogOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
