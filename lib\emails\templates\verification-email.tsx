import {
  Body,
  But<PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface VerificationEmailProps {
  verificationLink: string;
  userName: string;
}

export const VerificationEmail = ({
  verificationLink,
  userName,
}: VerificationEmailProps) => {
  return (
    <Html>
      <Head />
      <Preview>Verify your email address for NotAMess Forms</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={h1}>Verify your email address</Heading>
          <Text style={text}>Hi {userName},</Text>
          <Text style={text}>
            Thanks for signing up for NotAMess Forms! Please verify your email
            address by clicking the button below.
          </Text>
          <Section style={buttonContainer}>
            <Button
              style={{ ...button, padding: '12px 20px' }}
              href={verificationLink}
            >
              Verify Email Address
            </Button>
          </Section>
          <Text style={text}>
            If you didn't sign up for NotAMess Forms, you can safely ignore this
            email.
          </Text>
          <Text style={text}>
            This verification link will expire in 24 hours.
          </Text>
          <Hr style={hr} />
          <Text style={footer}>
            If the button above doesn't work, paste this link into your browser:
          </Text>
          <Text style={link}>
            <Link href={verificationLink} style={link}>
              {verificationLink}
            </Link>
          </Text>
          <Text style={footer}>
            © {new Date().getFullYear()} NotAMess Forms. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px',
  borderRadius: '4px',
  maxWidth: '600px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '30px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
};

const buttonContainer = {
  margin: '24px 0',
  textAlign: 'center' as const,
};

const button = {
  backgroundColor: '#5469d4',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '20px 0',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '16px 0',
};

const link = {
  color: '#5469d4',
  fontSize: '12px',
  textDecoration: 'underline',
  wordBreak: 'break-all' as const,
};
