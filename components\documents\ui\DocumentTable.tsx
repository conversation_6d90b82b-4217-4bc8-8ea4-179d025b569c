import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TrashFill } from '@/components/ux/icons/nucleo/trash-fill';
import { DocumentSummary } from '@/lib/types/database-modules';
import { formatDistanceToNow } from 'date-fns';
import {
  FileTextIcon,
  PencilIcon,
  Share2 as ShareIcon,
  TrashIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { JSX, useEffect, useState } from 'react';

interface DocumentTableProps {
  documents: DocumentSummary[];
  username: string;
  onDelete: (id: string) => void;
  onShare?: (id: string) => void;
  getDocumentTypeIcon: (type: string) => JSX.Element;
  getStatusColor: (status: string) => string;
  onBatchDelete?: (ids: string[]) => void;
}

type ColumnVisibility = {
  type: boolean;
  status: boolean;
  updated: boolean;
};

export function DocumentTable({
  documents,
  username,
  onDelete,
  onShare,
  getDocumentTypeIcon,
  getStatusColor,
  onBatchDelete,
}: DocumentTableProps) {
  const router = useRouter();
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>({
    type: true,
    status: true,
    updated: true,
  });

  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [selectAllState, setSelectAllState] = useState<
    boolean | 'indeterminate'
  >(false);

  const toggleColumn = (column: keyof ColumnVisibility) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [column]: !prev[column],
    }));
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const sortedDocuments = [...documents].sort((a, b) => {
    if (!sortColumn) return 0;

    let valueA, valueB;

    switch (sortColumn) {
      case 'title':
        valueA = (a.title ?? '').toLowerCase();
        valueB = (b.title ?? '').toLowerCase();
        break;
      case 'type':
        valueA = (a.document_type ?? '').toLowerCase();
        valueB = (b.document_type ?? '').toLowerCase();
        break;
      case 'status':
        valueA = (a.status ?? '').toLowerCase();
        valueB = (b.status ?? '').toLowerCase();
        break;
      case 'updated':
        valueA = new Date(a.updated_at ?? '').getTime();
        valueB = new Date(b.updated_at ?? '').getTime();
        break;
      default:
        return 0;
    }

    if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
    if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const getSortIcon = (column: string) => {
    if (sortColumn !== column) return null;
    return <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>;
  };

  const handleView = (id: string) => {
    router.push(`/${username}/documents/${id}`);
  };

  const handleEdit = (id: string) => {
    router.push(`/${username}/documents/${id}/edit`);
  };

  const toggleSelectDocument = (
    id: string,
    checked?: boolean | 'indeterminate'
  ) => {
    if (checked === 'indeterminate') return;

    setSelectedDocuments((prev) => {
      let newSelected;
      if (checked === false) {
        newSelected = prev.filter((docId) => docId !== id);
      } else if (checked === true) {
        newSelected = [...prev, id];
      } else {
        // Toggle behavior for backward compatibility
        if (prev.includes(id)) {
          newSelected = prev.filter((docId) => docId !== id);
        } else {
          newSelected = [...prev, id];
        }
      }

      // Update selectAllState based on the new selection
      updateSelectAllState(newSelected);
      return newSelected;
    });
  };

  // Helper function to update the selectAllState based on selected documents
  const updateSelectAllState = (selected: string[] = selectedDocuments) => {
    if (selected.length === 0) {
      setSelectAllState(false);
    } else if (selected.length === documents.length) {
      setSelectAllState(true);
    } else {
      setSelectAllState('indeterminate');
    }
  };

  // Effect to update selectAllState when documents change
  useEffect(() => {
    updateSelectAllState();
  }, [documents.length]);

  const toggleSelectAll = (checked: boolean | 'indeterminate') => {
    if (checked === 'indeterminate') {
      // If currently indeterminate, select all
      setSelectedDocuments(
        documents.map((doc) => doc.id).filter((id): id is string => id !== null)
      );
      setSelectAllState(true);
    } else if (checked) {
      // If checked, select all
      setSelectedDocuments(
        documents.map((doc) => doc.id).filter((id): id is string => id !== null)
      );
      setSelectAllState(true);
    } else {
      // If unchecked, deselect all
      setSelectedDocuments([]);
      setSelectAllState(false);
    }
  };

  const handleBatchDelete = () => {
    if (selectedDocuments.length > 0 && onBatchDelete) {
      onBatchDelete(selectedDocuments);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        {selectedDocuments.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {selectedDocuments.length}{' '}
              {selectedDocuments.length === 1 ? 'document' : 'documents'}{' '}
              selected
            </span>
            <Button
              variant="shadow_red"
              size="sm"
              onClick={handleBatchDelete}
              className="uppercase text-sm rounded-lg"
            >
              <TrashFill className="mr-2 h-4 w-4" />
              Delete Selected
            </Button>
          </div>
        )}
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox
                  checked={selectAllState}
                  onCheckedChange={toggleSelectAll}
                />
              </TableHead>
              <TableHead
                className="w-[300px] cursor-pointer"
                onClick={() => handleSort('title')}
              >
                Title {getSortIcon('title')}
              </TableHead>
              {columnVisibility.type && (
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('type')}
                >
                  Type {getSortIcon('type')}
                </TableHead>
              )}
              {columnVisibility.status && (
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  Status {getSortIcon('status')}
                </TableHead>
              )}
              {columnVisibility.updated && (
                <TableHead
                  className="cursor-pointer"
                  onClick={() => handleSort('updated')}
                >
                  Last Updated {getSortIcon('updated')}
                </TableHead>
              )}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedDocuments.map((doc) => (
              <TableRow
                key={doc.id ?? ''}
                className={
                  doc.id && selectedDocuments.includes(doc.id)
                    ? 'bg-accent-50/20'
                    : ''
                }
              >
                <TableCell>
                  <Checkbox
                    checked={
                      doc.id ? selectedDocuments.includes(doc.id) : false
                    }
                    onCheckedChange={(checked) =>
                      doc.id && toggleSelectDocument(doc.id, checked)
                    }
                  />
                </TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center space-x-2">
                    {getDocumentTypeIcon(doc.document_type ?? '')}
                    <span>{doc.title}</span>
                    {doc.is_template && (
                      <Badge variant="outline" className="ml-2">
                        <FileTextIcon className="mr-1 h-3 w-3" />
                        Template
                      </Badge>
                    )}
                  </div>
                  {doc.description && (
                    <p className="text-xs text-muted-foreground line-clamp-1 mt-1">
                      {doc.description}
                    </p>
                  )}
                </TableCell>
                {columnVisibility.type && (
                  <TableCell>{doc.document_type}</TableCell>
                )}
                {columnVisibility.status && (
                  <TableCell>
                    <div className="flex items-center">
                      <Badge className={getStatusColor(doc.status ?? '')}>
                        <div className="flex items-center gap-1">
                          <span className="h-2 w-2 rounded-full bg-current"></span>
                          <span className="capitalize">{doc.status}</span>
                        </div>
                      </Badge>
                    </div>
                  </TableCell>
                )}
                {columnVisibility.updated && (
                  <TableCell>
                    {formatDistanceToNow(new Date(doc.updated_at ?? ''), {
                      addSuffix: true,
                    })}
                  </TableCell>
                )}
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleView(doc.id ?? '')}
                    >
                      View
                    </Button>
                    {onShare && (
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => onShare(doc.id ?? '')}
                      >
                        <ShareIcon className="h-4 w-4" />
                        <span className="sr-only">Share</span>
                      </Button>
                    )}
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => handleEdit(doc.id ?? '')}
                    >
                      <PencilIcon className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      onClick={() => onDelete(doc.id ?? '')}
                    >
                      <TrashIcon className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
