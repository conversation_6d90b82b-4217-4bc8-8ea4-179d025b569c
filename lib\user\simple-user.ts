'use client';

import { supabaseClient } from '@/lib/supabase/client';

/**
 * Get the current authenticated user
 */
export async function getCurrentUser() {
  try {
    const { data, error } = await supabaseClient.auth.getUser();

    if (error) {
      console.error('Error getting current user:', error);
      return null;
    }

    return data.user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Check if a user is a lawyer
 */
export async function isUserLawyer(userId: string): Promise<boolean> {
  try {
    // First check if the user has a lawyer role in profiles
    const { data: profileData, error: profileError } = await supabaseClient
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (!profileError && profileData) {
      return true;
    }

    // Then check if the user has a record in the lawyers table
    const { data: lawyerData, error: lawyerError } = await supabaseClient
      .from('lawyers')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (!lawyerError && lawyerData) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking if user is lawyer:', error);
    return false;
  }
}
