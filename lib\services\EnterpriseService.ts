import {
  Feature,
  Organization,
  Permission,
  SubscriptionLevel,
  Team,
  TeamMember,
  TeamRole,
} from '@/types';
import { NotificationService } from './NotificationService';

/**
 * Service for managing enterprise organizations, teams, and permissions
 */
export class EnterpriseService {
  private static instance: EnterpriseService;
  private notificationService: NotificationService;
  private currentUser: { id: string; email: string } | null = null;

  // In-memory storage for demo purposes
  // In production, this would use a database
  private organizations: Map<string, Organization> = new Map();
  private teams: Map<string, Team> = new Map();
  private teamMembers: Map<string, TeamMember> = new Map();

  private constructor() {
    this.notificationService = NotificationService.getInstance();
    this.initializeDummyData();
  }

  public static getInstance(): EnterpriseService {
    if (!EnterpriseService.instance) {
      EnterpriseService.instance = new EnterpriseService();
    }
    return EnterpriseService.instance;
  }

  /**
   * Set the current user
   */
  public setCurrentUser(user: { id: string; email: string }): void {
    this.currentUser = user;
  }

  /**
   * Get the current user
   */
  public getCurrentUser(): { id: string; email: string } | null {
    return this.currentUser;
  }

  /**
   * Create a new organization
   */
  public async createOrganization(
    name: string,
    adminUserId: string,
    subscriptionLevel: SubscriptionLevel = 'basic'
  ): Promise<Organization> {
    if (!this.currentUser) {
      throw new Error('User must be logged in to create an organization');
    }

    const orgId = `org_${Math.random().toString(36).substring(2, 15)}`;

    const organization: Organization = {
      id: orgId,
      name,
      adminUsers: [adminUserId],
      teams: [],
      subscriptionLevel,
      features: this.getFeaturesBySubscriptionLevel(subscriptionLevel),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Create a default team for the organization
    const defaultTeam = await this.createTeam(
      orgId,
      'Default Team',
      'Default team for all members'
    );

    // Update the organization with the default team
    organization.teams = [defaultTeam];

    // Store the organization
    this.organizations.set(orgId, organization);

    // Add the creator as a team member of the default team
    await this.addTeamMember(defaultTeam.id, adminUserId, 'editor');

    this.notificationService.showSuccess(
      `Organization "${name}" created successfully`
    );

    return organization;
  }

  /**
   * Get an organization by ID
   */
  public async getOrganization(
    organizationId: string
  ): Promise<Organization | null> {
    return this.organizations.get(organizationId) || null;
  }

  /**
   * Get all organizations for the current user
   */
  public async getUserOrganizations(): Promise<Organization[]> {
    if (!this.currentUser) {
      return [];
    }

    const userOrgs: Organization[] = [];

    // Find organizations where the user is an admin
    for (const org of this.organizations.values()) {
      if (org.adminUsers.includes(this.currentUser.id)) {
        userOrgs.push(org);
        continue;
      }

      // Find organizations where the user is a member of any team
      for (const team of org.teams) {
        const isMember = team.members.some(
          (m) => m.userId === this.currentUser?.id
        );
        if (isMember && !userOrgs.includes(org)) {
          userOrgs.push(org);
          break;
        }
      }
    }

    return userOrgs;
  }

  /**
   * Update an organization
   */
  public async updateOrganization(
    organizationId: string,
    updates: Partial<Organization>
  ): Promise<Organization | null> {
    const organization = this.organizations.get(organizationId);

    if (!organization) {
      this.notificationService.showError('Organization not found');
      return null;
    }

    // Check if user has permission
    if (!this.hasOrganizationAdminAccess(organizationId)) {
      this.notificationService.showError(
        'You do not have permission to update this organization'
      );
      return null;
    }

    // Update the organization
    const updatedOrganization = {
      ...organization,
      ...updates,
      id: organization.id, // Ensure ID cannot be changed
      updatedAt: new Date(),
    };

    this.organizations.set(organizationId, updatedOrganization);
    this.notificationService.showSuccess('Organization updated successfully');

    return updatedOrganization;
  }

  /**
   * Create a new team within an organization
   */
  public async createTeam(
    organizationId: string,
    name: string,
    description?: string
  ): Promise<Team> {
    const organization = this.organizations.get(organizationId);

    if (!organization) {
      throw new Error('Organization not found');
    }

    // Check if user has permission to create a team
    if (!this.hasOrganizationAdminAccess(organizationId)) {
      throw new Error(
        'You do not have permission to create a team in this organization'
      );
    }

    const teamId = `team_${Math.random().toString(36).substring(2, 15)}`;

    const team: Team = {
      id: teamId,
      name,
      organizationId,
      description,
      members: [],
      permissions: this.getDefaultTeamPermissions(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store the team
    this.teams.set(teamId, team);

    // Update the organization's teams
    organization.teams.push(team);
    this.organizations.set(organizationId, organization);

    return team;
  }

  /**
   * Get a team by ID
   */
  public async getTeam(teamId: string): Promise<Team | null> {
    return this.teams.get(teamId) || null;
  }

  /**
   * Update a team
   */
  public async updateTeam(
    teamId: string,
    updates: Partial<Team>
  ): Promise<Team | null> {
    const team = this.teams.get(teamId);

    if (!team) {
      this.notificationService.showError('Team not found');
      return null;
    }

    // Check if user has permission
    if (!this.hasTeamAdminAccess(teamId)) {
      this.notificationService.showError(
        'You do not have permission to update this team'
      );
      return null;
    }

    // Update the team
    const updatedTeam = {
      ...team,
      ...updates,
      id: team.id, // Ensure ID cannot be changed
      organizationId: team.organizationId, // Ensure org ID cannot be changed
      updatedAt: new Date(),
    };

    this.teams.set(teamId, updatedTeam);

    // Also update the team in the organization
    const organization = this.organizations.get(team.organizationId);
    if (organization) {
      const teamIndex = organization.teams.findIndex((t) => t.id === teamId);
      if (teamIndex >= 0) {
        organization.teams[teamIndex] = updatedTeam;
        this.organizations.set(organization.id, organization);
      }
    }

    this.notificationService.showSuccess('Team updated successfully');
    return updatedTeam;
  }

  /**
   * Add a user to a team
   */
  public async addTeamMember(
    teamId: string,
    userId: string,
    role: TeamRole = 'viewer'
  ): Promise<TeamMember | null> {
    const team = this.teams.get(teamId);

    if (!team) {
      this.notificationService.showError('Team not found');
      return null;
    }

    // Check if user has permission to add members
    if (!this.hasTeamAdminAccess(teamId)) {
      this.notificationService.showError(
        'You do not have permission to add members to this team'
      );
      return null;
    }

    // Check if user is already a member
    const existingMember = team.members.find((m) => m.userId === userId);
    if (existingMember) {
      this.notificationService.showWarning(
        'User is already a member of this team'
      );
      return existingMember;
    }

    const memberId = `member_${Math.random().toString(36).substring(2, 15)}`;

    const teamMember: TeamMember = {
      id: memberId,
      userId,
      teamId,
      role,
      joinedAt: new Date(),
    };

    // Store the team member
    this.teamMembers.set(memberId, teamMember);

    // Update the team's members
    team.members.push(teamMember);
    this.teams.set(teamId, team);

    // Also update the team in the organization
    const organization = this.organizations.get(team.organizationId);
    if (organization) {
      const teamIndex = organization.teams.findIndex((t) => t.id === teamId);
      if (teamIndex >= 0) {
        organization.teams[teamIndex] = team;
        this.organizations.set(organization.id, organization);
      }
    }

    this.notificationService.showSuccess('Team member added successfully');
    return teamMember;
  }

  /**
   * Update a team member's role
   */
  public async updateTeamMemberRole(
    teamId: string,
    userId: string,
    newRole: TeamRole
  ): Promise<TeamMember | null> {
    const team = this.teams.get(teamId);

    if (!team) {
      this.notificationService.showError('Team not found');
      return null;
    }

    // Check if user has permission to update members
    if (!this.hasTeamAdminAccess(teamId)) {
      this.notificationService.showError(
        'You do not have permission to update members in this team'
      );
      return null;
    }

    // Find the team member
    const memberIndex = team.members.findIndex((m) => m.userId === userId);
    if (memberIndex < 0) {
      this.notificationService.showError('Team member not found');
      return null;
    }

    // Update the member's role
    team.members[memberIndex].role = newRole;
    this.teams.set(teamId, team);

    // Update the member in the storage
    const memberId = team.members[memberIndex].id;
    if (this.teamMembers.has(memberId)) {
      const member = this.teamMembers.get(memberId)!;
      member.role = newRole;
      this.teamMembers.set(memberId, member);
    }

    // Also update the team in the organization
    const organization = this.organizations.get(team.organizationId);
    if (organization) {
      const teamIndex = organization.teams.findIndex((t) => t.id === teamId);
      if (teamIndex >= 0) {
        organization.teams[teamIndex] = team;
        this.organizations.set(organization.id, organization);
      }
    }

    this.notificationService.showSuccess(
      'Team member role updated successfully'
    );
    return team.members[memberIndex];
  }

  /**
   * Remove a user from a team
   */
  public async removeTeamMember(
    teamId: string,
    userId: string
  ): Promise<boolean> {
    const team = this.teams.get(teamId);

    if (!team) {
      this.notificationService.showError('Team not found');
      return false;
    }

    // Check if user has permission to remove members
    if (!this.hasTeamAdminAccess(teamId)) {
      this.notificationService.showError(
        'You do not have permission to remove members from this team'
      );
      return false;
    }

    // Find the team member
    const memberIndex = team.members.findIndex((m) => m.userId === userId);
    if (memberIndex < 0) {
      this.notificationService.showError('Team member not found');
      return false;
    }

    // Get the member ID before removing
    const memberId = team.members[memberIndex].id;

    // Remove the member from the team
    team.members.splice(memberIndex, 1);
    this.teams.set(teamId, team);

    // Remove the member from storage
    this.teamMembers.delete(memberId);

    // Also update the team in the organization
    const organization = this.organizations.get(team.organizationId);
    if (organization) {
      const teamIndex = organization.teams.findIndex((t) => t.id === teamId);
      if (teamIndex >= 0) {
        organization.teams[teamIndex] = team;
        this.organizations.set(organization.id, organization);
      }
    }

    this.notificationService.showSuccess('Team member removed successfully');
    return true;
  }

  /**
   * Check if the current user has admin access to an organization
   */
  public hasOrganizationAdminAccess(organizationId: string): boolean {
    if (!this.currentUser) {
      return false;
    }

    const organization = this.organizations.get(organizationId);
    if (!organization) {
      return false;
    }

    // Check if user is an admin of the organization
    return organization.adminUsers.includes(this.currentUser.id);
  }

  /**
   * Check if the current user has admin access to a team
   */
  public hasTeamAdminAccess(teamId: string): boolean {
    if (!this.currentUser) {
      return false;
    }

    const team = this.teams.get(teamId);
    if (!team) {
      return false;
    }

    // Check if user is an admin of the parent organization
    if (this.hasOrganizationAdminAccess(team.organizationId)) {
      return true;
    }

    // Check if user is an editor of the team
    return team.members.some(
      (m) => m.userId === this.currentUser?.id && m.role === 'editor'
    );
  }

  /**
   * Check if the current user has a specific permission on a team
   */
  public hasTeamPermission(
    teamId: string,
    resource: string,
    action: string
  ): boolean {
    if (!this.currentUser) {
      return false;
    }

    const team = this.teams.get(teamId);
    if (!team) {
      return false;
    }

    // Admins always have all permissions
    if (this.hasTeamAdminAccess(teamId)) {
      return true;
    }

    // Find the user's role in the team
    const member = team.members.find((m) => m.userId === this.currentUser?.id);
    if (!member) {
      return false;
    }

    // Editors can create, read and update but not delete or manage
    if (member.role === 'editor') {
      if (action === 'create' || action === 'read' || action === 'update') {
        return true;
      }
      // Check specific permissions
      return team.permissions.some(
        (p) => p.resource === resource && p.action === action
      );
    }

    // Viewers can only read
    if (member.role === 'viewer') {
      if (action === 'read') {
        return true;
      }
      // Check specific permissions
      return team.permissions.some(
        (p) => p.resource === resource && p.action === action
      );
    }

    return false;
  }

  /**
   * Get features available for a subscription level
   */
  private getFeaturesBySubscriptionLevel(level: SubscriptionLevel): Feature[] {
    const features: Feature[] = [
      {
        id: 'feature_basic_forms',
        name: 'Basic Forms',
        description: 'Create and manage basic forms',
        enabled: true,
      },
      {
        id: 'feature_advanced_forms',
        name: 'Advanced Forms',
        description: 'Create and manage advanced forms with conditional logic',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_ai_suggestions',
        name: 'AI Suggestions',
        description: 'Get AI-powered suggestions for form fields',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_blockchain',
        name: 'Blockchain Verification',
        description: 'Verify documents on the blockchain',
        enabled: level === 'enterprise',
      },
      {
        id: 'feature_custom_branding',
        name: 'Custom Branding',
        description: 'Add custom branding to forms and documents',
        enabled: level !== 'basic',
      },
      {
        id: 'feature_team_collaboration',
        name: 'Team Collaboration',
        description: 'Collaborate with team members on forms',
        enabled: true,
      },
      {
        id: 'feature_consulting',
        name: 'Consulting Services',
        description: 'Access professional consulting services',
        enabled: level === 'enterprise',
      },
    ];

    return features;
  }

  /**
   * Get default permissions for a new team
   */
  private getDefaultTeamPermissions(): Permission[] {
    const now = new Date();
    return [
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'create',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'read',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'form',
        action: 'update',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'document',
        action: 'create',
        createdAt: now,
      },
      {
        id: `perm_${Math.random().toString(36).substring(2, 15)}`,
        resource: 'document',
        action: 'read',
        createdAt: now,
      },
    ];
  }

  /**
   * Initialize dummy data for demo purposes
   */
  private initializeDummyData(): void {
    // Create a demo user
    this.currentUser = {
      id: 'user_demo',
      email: '<EMAIL>',
    };

    // Create a demo organization
    const demoOrg: Organization = {
      id: 'org_demo',
      name: 'Demo Organization',
      adminUsers: ['user_demo'],
      teams: [],
      subscriptionLevel: 'enterprise',
      features: this.getFeaturesBySubscriptionLevel('enterprise'),
      createdAt: new Date(),
      updatedAt: new Date(),
      logoUrl: 'https://via.placeholder.com/150',
      primaryColor: '#4f46e5',
      contactEmail: '<EMAIL>',
    };

    // Create a demo team
    const demoTeam: Team = {
      id: 'team_demo',
      name: 'Demo Team',
      organizationId: 'org_demo',
      description: 'A team for demonstration purposes',
      members: [],
      permissions: this.getDefaultTeamPermissions(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Create demo team members
    const demoMember1: TeamMember = {
      id: 'member_demo1',
      userId: 'user_demo',
      teamId: 'team_demo',
      role: 'admin',
      joinedAt: new Date(),
      name: 'Demo User',
      email: '<EMAIL>',
    };

    const demoMember2: TeamMember = {
      id: 'member_demo2',
      userId: 'user_other',
      teamId: 'team_demo',
      role: 'editor',
      joinedAt: new Date(),
      name: 'Other User',
      email: '<EMAIL>',
    };

    // Add members to team
    demoTeam.members = [demoMember1, demoMember2];

    // Add team to organization
    demoOrg.teams = [demoTeam];

    // Store in our maps
    this.organizations.set(demoOrg.id, demoOrg);
    this.teams.set(demoTeam.id, demoTeam);
    this.teamMembers.set(demoMember1.id, demoMember1);
    this.teamMembers.set(demoMember2.id, demoMember2);
  }
}
