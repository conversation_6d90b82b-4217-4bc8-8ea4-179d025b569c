'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { FileImage, Pen, Trash } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

interface SignatureSectionProps {
  onSignatureChange: (signatureData: string | null) => void;
  initialSignature?: string | null;
  className?: string;
}

export default function SignatureSection({
  onSignatureChange,
  initialSignature = null,
  className,
}: SignatureSectionProps) {
  // Using Sonner toast
  const [activeTab, setActiveTab] = useState<string>('draw');
  const [signatureData, setSignatureData] = useState<string | null>(
    initialSignature
  );
  const [name, setName] = useState<string>('');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasContext, setCanvasContext] =
    useState<CanvasRenderingContext2D | null>(null);

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match its display size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Set up drawing style
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = '#000';

    setCanvasContext(ctx);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // If there's an initial signature, draw it
    if (initialSignature && initialSignature.startsWith('data:image')) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      };
      img.src = initialSignature;
    }
  }, [initialSignature]);

  // Handle drawing
  const startDrawing = useCallback(
    (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>
    ) => {
      if (!canvasContext) return;
      setIsDrawing(true);

      const canvas = canvasRef.current;
      if (!canvas) return;

      let x, y;
      if ('touches' in e) {
        // Touch event
        const rect = canvas.getBoundingClientRect();
        x = e.touches[0].clientX - rect.left;
        y = e.touches[0].clientY - rect.top;
      } else {
        // Mouse event
        x = e.nativeEvent.offsetX;
        y = e.nativeEvent.offsetY;
      }

      canvasContext.beginPath();
      canvasContext.moveTo(x, y);
    },
    [canvasContext]
  );

  const draw = useCallback(
    (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>
    ) => {
      if (!isDrawing || !canvasContext) return;

      const canvas = canvasRef.current;
      if (!canvas) return;

      let x, y;
      if ('touches' in e) {
        // Touch event
        const rect = canvas.getBoundingClientRect();
        x = e.touches[0].clientX - rect.left;
        y = e.touches[0].clientY - rect.top;
      } else {
        // Mouse event
        x = e.nativeEvent.offsetX;
        y = e.nativeEvent.offsetY;
      }

      canvasContext.lineTo(x, y);
      canvasContext.stroke();
    },
    [isDrawing, canvasContext]
  );

  const stopDrawing = useCallback(() => {
    if (!isDrawing || !canvasContext || !canvasRef.current) return;

    setIsDrawing(false);
    canvasContext.closePath();

    // Save the signature
    const canvas = canvasRef.current;
    const dataUrl = canvas.toDataURL('image/png');
    setSignatureData(dataUrl);
    onSignatureChange(dataUrl);
  }, [isDrawing, canvasContext, onSignatureChange]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Invalid file type', {
        description: 'Please upload an image file (JPEG, PNG, etc.)',
      });
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File too large', {
        description: 'Please upload an image smaller than 2MB',
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const dataUrl = event.target?.result as string;
      setSignatureData(dataUrl);
      onSignatureChange(dataUrl);

      // Also display the image on the canvas
      if (canvasContext && canvasRef.current) {
        const img = new Image();
        img.onload = () => {
          if (!canvasContext || !canvasRef.current) return;

          // Clear canvas
          canvasContext.clearRect(
            0,
            0,
            canvasRef.current.width,
            canvasRef.current.height
          );

          // Calculate dimensions to fit the image while maintaining aspect ratio
          const canvas = canvasRef.current;
          const ratio = Math.min(
            canvas.width / img.width,
            canvas.height / img.height
          );
          const centerX = (canvas.width - img.width * ratio) / 2;
          const centerY = (canvas.height - img.height * ratio) / 2;

          // Draw the image centered and scaled
          canvasContext.drawImage(
            img,
            centerX,
            centerY,
            img.width * ratio,
            img.height * ratio
          );
        };
        img.src = dataUrl;
      }
    };
    reader.readAsDataURL(file);
  };

  // Clear signature
  const clearSignature = () => {
    if (!canvasContext || !canvasRef.current) return;

    canvasContext.clearRect(
      0,
      0,
      canvasRef.current.width,
      canvasRef.current.height
    );
    setSignatureData(null);
    onSignatureChange(null);
  };

  // Type signature
  const handleTypeSignature = () => {
    if (!name.trim()) {
      toast.error('Name required', {
        description: 'Please enter your name for the signature',
      });
      return;
    }

    if (!canvasContext || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvasContext;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set font style for signature
    // Use a more widely available cursive font with fallbacks
    ctx.font =
      '40px "Brush Script MT", "Comic Sans MS", "Segoe Script", cursive';
    ctx.fillStyle = '#000';

    // Center the text
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Draw the signature
    try {
      ctx.fillText(name, canvas.width / 2, canvas.height / 2);
    } catch (error) {
      console.error('Error drawing text:', error);
      // Fallback to a simpler font if the cursive font fails
      ctx.font = '40px Arial, sans-serif';
      ctx.fillText(name, canvas.width / 2, canvas.height / 2);
    }

    // Save the signature
    const dataUrl = canvas.toDataURL('image/png');
    setSignatureData(dataUrl);
    onSignatureChange(dataUrl);
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Pen className="h-5 w-5" />
          Signature
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue="draw"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="draw">Draw</TabsTrigger>
            <TabsTrigger value="type">Type</TabsTrigger>
            <TabsTrigger value="upload">Upload</TabsTrigger>
          </TabsList>

          <TabsContent value="draw" className="space-y-4">
            <div className="border rounded-md p-2 bg-white">
              <canvas
                ref={canvasRef}
                className="w-full h-32 border border-dashed border-gray-300 rounded-md touch-none"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={stopDrawing}
              />
            </div>
            <div className="text-xs text-muted-foreground text-center">
              Draw your signature above
            </div>
          </TabsContent>

          <TabsContent value="type" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="signature-name">Your Name</Label>
              <Input
                id="signature-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Type your full name"
              />
              <Button onClick={handleTypeSignature} className="w-full">
                Generate Signature
              </Button>
            </div>

            {signatureData && activeTab === 'type' && (
              <div className="border rounded-md p-2 bg-white">
                <img
                  src={signatureData}
                  alt="Signature"
                  className="max-h-32 mx-auto"
                />
              </div>
            )}
          </TabsContent>

          <TabsContent value="upload" className="space-y-4">
            <div className="border rounded-md p-4 border-dashed">
              <div className="flex flex-col items-center gap-2">
                <FileImage className="h-8 w-8 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Upload a signature image
                </p>
                <Input
                  id="signature-file"
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="max-w-xs"
                />
              </div>
            </div>

            {signatureData && activeTab === 'upload' && (
              <div className="border rounded-md p-2 bg-white">
                <img
                  src={signatureData}
                  alt="Signature"
                  className="max-h-32 mx-auto"
                />
              </div>
            )}
          </TabsContent>
        </Tabs>

        {signatureData && (
          <div className="mt-4 flex justify-end">
            <Button
              variant="shadow_red"
              size="sm"
              onClick={clearSignature}
              className="flex items-center gap-1"
            >
              <Trash className="h-4 w-4" />
              Clear Signature
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
