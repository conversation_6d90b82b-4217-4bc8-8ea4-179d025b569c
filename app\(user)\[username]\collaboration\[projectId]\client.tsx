'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useCollaboration } from '@/lib/hooks';
import { avatar_male_001 } from '@/lib/imgs';
import {
  ArrowLeft,
  Calendar,
  CheckCircle2,
  Clock,
  FileText,
  MoreHorizontal,
  PencilLine,
  Plus,
  Share2,
  Trash2,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignedTo?: string;
  dueDate?: string;
  createdBy: string;
  createdAt: string;
}

interface Member {
  id: string;
  userId: string;
  fullName?: string;
  email: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  createdAt: string;
}

interface Comment {
  id: string;
  content: string;
  userId: string;
  userName: string;
  createdAt: string;
  userAvatar?: string | null;
}

interface Document {
  id: string;
  title: string;
  type: string;
  addedBy: string;
  addedAt: string;
}

export function ProjectDetailClient({
  username,
  projectId,
}: {
  username: string;
  projectId: string;
}) {
  const {
    getProjectById,
    fetchProjectMembers,
    fetchProjectTasks,
    createNewTask,
    deleteExistingProject,
    fetchProjectComments,
    fetchProjectDocuments,
    addComment,
  } = useCollaboration();

  const [project, setProject] = useState<any>(null);
  const [members, setMembers] = useState<Member[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function fetchProjectData() {
      setIsLoading(true);
      try {
        console.log('Fetching project details for:', projectId);

        // Fetch project details
        const projectData = await getProjectById(projectId);

        if (!projectData) {
          console.log('Project not found or access denied');
          // Don't throw, just set error state
          setError(
            new Error(
              'Project not found or you do not have access to this project'
            )
          );
          setIsLoading(false);
          return;
        }

        console.log('Project data loaded successfully:', projectData);
        setProject(projectData);

        // Fetch project members
        try {
          console.log('Fetching project members');
          const membersData = await fetchProjectMembers(projectId);
          console.log('Members data:', membersData);

          // Map ProjectMember to our Member interface
          const formattedMembers = membersData.map((member: any) => ({
            id: member.id,
            userId: member.user_id,
            fullName: member.user?.full_name || undefined,
            email: member.user?.email || '<EMAIL>',
            role: member.role as 'owner' | 'admin' | 'member' | 'viewer',
            createdAt: member.created_at,
          }));

          setMembers(formattedMembers);
        } catch (memberError) {
          console.error('Error fetching members:', memberError);
          // Continue with empty members array
          setMembers([]);
        }

        // Fetch project tasks
        try {
          console.log('Fetching project tasks');
          const tasksData = await fetchProjectTasks(projectId);
          console.log('Tasks data:', tasksData);

          // Map ProjectTask to our Task interface
          const formattedTasks = tasksData.map((task: any) => ({
            id: task.id,
            title: task.title,
            description: task.description || undefined,
            status: task.status as 'todo' | 'in_progress' | 'review' | 'done',
            priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
            assignedTo: task.assigned_to || undefined,
            dueDate: task.due_date || undefined,
            createdBy: task.created_by,
            createdAt: task.created_at,
          }));

          setTasks(formattedTasks);
        } catch (taskError) {
          console.error('Error fetching tasks:', taskError);
          // Continue with empty tasks array
          setTasks([]);
        }

        // Fetch project comments
        try {
          console.log('Fetching project comments');
          const commentsData = await fetchProjectComments(projectId);
          console.log('Comments data:', commentsData);

          // Map ProjectComment to our Comment interface
          const formattedComments = commentsData.map((comment) => ({
            id: comment.id,
            content: comment.content,
            userId: comment.user_id,
            userName: comment.user?.full_name || 'Unknown User',
            createdAt: new Date(comment.created_at).toLocaleString(),
            userAvatar: comment.user?.avatar_url,
          }));

          setComments(formattedComments);
        } catch (commentError) {
          console.error('Error fetching comments:', commentError);
          // Continue with empty comments array
          setComments([]);
        }

        // Fetch project documents
        try {
          console.log('Fetching project documents');
          const documentsData = await fetchProjectDocuments(projectId);
          console.log('Documents data:', documentsData);

          // Map ProjectDocument to our Document interface
          const formattedDocuments = documentsData.map((doc) => ({
            id: doc.id,
            title: doc.document?.title || 'Untitled Document',
            type: doc.document?.document_type || 'Unknown',
            addedBy: 'Team Member', // We don't have this info directly
            addedAt: new Date(doc.created_at).toLocaleString(),
          }));

          setDocuments(formattedDocuments);
        } catch (documentError) {
          console.error('Error fetching documents:', documentError);
          // Continue with empty documents array
          setDocuments([]);
        }
      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
      } finally {
        setIsLoading(false);
      }
    }

    fetchProjectData();
  }, [
    projectId,
    getProjectById,
    fetchProjectMembers,
    fetchProjectTasks,
    fetchProjectComments,
    fetchProjectDocuments,
  ]);

  const handleCreateTask = async (taskData: any) => {
    try {
      await createNewTask(
        projectId,
        taskData.title,
        taskData.description,
        taskData.status,
        taskData.priority,
        taskData.assignedTo,
        taskData.dueDate
      );

      // Refresh tasks
      const tasksData = await fetchProjectTasks(projectId);

      // Map ProjectTask to our Task interface
      const formattedTasks = tasksData.map((task: any) => ({
        id: task.id,
        title: task.title,
        description: task.description || undefined,
        status: task.status as 'todo' | 'in_progress' | 'review' | 'done',
        priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
        assignedTo: task.assigned_to || undefined,
        dueDate: task.due_date || undefined,
        createdBy: task.created_by,
        createdAt: task.created_at,
      }));

      setTasks(formattedTasks);

      toast.success('Task created successfully');
    } catch (error) {
      console.error('Error creating task:', error);
      toast.error('Failed to create task');
    }
  };

  const handleAddComment = async () => {
    const commentInput = document.getElementById(
      'comment-input'
    ) as HTMLTextAreaElement;
    const content = commentInput?.value?.trim();

    if (!content) {
      toast.error('Please enter a comment');
      return;
    }

    try {
      await addComment(projectId, content);

      // Clear the input
      commentInput.value = '';

      // Refresh comments
      const commentsData = await fetchProjectComments(projectId);

      // Map ProjectComment to our Comment interface
      const formattedComments = commentsData.map((comment: any) => ({
        id: comment.id,
        content: comment.content,
        userId: comment.user_id,
        userName: comment.user?.full_name || 'Unknown User',
        createdAt: new Date(comment.created_at).toLocaleString(),
        userAvatar: comment.user?.avatar_url,
      }));

      setComments(formattedComments);

      toast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  const handleDeleteProject = async () => {
    if (
      window.confirm(
        'Are you sure you want to delete this project? This action cannot be undone.'
      )
    ) {
      try {
        await deleteExistingProject(projectId);
        toast.success('Project deleted successfully');
        window.location.href = `/${username}/collaboration`;
      } catch (error) {
        console.error('Error deleting project:', error);
        toast.error('Failed to delete project');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/collaboration`}
            className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
          >
            <ArrowLeft className="size-4" />
            <span>Back to Projects</span>
          </Link>
        </div>
        <div className="animate-pulse">
          <div className="h-8 w-1/3 bg-neutral-200 rounded mb-4"></div>
          <div className="h-4 w-1/2 bg-neutral-200 rounded mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <div className="h-64 bg-neutral-200 rounded mb-6"></div>
            </div>
            <div>
              <div className="h-40 bg-neutral-200 rounded mb-4"></div>
              <div className="h-40 bg-neutral-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Link
            href={`/${username}/collaboration`}
            className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
          >
            <ArrowLeft className="size-4" />
            <span>Back to Projects</span>
          </Link>
        </div>
        <Card>
          <CardContent className="p-8 flex flex-col items-center justify-center">
            <div className="text-red-500 mb-4 text-xl">Error</div>
            <p className="text-neutral-600 mb-4">
              {error?.message || 'Project not found'}
            </p>
            <Button asChild>
              <Link href={`/${username}/collaboration`}>
                Return to Projects
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate project progress
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter((task) => task.status === 'done').length;
  const progress =
    totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  // Calculate days remaining
  const endDate = new Date(project.end_date);
  const now = new Date();
  const daysRemaining = Math.ceil(
    (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  );

  // Determine priority based on days remaining
  let priority = 'Medium';
  if (daysRemaining < 7) {
    priority = 'High';
  } else if (daysRemaining > 30) {
    priority = 'Low';
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link
          href={`/${username}/collaboration`}
          className="flex items-center gap-1 text-neutral-600 hover:text-neutral-900"
        >
          <ArrowLeft className="size-4" />
          <span>Back to Projects</span>
        </Link>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold">{project.name}</h1>
          <p className="text-neutral-600">{project.description}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-1">
            <Share2 className="size-4" />
            <span>Share</span>
          </Button>
          <Button variant="outline" className="gap-1">
            <PencilLine className="size-4" />
            <span>Edit</span>
          </Button>
          <Button
            variant="outline"
            className="gap-1 text-red-600 hover:bg-red-50 hover:text-red-700 border-red-200"
            onClick={handleDeleteProject}
          >
            <Trash2 className="size-4" />
            <span>Delete</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="comments">Comments</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>Project Overview</CardTitle>
                  <CardDescription>
                    Key information about the project
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-2">Progress</h3>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">
                        {completedTasks} of {totalTasks} tasks completed
                      </span>
                      <span className="text-sm font-medium">{progress}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Start Date</h3>
                      <div className="flex items-center gap-2">
                        <Calendar className="size-4 text-neutral-500" />
                        <span>
                          {new Date(project.start_date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-2">End Date</h3>
                      <div className="flex items-center gap-2">
                        <Calendar className="size-4 text-neutral-500" />
                        <span>
                          {new Date(project.end_date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Status</h3>
                    <Badge
                      className={
                        project.status === 'active'
                          ? 'bg-green-100 text-green-800 hover:bg-green-100'
                          : project.status === 'completed'
                            ? 'bg-blue-100 text-blue-800 hover:bg-blue-100'
                            : 'bg-neutral-100 text-neutral-800 hover:bg-neutral-100'
                      }
                    >
                      {project.status.charAt(0).toUpperCase() +
                        project.status.slice(1)}
                    </Badge>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Priority</h3>
                    <Badge
                      className={
                        priority === 'High'
                          ? 'bg-red-100 text-red-800 hover:bg-red-100'
                          : priority === 'Medium'
                            ? 'bg-amber-100 text-amber-800 hover:bg-amber-100'
                            : 'bg-green-100 text-green-800 hover:bg-green-100'
                      }
                    >
                      {priority} Priority
                    </Badge>
                    <p className="text-sm text-neutral-500 mt-1">
                      {daysRemaining > 0
                        ? `${daysRemaining} days remaining`
                        : 'Deadline passed'}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">
                      Activity Summary
                    </h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-neutral-50 p-3 rounded-md text-center">
                        <div className="text-xl font-semibold">
                          {tasks.length}
                        </div>
                        <div className="text-xs text-neutral-500">Tasks</div>
                      </div>
                      <div className="bg-neutral-50 p-3 rounded-md text-center">
                        <div className="text-xl font-semibold">
                          {comments.length}
                        </div>
                        <div className="text-xs text-neutral-500">Comments</div>
                      </div>
                      <div className="bg-neutral-50 p-3 rounded-md text-center">
                        <div className="text-xl font-semibold">
                          {documents.length}
                        </div>
                        <div className="text-xs text-neutral-500">
                          Documents
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tasks">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Tasks</CardTitle>
                    <CardDescription>
                      Manage project tasks and track progress
                    </CardDescription>
                  </div>
                  <Button
                    className="gap-1"
                    onClick={() => {
                      const title = prompt('Enter task title:');
                      if (title) {
                        handleCreateTask({
                          title,
                          description:
                            prompt('Enter task description (optional):') || '',
                          status: 'todo',
                          priority: 'medium',
                        });
                      }
                    }}
                  >
                    <Plus className="size-4" />
                    <span>Add Task</span>
                  </Button>
                </CardHeader>
                <CardContent>
                  {tasks.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500">
                      No tasks created yet
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {tasks.map((task) => (
                        <div
                          key={task.id}
                          className="border rounded-md p-4 hover:bg-neutral-50"
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex items-start gap-3">
                              <div
                                className={`mt-1 size-5 rounded-full flex items-center justify-center ${
                                  task.status === 'done'
                                    ? 'bg-green-100 text-green-600'
                                    : 'bg-neutral-100 text-neutral-400'
                                }`}
                              >
                                {task.status === 'done' && (
                                  <CheckCircle2 className="size-4" />
                                )}
                              </div>
                              <div>
                                <h3 className="font-medium">{task.title}</h3>
                                {task.description && (
                                  <p className="text-sm text-neutral-600 mt-1">
                                    {task.description}
                                  </p>
                                )}
                                <div className="flex flex-wrap gap-2 mt-2">
                                  <Badge
                                    variant="outline"
                                    className={
                                      task.status === 'todo'
                                        ? 'border-neutral-200 text-neutral-700'
                                        : task.status === 'in_progress'
                                          ? 'border-blue-200 text-blue-700'
                                          : task.status === 'review'
                                            ? 'border-amber-200 text-amber-700'
                                            : 'border-green-200 text-green-700'
                                    }
                                  >
                                    {task.status
                                      .split('_')
                                      .map(
                                        (word) =>
                                          word.charAt(0).toUpperCase() +
                                          word.slice(1)
                                      )
                                      .join(' ')}
                                  </Badge>
                                  <Badge
                                    variant="outline"
                                    className={
                                      task.priority === 'urgent'
                                        ? 'border-red-200 text-red-700'
                                        : task.priority === 'high'
                                          ? 'border-orange-200 text-orange-700'
                                          : task.priority === 'medium'
                                            ? 'border-amber-200 text-amber-700'
                                            : 'border-green-200 text-green-700'
                                    }
                                  >
                                    {task.priority.charAt(0).toUpperCase() +
                                      task.priority.slice(1)}{' '}
                                    Priority
                                  </Badge>
                                  {task.dueDate && (
                                    <Badge variant="outline">
                                      <Clock className="mr-1 size-3" />
                                      Due:{' '}
                                      {new Date(
                                        task.dueDate
                                      ).toLocaleDateString()}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="rounded-full"
                            >
                              <MoreHorizontal className="size-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Documents</CardTitle>
                    <CardDescription>
                      Project documents and files
                    </CardDescription>
                  </div>
                  <Button
                    className="gap-1"
                    onClick={() => {
                      toast.info('This feature is coming soon!', {
                        description:
                          'Document upload functionality will be available in the next update.',
                      });
                    }}
                  >
                    <Plus className="size-4" />
                    <span>Add Document</span>
                  </Button>
                </CardHeader>
                <CardContent>
                  {documents.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500">
                      No documents added yet
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {documents.map((doc) => (
                        <div
                          key={doc.id}
                          className="border rounded-md p-4 hover:bg-neutral-50"
                        >
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-3">
                              <div className="bg-neutral-100 p-2 rounded">
                                <FileText className="size-5 text-neutral-600" />
                              </div>
                              <div>
                                <h3 className="font-medium">{doc.title}</h3>
                                <p className="text-xs text-neutral-500">
                                  Added by {doc.addedBy} • {doc.addedAt}
                                </p>
                              </div>
                            </div>
                            <Badge variant="outline">{doc.type}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comments">
              <Card>
                <CardHeader>
                  <CardTitle>Comments</CardTitle>
                  <CardDescription>
                    Project discussions and updates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {comments.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500">
                      No comments yet
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {comments.map((comment) => (
                        <div key={comment.id} className="border rounded-md p-4">
                          <div className="flex items-start gap-3">
                            <Image
                              src={avatar_male_001}
                              alt={comment.userName}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium">
                                  {comment.userName}
                                </h3>
                                <span className="text-xs text-neutral-500">
                                  {comment.createdAt}
                                </span>
                              </div>
                              <p className="text-neutral-700 mt-1">
                                {comment.content}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
                <CardFooter className="border-t p-4">
                  <div className="flex gap-3 w-full">
                    <Image
                      src={avatar_male_001}
                      alt="You"
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                    <div className="flex-1">
                      <textarea
                        className="w-full border rounded-md p-2 text-sm resize-none"
                        placeholder="Add a comment..."
                        rows={2}
                        id="comment-input"
                      ></textarea>
                      <div className="flex justify-end mt-2">
                        <Button onClick={handleAddComment}>Post Comment</Button>
                      </div>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>People working on this project</CardDescription>
            </CardHeader>
            <CardContent>
              {members.length === 0 ? (
                <div className="text-center py-4 text-neutral-500">
                  No team members yet
                </div>
              ) : (
                <div className="space-y-4">
                  {members.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <Image
                          src={avatar_male_001}
                          alt={member.fullName || member.email}
                          width={36}
                          height={36}
                          className="rounded-full"
                        />
                        <div>
                          <h3 className="font-medium">
                            {member.fullName || member.email.split('@')[0]}
                          </h3>
                          <p className="text-xs text-neutral-500">
                            {member.role.charAt(0).toUpperCase() +
                              member.role.slice(1)}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="size-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="border-t pt-4">
              <Button variant="outline" className="w-full gap-1">
                <Plus className="size-4" />
                <span>Add Member</span>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">Task Status</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-neutral-50 p-2 rounded-md text-center">
                      <div className="text-lg font-semibold">
                        {tasks.filter((t) => t.status === 'todo').length}
                      </div>
                      <div className="text-xs text-neutral-500">To Do</div>
                    </div>
                    <div className="bg-neutral-50 p-2 rounded-md text-center">
                      <div className="text-lg font-semibold">
                        {tasks.filter((t) => t.status === 'in_progress').length}
                      </div>
                      <div className="text-xs text-neutral-500">
                        In Progress
                      </div>
                    </div>
                    <div className="bg-neutral-50 p-2 rounded-md text-center">
                      <div className="text-lg font-semibold">
                        {tasks.filter((t) => t.status === 'review').length}
                      </div>
                      <div className="text-xs text-neutral-500">In Review</div>
                    </div>
                    <div className="bg-neutral-50 p-2 rounded-md text-center">
                      <div className="text-lg font-semibold">
                        {tasks.filter((t) => t.status === 'done').length}
                      </div>
                      <div className="text-xs text-neutral-500">Completed</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-1">Recent Activity</h3>
                  <div className="text-sm text-neutral-600">
                    <div className="flex items-center gap-2 py-1">
                      <div className="size-2 rounded-full bg-green-500"></div>
                      <span>{completedTasks} tasks completed</span>
                    </div>
                    <div className="flex items-center gap-2 py-1">
                      <div className="size-2 rounded-full bg-blue-500"></div>
                      <span>{comments.length} comments added</span>
                    </div>
                    <div className="flex items-center gap-2 py-1">
                      <div className="size-2 rounded-full bg-purple-500"></div>
                      <span>{documents.length} documents uploaded</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
