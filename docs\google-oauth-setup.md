# Setting Up Google OAuth for NotAMess Forms

This guide will help you set up Google OAuth for your Supabase project to enable Google sign-in.

## Step 1: Create OAuth Credentials in Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" and select "OAuth client ID"
5. Select "Web application" as the application type
6. Add a name for your OAuth client
7. Add the following Authorized JavaScript origins:
   - `https://pknfwucukdrmekrsvuni.supabase.co` (your Supabase project URL)
   - `http://localhost:3000` (for local development)
8. Add the following Authorized redirect URIs:
   - `https://pknfwucukdrmekrsvuni.supabase.co/auth/v1/callback`
   - `http://localhost:3000/auth/v1/callback` (for local development)
9. Click "Create"
10. Note your Client ID and Client Secret

## Step 2: Configure Supabase Auth Settings

1. Go to your [Supabase Dashboard](https://app.supabase.com/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list and click "Edit"
5. Enable the provider by toggling the switch
6. Enter your Google Client ID and Client Secret from Step 1
7. Save the changes

## Step 3: Configure Redirect URLs

1. In your Supabase Dashboard, go to "Authentication" > "URL Configuration"
2. Set the Site URL to your production URL (e.g., `https://your-domain.com`)
3. Add the following Redirect URLs:
   - `https://your-domain.com/auth/callback`
   - `https://your-domain.com/auth/redirect`
   - `http://localhost:3000/auth/callback` (for local development)
   - `http://localhost:3000/auth/redirect` (for local development)
4. Save the changes

## Testing

1. Make sure your application is running
2. Try signing in with Google
3. You should be redirected back to your application after successful authentication

## Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify that your Client ID and Client Secret are correct
3. Ensure all redirect URLs are properly configured in both Google Cloud Console and Supabase
4. Check that your application is using the correct Supabase URL and API keys
