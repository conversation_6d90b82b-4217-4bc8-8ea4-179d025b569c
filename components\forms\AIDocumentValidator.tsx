import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import { AIService } from '@/lib/services/AIService';
import { <PERSON><PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'critical' | 'warning' | 'info';
}

export interface ValidationResult {
  ruleId: string;
  passed: boolean;
  message: string;
  suggestions: string[];
}

export interface DocumentValidationResults {
  timestamp: Date;
  overallStatus: 'valid' | 'warning' | 'invalid';
  results: ValidationResult[];
  suggestedFixes: string[];
}

interface AIDocumentValidatorProps {
  documentContent: string;
  validationRules: ValidationRule[];
  onValidationComplete: (results: DocumentValidationResults) => void;
}

export function AIDocumentValidator({
  documentContent,
  validationRules,
  onValidationComplete,
}: AIDocumentValidatorProps) {
  const [validationResults, setValidationResults] =
    useState<DocumentValidationResults | null>(null);
  const [error, setError] = useState<string | null>(null);

  const aiService = AIService.getInstance();

  const validateDocument = async () => {
    setError(null);

    // Create a promise for the validation process
    const validationPromise = (async () => {
      // First, analyze the document
      const analysis = await aiService.analyzeDocument(documentContent, 'all');

      // Then, build validation results based on analysis and rules
      const results: ValidationResult[] = [];

      // This would typically be a more sophisticated AI-driven validation
      // For now, we'll simulate the validation using the document analysis
      validationRules.forEach((rule) => {
        // Check if any risks in the analysis match our rule
        const matchingRisks = analysis.risks.filter((risk) =>
          risk.description.toLowerCase().includes(rule.name.toLowerCase())
        );

        if (matchingRisks.length > 0) {
          // Rule failed
          results.push({
            ruleId: rule.id,
            passed: false,
            message: matchingRisks[0].description,
            suggestions: analysis.suggestions.filter((suggestion) =>
              suggestion.toLowerCase().includes(rule.name.toLowerCase())
            ),
          });
        } else {
          // Rule passed
          results.push({
            ruleId: rule.id,
            passed: true,
            message: `No issues found related to ${rule.name}`,
            suggestions: [],
          });
        }
      });

      // Determine overall status
      const hasCriticalFailures = validationRules
        .filter((rule) => rule.severity === 'critical')
        .some((rule) =>
          results.find((result) => result.ruleId === rule.id && !result.passed)
        );

      const hasWarnings = validationRules
        .filter((rule) => rule.severity === 'warning')
        .some((rule) =>
          results.find((result) => result.ruleId === rule.id && !result.passed)
        );

      const overallStatus = hasCriticalFailures
        ? 'invalid'
        : hasWarnings
          ? 'warning'
          : 'valid';

      const validationResults: DocumentValidationResults = {
        timestamp: new Date(),
        overallStatus,
        results,
        suggestedFixes: analysis.suggestions,
      };

      return validationResults;
    })();

    // Use toast.promise to handle loading, success, and error states
    toast.promise(validationPromise, {
      loading: 'Validating document...',
      success: (results) => {
        setValidationResults(results);
        onValidationComplete(results);
        return `Validation complete: ${results.overallStatus === 'valid' ? 'Document is valid' : results.overallStatus === 'warning' ? 'Document has warnings' : 'Document has critical issues'}`;
      },
      error: (err) => {
        console.error('Error validating document:', err);
        setError('Failed to validate document');
        return 'Failed to validate document';
      },
    });
  };

  useEffect(() => {
    if (documentContent && validationRules.length > 0) {
      validateDocument();
    }
  }, [documentContent, validationRules]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          Smart Document Validation
        </CardTitle>
        <CardDescription>
          AI-powered document validation based on predefined rules
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="p-4 border rounded-md bg-destructive/10 text-destructive">
            {error}
          </div>
        ) : validationResults ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 rounded-md border bg-muted/30">
              {validationResults.overallStatus === 'valid' ? (
                <CheckCircle2 className="h-5 w-5 text-green-500" />
              ) : validationResults.overallStatus === 'warning' ? (
                <ShieldAlert className="h-5 w-5 text-yellow-500" />
              ) : (
                <ShieldAlert className="h-5 w-5 text-red-500" />
              )}
              <div>
                <div className="font-medium">
                  {validationResults.overallStatus === 'valid'
                    ? 'Document Valid'
                    : validationResults.overallStatus === 'warning'
                      ? 'Document Has Warnings'
                      : 'Document Invalid'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {validationResults.overallStatus === 'valid'
                    ? 'All validation rules passed'
                    : validationResults.overallStatus === 'warning'
                      ? 'Some non-critical rules failed'
                      : 'Critical validation rules failed'}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Validation Results</h3>
              {validationResults.results.map((result, index) => {
                const rule = validationRules.find(
                  (r) => r.id === result.ruleId
                );
                return (
                  <div
                    key={index}
                    className={`p-3 rounded-md border ${
                      result.passed
                        ? 'bg-green-500/10'
                        : rule?.severity === 'critical'
                          ? 'bg-red-500/10'
                          : rule?.severity === 'warning'
                            ? 'bg-yellow-500/10'
                            : 'bg-blue-500/10'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{rule?.name}</div>
                      <div
                        className={`text-xs ${
                          result.passed
                            ? 'text-green-500'
                            : rule?.severity === 'critical'
                              ? 'text-red-500'
                              : rule?.severity === 'warning'
                                ? 'text-yellow-500'
                                : 'text-blue-500'
                        }`}
                      >
                        {result.passed ? 'PASSED' : 'FAILED'}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {result.message}
                    </div>
                    {!result.passed && result.suggestions.length > 0 && (
                      <div className="mt-2">
                        <div className="text-xs font-medium mb-1">
                          Suggestions:
                        </div>
                        <ul className="text-xs list-disc list-inside">
                          {result.suggestions.map((suggestion, i) => (
                            <li key={i}>{suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {validationResults.suggestedFixes.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Suggested Improvements</h3>
                <div className="p-3 rounded-md border bg-muted/30">
                  <ul className="text-sm list-disc list-inside">
                    {validationResults.suggestedFixes.map((fix, index) => (
                      <li key={index}>{fix}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-6 text-muted-foreground">
            No validation results available
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={validateDocument} className="w-full">
          Revalidate Document
        </Button>
      </CardFooter>
    </Card>
  );
}
