'use client';
import { motion } from 'motion/react';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';

const formSateData = [
  {
    id: 2,
    title: 'Create Account',
    slug: 'create-account',
    path: '/create-account',
  },
  {
    id: 1,
    title: 'Log In',
    slug: 'login',
    path: '/login',
  },

  // { slug: "forgetPassword", title: "Forget Password", id: 3 },
];

export function AuthPageToggle() {
  const router = useRouter();
  const pathname = usePathname();

  const [formState, setFormState] = useState('login');

  function handleFormStateChange(e: any) {
    const packageName = e.target.value;
    router.push(`/${packageName}`);
  }

  return (
    <div className="">
      <div className="flex w-fit items-center rounded-2xl bg-gray-200/40 border border-gray-200 p-2">
        {formSateData.map((s) => (
          <label key={s.id} className={`relative z-0 px-4 py-2 font-medium`}>
            {/* //this input is hidden cos there is no appearance for it */}
            <input
              type="radio"
              name="RatecardPackages"
              value={s.slug}
              checked={pathname == s.path}
              onChange={handleFormStateChange}
              onClick={handleFormStateChange}
              className="absolute -left-[999px] appearance-none"
            />
            <div data-text={s.slug} className="cursor-pointer">
              {pathname == s.path && (
                <motion.div
                  layoutId="formStateId"
                  style={{
                    borderRadius: 12,
                  }}
                  className="absolute inset-0 -z-10 bg-white shadow-sm"
                />
              )}
              <span
                className={`${
                  pathname == s.path ? 'text-black' : 'text-gray-500'
                }`}
              >
                {s.title}
              </span>
            </div>
          </label>
        ))}
      </div>
    </div>
  );
}
