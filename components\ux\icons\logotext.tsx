import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { cn } from '@/lib/utils';
type props = {
  className?: string;
};
export function LogoWithNotamess({ className }: props) {
  return (
    <span
      className={cn(
        FONT_BIRCOLAGE_GROTESQUE.className,
        'text-accent-100 font-bold',
        className
      )}
    >
      Not<span className="text-accent-10">a</span>mess Forms
    </span>
  );
}
export function LogoNotamess({ className }: props) {
  return (
    <span
      className={cn(
        FONT_BIRCOLAGE_GROTESQUE.className,
        'text-accent-100 font-bold',
        className
      )}
    >
      not<span className="text-accent-10">a</span>mess
    </span>
  );
}
export function NotamessLogo({ className }: props) {
  return (
    <span
      className={cn(
        FONT_BIRCOLAGE_GROTESQUE.className,
        'text-notamess-100 font-bold',
        className
      )}
    >
      not<span className="text-notamess-10">a</span>mess
    </span>
  );
}
export function LogoText({ className }: props) {
  return (
    <span
      className={cn(
        FONT_BIRCOLAGE_GROTESQUE.className,
        'text-accent-100 font-bold',
        className
      )}
    >
      Forms
    </span>
  );
}
