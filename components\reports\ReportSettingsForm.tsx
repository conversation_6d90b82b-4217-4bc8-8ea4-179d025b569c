'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ReportSettings,
  ReportFrequency,
  MetricType,
} from '@/lib/types/database-modules';
import { Loader2, Mail, Save } from 'lucide-react';
import { toast } from 'sonner';

interface ReportSettingsFormProps {
  settings: ReportSettings[];
  loading?: boolean;
  onUpdateSettings: (
    reportType: ReportFrequency,
    isEnabled: boolean,
    emailRecipients: string[],
    includeMetrics: MetricType[]
  ) => Promise<boolean>;
}

export function ReportSettingsForm({
  settings,
  loading = false,
  onUpdateSettings,
}: ReportSettingsFormProps) {
  const [activeTab, setActiveTab] = useState<ReportFrequency>('weekly');
  const [isEnabled, setIsEnabled] = useState(true);
  const [emailRecipients, setEmailRecipients] = useState<string[]>([]);
  const [newEmail, setNewEmail] = useState('');
  const [selectedMetrics, setSelectedMetrics] = useState<MetricType[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  // Available metrics
  const availableMetrics: { value: MetricType; label: string }[] = [
    { value: 'total_consultations', label: 'Total Consultations' },
    { value: 'completed_consultations', label: 'Completed Consultations' },
    { value: 'cancelled_consultations', label: 'Cancelled Consultations' },
    { value: 'upcoming_consultations', label: 'Upcoming Consultations' },
    { value: 'document_consultations', label: 'Document Reviews' },
    { value: 'video_consultations', label: 'Video Consultations' },
    { value: 'recurring_consultations', label: 'Recurring Consultations' },
    { value: 'total_minutes', label: 'Total Minutes' },
    { value: 'avg_duration_minutes', label: 'Average Duration' },
    { value: 'completion_rate', label: 'Completion Rate' },
    { value: 'cancellation_rate', label: 'Cancellation Rate' },
  ];

  // Load settings when tab changes
  useEffect(() => {
    const setting = settings.find((s) => s.report_type === activeTab);

    if (setting) {
      setIsEnabled(setting.is_enabled ?? true);
      setEmailRecipients(setting.email_recipients || []);
      setSelectedMetrics((setting.include_metrics || []) as MetricType[]);
    } else {
      // Default values
      setIsEnabled(true);
      setEmailRecipients([]);
      setSelectedMetrics([
        'total_consultations',
        'completed_consultations',
        'cancelled_consultations',
        'upcoming_consultations',
      ]);
    }
  }, [settings, activeTab]);

  // Handle add email
  const handleAddEmail = () => {
    if (!newEmail.trim() || !isValidEmail(newEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (emailRecipients.includes(newEmail)) {
      toast.error('Email already added');
      return;
    }

    setEmailRecipients([...emailRecipients, newEmail]);
    setNewEmail('');
  };

  // Handle remove email
  const handleRemoveEmail = (email: string) => {
    setEmailRecipients(emailRecipients.filter((e) => e !== email));
  };

  // Handle metric toggle
  const handleMetricToggle = (metric: MetricType, checked: boolean) => {
    if (checked) {
      setSelectedMetrics([...selectedMetrics, metric]);
    } else {
      setSelectedMetrics(selectedMetrics.filter((m) => m !== metric));
    }
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    await onUpdateSettings(
      activeTab,
      isEnabled,
      emailRecipients,
      selectedMetrics
    );
    setIsSaving(false);
  };

  // Validate email
  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-full" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-10 w-full mb-4" />
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-8 w-full" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Report Settings</CardTitle>
        <CardDescription>
          Configure your automated report settings. Reports will be sent to the
          specified email addresses.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as ReportFrequency)}
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="weekly">Weekly</TabsTrigger>
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="quarterly">Quarterly</TabsTrigger>
          </TabsList>

          <TabsContent value="weekly" className="space-y-4 pt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="weekly-enabled">Enable Weekly Reports</Label>
              <Switch
                id="weekly-enabled"
                checked={isEnabled}
                onCheckedChange={setIsEnabled}
              />
            </div>
          </TabsContent>

          <TabsContent value="monthly" className="space-y-4 pt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="monthly-enabled">Enable Monthly Reports</Label>
              <Switch
                id="monthly-enabled"
                checked={isEnabled}
                onCheckedChange={setIsEnabled}
              />
            </div>
          </TabsContent>

          <TabsContent value="quarterly" className="space-y-4 pt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="quarterly-enabled">
                Enable Quarterly Reports
              </Label>
              <Switch
                id="quarterly-enabled"
                checked={isEnabled}
                onCheckedChange={setIsEnabled}
              />
            </div>
          </TabsContent>
        </Tabs>

        <div className="space-y-4">
          <Label>Email Recipients</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter email address"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddEmail();
                }
              }}
            />
            <Button
              variant="outline"
              onClick={handleAddEmail}
              disabled={!newEmail.trim() || !isValidEmail(newEmail)}
            >
              Add
            </Button>
          </div>

          <div className="space-y-2">
            {emailRecipients.length === 0 ? (
              <p className="text-sm text-muted-foreground">
                No recipients added
              </p>
            ) : (
              emailRecipients.map((email, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-muted rounded"
                >
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{email}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    Remove
                  </Button>
                </div>
              ))
            )}
          </div>
        </div>

        <div className="space-y-4">
          <Label>Include Metrics</Label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {availableMetrics.map((metric) => (
              <div key={metric.value} className="flex items-center space-x-2">
                <Checkbox
                  id={metric.value}
                  checked={selectedMetrics.includes(metric.value)}
                  onCheckedChange={(checked) =>
                    handleMetricToggle(metric.value, !!checked)
                  }
                />
                <Label htmlFor={metric.value}>{metric.label}</Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSaveSettings} disabled={isSaving}>
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
