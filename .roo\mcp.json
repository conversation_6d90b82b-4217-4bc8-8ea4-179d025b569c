{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "alwaysAllow": ["list_organizations", "get_organization", "list_projects", "get_project", "get_cost", "confirm_cost", "create_project", "list_tables", "list_extensions", "list_migrations", "apply_migration", "execute_sql", "list_edge_functions", "deploy_edge_function", "get_logs", "get_project_url", "get_anon_key", "generate_typescript_types", "create_branch", "list_branches", "delete_branch", "rebase_branch", "reset_branch", "merge_branch"]}}}