# NotAMess Forms System Patterns

## Architecture Overview

```mermaid
flowchart TD
    Client[Next.js Client] --> Router[App Router]
    Router --> API[API Routes]
    Router --> Pages[Pages]
    API --> Auth[Supabase Auth]
    API --> DB[Supabase DB]

    Pages --> Components[Components]
    Components --> UI[UI Components]
    Components --> Forms[Form System]
    Components --> Layout[Layouts]

    Pages --> Dashboard[Dashboard Pages]
    Dashboard --> Home[Home]
    Dashboard --> DocCreator[Document Creator]
    Dashboard --> SmartContracts[Smart Contracts]
    Dashboard --> LawyerSupport[Lawyer Support]
    Dashboard --> CollabHub[Collaboration Hub]
    Dashboard --> Settings[Settings]
```

## Core Technical Decisions

### 1. Frontend Architecture

- Next.js 13+ with App Router
- React for component architecture
- TailwindCSS for styling
- Shadcn/UI component library
- Motion/React for animations

### 2. Backend Services

- Supabase for database and authentication

### 3. State Management

- React Context for global state
- Form-specific context providers
- Zustand for complex state management

## Design Patterns

### 1. Component Patterns

```mermaid
flowchart TD
    Base[Base Components] --> UI[UI Components]
    UI --> Forms[Form Components]
    UI --> Layout[Layout Components]
    UI --> Animated[Animated Components]
    UI --> Settings[Settings Components]
```

- Atomic Design methodology
- Component composition
- Render props pattern
- HOC for enhanced functionality
- Animation composition pattern

### 2. Form System Patterns

```mermaid
flowchart LR
    Template[Template] --> Schema[Schema]
    Schema --> Validation[Validation]
    Validation --> State[Form State]
    State --> UI[UI Rendering]
```

- Template-based form generation
- Schema-driven validation
- State management patterns
- Dynamic UI rendering

### 3. Authentication Patterns

```mermaid
flowchart TD
    Auth[Auth Provider] --> Session[Session Management]
    Session --> RBAC[Role-Based Access]
    RBAC --> Gates[Permission Gates]
```

- JWT-based authentication
- Role-based access control
- Permission gates
- Session management

### 4. Animation Patterns

```mermaid
flowchart LR
    Component[Base Component] --> AnimatedWrapper[AnimationPresence Wrapper]
    AnimatedWrapper --> MotionComponent[Motion Component]
    MotionComponent --> AnimationProps[Animation Props]
    AnimationProps --> AnimationState[Animation State]
```

- Component-level animation encapsulation
- Direction-aware transitions
- Blur and transform combined effects
- AnimatePresence for exit animations
- State-driven animation properties

### 5. Dashboard Navigation Patterns

```mermaid
flowchart TD
    Dashboard[Dashboard Layout] --> Navigation[Navigation Component]
    Navigation --> MainPages[Main Page Sections]
    Navigation --> SubPages[Sub-Page Sections]

    MainPages --> Home[Home]
    MainPages --> DocCreator[Document Creator]
    MainPages --> SmartContracts[Smart Contracts]
    MainPages --> LawyerSupport[Lawyer Support]
    MainPages --> CollabHub[Collaboration Hub]
    MainPages --> Settings[Settings]

    SubPages --> Specific[Page-Specific Content]
    Specific --> AnimatedContent[Animated Content]
```

- Hierarchical navigation structure
- Main and sub-page organization
- Consistent layout patterns
- Animated page transitions
- Context-aware navigation
- Responsive adaptation

## Component Relationships

### 1. Form Components

```typescript
interface FormTemplate {
  id: string;
  schema: JSONSchema;
  validation: ValidationRules;
  ui: UISchema;
  handlers: FormHandlers;
}

interface FormContext {
  state: FormState;
  dispatch: FormDispatch;
  validation: ValidationState;
  handlers: FormHandlers;
}
```

### 2. Authentication Components

```typescript
interface AuthContext {
  user: User | null;
  session: Session | null;
  roles: Role[];
  permissions: Permission[];
}

interface PermissionGate {
  requires: Permission[];
  children: React.ReactNode;
}
```

### 3. Layout Components

```typescript
interface LayoutProps {
  navigation: NavigationProps;
  sidebar?: SidebarProps;
  footer?: FooterProps;
  children: React.ReactNode;
}
```

### 4. Animation Components

```typescript
// Sidebar Animation Pattern
interface AnimatedContentProps {
  direction?: number; // Controls the animation direction
  children: React.ReactNode;
}

// Motion component animation properties
const motionProps = {
  initial: {
    opacity: 0,
    filter: 'blur(4px)',
    x: direction * 20,
  },
  animate: {
    opacity: 1,
    filter: 'blur(0px)',
    x: 0,
  },
  exit: {
    opacity: 0,
    filter: 'blur(4px)',
    x: -20,
  },
  transition: {
    duration: 0.2,
    ease: 'easeInOut',
  },
};
```

### 5. Dashboard Navigation Components

```typescript
// Main dashboard navigation structure
interface DashboardNavigationProps {
  activePage: string;
  activeSubPage?: string;
  onNavigate: (page: string, subPage?: string) => void;
}

// Settings page components
interface SettingsPageProps {
  section:
    | 'account'
    | 'subscription'
    | 'security'
    | 'notifications'
    | 'preferences';
  user: User;
}

// Account section interface
interface AccountSettingsProps {
  user: User;
  billingHistory: BillingRecord[];
  onUpdateUser: (data: Partial<User>) => Promise<void>;
}

// Subscription section interface
interface SubscriptionSettingsProps {
  currentPlan: SubscriptionPlan;
  usage: UsageMetrics;
  availablePlans: SubscriptionPlan[];
  onChangePlan: (planId: string) => Promise<void>;
}
```

## Data Flow Patterns

### 1. Form Data Flow

```mermaid
flowchart LR
    Input[User Input] --> Validation[Validation]
    Validation --> State[Form State]
    State --> UI[UI Update]
    State --> Storage[Storage]
```

### 2. Authentication Flow

```mermaid
flowchart TD
    Login[Login] --> Token[JWT Token]
    Token --> Session[Session]
    Session --> Roles[Roles]
    Roles --> Permissions[Permissions]
```

### 3. Document Flow

```mermaid
flowchart LR
    Create[Creation] --> Review[Expert Review]
    Review --> Storage[Storage]
```

### 4. Animation Flow

```mermaid
flowchart TD
    StateChange[State Change] --> AnimationTrigger[Animation Trigger]
    AnimationTrigger --> AnimatePresence[AnimatePresence]
    AnimatePresence --> Enter[Enter Animation]
    AnimatePresence --> Exit[Exit Animation]
    Enter --> Rendered[Rendered Component]
    Exit --> Removed[Removed from DOM]
```

### 5. Dashboard Navigation Flow

```mermaid
flowchart TD
    SelectMain[Select Main Page] --> UpdateURL[Update URL]
    UpdateURL --> LoadContent[Load Content]
    LoadContent --> AnimateExit[Animate Exit]
    LoadContent --> AnimateEnter[Animate Enter]
    SelectMain --> SelectSub[Select Sub-Page]
    SelectSub --> UpdateSubURL[Update Sub-URL]
    UpdateSubURL --> LoadSubContent[Load Sub-Content]
    LoadSubContent --> AnimateSubExit[Animate Sub-Exit]
    LoadSubContent --> AnimateSubEnter[Animate Sub-Enter]
```

## UI Component Structure

### 1. Navigation System

```mermaid
flowchart TD
    AppSidebar[AppSidebar] --> SidebarProvider[SidebarProvider]
    SidebarProvider --> SidebarContent[SidebarContent]
    SidebarContent --> AnimatePresence[AnimatePresence]
    AnimatePresence --> DashboardContent[DashboardContent]
    AnimatePresence --> SettingsContent[SettingsContent]
    DashboardContent --> MotionWrapper[Motion Wrapper]
    SettingsContent --> MotionWrapper
```

### 2. Dropdown System

```mermaid
flowchart TD
    DropdownMenu[DropdownMenu] --> Portal[Portal]
    Portal --> AnimatePresence[AnimatePresence]
    AnimatePresence --> Content[DropdownContent]
    Content --> MotionDiv[MotionDiv with Animations]
    MotionDiv --> Items[Menu Items]
```

### 3. Dashboard Hierarchy

```mermaid
flowchart TD
    Dashboard[Dashboard Layout] --> Sidebar[Sidebar]
    Dashboard --> MainContent[Main Content Area]

    Sidebar --> MainNav[Main Navigation]
    MainNav --> HomeNav[Home]
    MainNav --> DocNav[Document Creator]
    MainNav --> ContractNav[Smart Contracts]
    MainNav --> LawyerNav[Lawyer Support]
    MainNav --> CollabNav[Collaboration Hub]
    MainNav --> SettingsNav[Settings]

    MainContent --> ContentHeader[Page Header]
    MainContent --> SubNavigation[Sub-Navigation]
    MainContent --> PageContent[Page Content]

    SubNavigation --> SubPages[Sub-Pages]
    PageContent --> AnimatedWrapper[Animated Content Wrapper]
```

### 4. Settings Page Structure

```mermaid
flowchart TD
    Settings[Settings Page] --> SettingsNav[Settings Navigation]
    Settings --> SettingsContent[Settings Content]

    SettingsNav --> AccountNav[Account]
    SettingsNav --> SubscriptionNav[Subscription]
    SettingsNav --> SecurityNav[Security]
    SettingsNav --> NotificationsNav[Notifications]
    SettingsNav --> PreferencesNav[Preferences]

    SettingsContent --> AccountContent[Account Settings]
    SettingsContent --> SubscriptionContent[Subscription Settings]
    SettingsContent --> SecurityContent[Security Settings]
    SettingsContent --> NotificationsContent[Notification Settings]
    SettingsContent --> PreferencesContent[Preferences Settings]

    AccountContent --> PersonalInfo[Personal Information]
    AccountContent --> BillingInfo[Billing Information]

    SubscriptionContent --> CurrentPlan[Current Plan]
    SubscriptionContent --> UsageMetrics[Usage Metrics]
    SubscriptionContent --> PlanOptions[Plan Options]

    SecurityContent --> TwoFactorAuth[Two-Factor Auth]
    SecurityContent --> PasswordSettings[Password Settings]
    SecurityContent --> WalletConnection[Wallet Connection]

    NotificationsContent --> EmailNotifs[Email Notifications]
    NotificationsContent --> AppNotifs[In-App Notifications]
    NotificationsContent --> SMSNotifs[SMS Notifications]

    PreferencesContent --> ThemeSettings[Theme Settings]
    PreferencesContent --> ExportSettings[Export Settings]
    PreferencesContent --> LanguageSettings[Language Settings]
```

## Error Handling Patterns

1.  Global Error Boundary
2.  Form-level error handling
3.  API error handling
4.  Blockchain transaction error handling
5.  Animation fallback patterns

## Performance Patterns

1.  Code splitting
2.  Lazy loading
3.  Caching strategies
4.  Optimistic updates
5.  Animation optimization techniques:
    - will-change property
    - Hardware acceleration
    - Composable animations
    - Exit animation cleanup

## Security Patterns

1.  Input validation
2.  Output sanitization
3.  RBAC implementation
4.  Blockchain security
