-- This migration updates the Realtime Broadcast functionality to fix any issues
-- and ensure compatibility with the latest Supabase version

-- First, ensure the realtime extension is enabled
CREATE EXTENSION IF NOT EXISTS "pg_net";

-- Ensure the realtime schema exists
CREATE SCHEMA IF NOT EXISTS realtime;

-- Recreate the policy to allow authenticated users to receive broadcasts
DROP POLICY IF EXISTS "Authenticated users can receive broadcasts" ON realtime.messages;
CREATE POLICY "Authenticated users can receive broadcasts"
ON realtime.messages
FOR SELECT
TO authenticated
USING ( true );

-- Update the broadcast_document_changes function to handle NULL values properly
CREATE OR REPLACE FUNCTION public.broadcast_document_changes()
RETURNS trigger
AS $$
BEGIN
  -- Handle the case where new might be NULL (for DELETE operations)
  PERFORM realtime.broadcast_changes(
    CASE 
      WHEN TG_OP = 'DELETE' THEN 'document:' || old.id::text
      ELSE 'document:' || new.id::text
    END, -- topic
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    CASE 
      WHEN TG_OP = 'DELETE' THEN old
      ELSE new
    END, -- record
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Update the broadcast_notification_changes function to handle NULL values properly
CREATE OR REPLACE FUNCTION public.broadcast_notification_changes()
RETURNS trigger
AS $$
BEGIN
  -- Handle the case where new might be NULL (for DELETE operations)
  PERFORM realtime.broadcast_changes(
    CASE 
      WHEN TG_OP = 'DELETE' THEN 'notifications:' || old.user_id::text
      ELSE 'notifications:' || new.user_id::text
    END, -- topic
    TG_OP, -- event
    TG_OP, -- operation
    TG_TABLE_NAME, -- table
    TG_TABLE_SCHEMA, -- schema
    CASE 
      WHEN TG_OP = 'DELETE' THEN json_build_object(
        'id', old.id,
        'user_id', old.user_id,
        'title', old.title,
        'content', old.content,
        'type', old.type,
        'read', old.read,
        'action_url', old.action_url,
        'related_id', old.related_id,
        'related_type', old.related_type,
        'created_at', old.created_at
      )
      ELSE json_build_object(
        'id', new.id,
        'user_id', new.user_id,
        'title', new.title,
        'content', new.content,
        'type', new.type,
        'read', new.read,
        'action_url', new.action_url,
        'related_id', new.related_id,
        'related_type', new.related_type,
        'created_at', new.created_at
      )
    END, -- record (only sending necessary fields)
    CASE 
      WHEN TG_OP = 'UPDATE' OR TG_OP = 'DELETE' THEN old
      ELSE NULL
    END -- old record
  );
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a helper function to send direct messages to a channel
CREATE OR REPLACE FUNCTION public.send_realtime_message(
  topic TEXT,
  event TEXT,
  payload JSONB
)
RETURNS VOID
AS $$
BEGIN
  -- Use the realtime.send function to send a message to a channel
  PERFORM realtime.send(payload, event, topic, false);
END;
$$ LANGUAGE plpgsql;

-- Ensure all triggers are properly set up
-- Recreate the document changes trigger
DROP TRIGGER IF EXISTS on_document_changes ON public.documents;
CREATE TRIGGER on_document_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.documents
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_document_changes();

-- Recreate the notification changes trigger
DROP TRIGGER IF EXISTS on_notification_changes ON public.notifications;
CREATE TRIGGER on_notification_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.notifications
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_notification_changes();
