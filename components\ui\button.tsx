import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from 'lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center cursor-pointer *:font-geist-sans *:tracking-[-6%]  whitespace-nowrap rounded-full font-medium ring-offset-white transition-colors duration-300 ease-linner focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-20',
  {
    variants: {
      variant: {
        default: 'text-white shadow-dark transition-all duration-300 ',
        // shadows
        shadow_accent: 'text-white shadow-teal transition-all duration-300 ',
        shadow_red: 'text-red-900 shadow-default transition-all duration-300 ',
        shadow:
          'text-neutral-500 hover:text-black shadow-light transition-all duration-300 ',
        shadow_dark: 'text-white shadow-dark transition-all duration-300 ',
        shadow_blue: 'text-blue-900 shadow-blue transition-all duration-300 ',
        shadow_orange:
          'text-orange-900 shadow-orange transition-all duration-300 ',
        shadow_yellow:
          'text-amber-600 shadow-yellow transition-all duration-300 ',
        shadow_green:
          'text-green-900 shadow-green transition-all duration-300 ',
        shadow_purple:
          'text-purple-900 shadow-purple transition-all duration-300 ',
        shadow_teal: 'text-white shadow-teal transition-all duration-300 ',
        shadow_cyan: 'text-cyan-800 shadow-cyan transition-all duration-300 ',
        shadow_pink: 'text-white shadow-pink transition-all duration-300 ',
        // end
        outline:
          'border border-slate-200 bg-white hover:bg-slate-100 hover:text-slate-900',
        outlineTransparent:
          'border border-slate-200 bg-transparent shadow-xs hover:bg-gray-200/40 hover:text-slate-900',
        secondary: 'bg-slate-100 text-slate-900 hover:bg-slate-100/80',
        ghost: 'hover:text-slate-900',
        link: 'text-slate-900 underline-offset-4 hover:underline',
      },
      size: {
        default: 'px-4  h-10',
        sm: 'h-8  px-4 py-2',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
        ghost: 'h-10 px-2',
      },
      icon: {
        none: '',
        left: 'pr-4 pl-2 py-2 space-x-2',
        right: 'pr-2 pl-4 py-2 space-x-2',
        both: 'px-4 py-2 space-x-2',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      icon: 'none',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, icon, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, icon, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
