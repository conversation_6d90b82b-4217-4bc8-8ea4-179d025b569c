'use client';

import { buttonVariants } from '@/components/ui/button';
import { SleepUnderTree } from '@/components/ux/icons';
import { FONT_JETBRAINS_MONO } from '@/lib/constants/fonts';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function NotFound() {
  const pathname = usePathname();
  return (
    <main className="flex min-h-screen flex-col justify-between">
      <section className="flex flex-col items-center justify-center p-4 pt-14">
        <h2
          className={cn(FONT_JETBRAINS_MONO.className, 'text-alt-300 text-9xl')}
        >
          404
        </h2>
        <div className="-my-8">
          <SleepUnderTree className="text-alt-300 h-40 w-40" />
        </div>
        <p className="text-alt-300">Could not find requested resource</p>
        <p className={cn('my-2 px-4 py-2 text-red-500 underline')}>
          <span className="opacity-40"> https://forms.notamess.com</span>
          <span>{pathname}</span>
        </p>
        <div className="py-4 flex gap-4">
          <button
            onClick={() => window.history.back()}
            className={cn(buttonVariants({ variant: 'shadow_red' }))}
          >
            Go Back
          </button>
          <Link
            href={'/'}
            className={cn(buttonVariants({ variant: 'shadow' }))}
          >
            Return Home
          </Link>
        </div>
      </section>
    </main>
  );
}
