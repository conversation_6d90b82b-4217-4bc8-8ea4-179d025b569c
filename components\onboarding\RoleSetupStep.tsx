'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { userStore } from '@/lib/store/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { Briefcase, Loader2, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Using the supabaseClient imported from @/lib/supabase/client

// Define the role types
type UserRole = 'user' | 'lawyer';

// Define the plan types to match the database enum
type PlanType = 'Free' | 'Standard' | 'Enterprise';

// Define the lawyer profile schema
const lawyerProfileSchema = z.object({
  full_name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  specialization: z
    .array(z.string())
    .min(1, 'Select at least one specialization'),
  bio: z.string().min(10, 'Bio must be at least 10 characters'),
  years_experience: z.coerce.number().min(0, 'Experience cannot be negative'),
  hourly_rate: z.coerce.number().min(0, 'Hourly rate cannot be negative'),
  consultation_fee: z.coerce
    .number()
    .min(0, 'Consultation fee cannot be negative'),
  languages: z.array(z.string()).min(1, 'Select at least one language'),
});

type LawyerProfileFormValues = z.infer<typeof lawyerProfileSchema>;

interface RoleSetupStepProps {
  selectedPlan: PlanType;
  onComplete: (role: UserRole, lawyerProfile?: LawyerProfileFormValues) => void;
  profileBio?: string; // Optional bio from profile setup
  onRoleChange?: (role: UserRole) => Promise<void>;
  existingLawyerProfile?: LawyerProfileFormValues | null; // Existing lawyer profile data from the database
  isLoadingLawyerProfile?: boolean; // Loading state for lawyer profile data
}

export function RoleSetupStep({
  selectedPlan,
  onComplete,
  profileBio,
  onRoleChange,
  existingLawyerProfile,
  isLoadingLawyerProfile = false,
}: RoleSetupStepProps) {
  const { profile } = userStore(); // We don't need user here
  const [selectedRole, setSelectedRole] = useState<UserRole>('user');
  const [showLawyerForm, setShowLawyerForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if the selected plan allows lawyer registration
  const canBeLawyer = selectedPlan === 'Enterprise';

  // Initialize the lawyer profile form
  const form = useForm<LawyerProfileFormValues>({
    resolver: zodResolver(lawyerProfileSchema),
    defaultValues: {
      // Use existing lawyer profile data if available, otherwise use defaults
      full_name: existingLawyerProfile?.full_name || profile?.full_name || '',
      email: existingLawyerProfile?.email || profile?.email || '',
      specialization: existingLawyerProfile?.specialization || [],
      bio: existingLawyerProfile?.bio || profileBio || '', // Use the bio from profile setup if available
      years_experience: existingLawyerProfile?.years_experience || 0,
      hourly_rate: existingLawyerProfile?.hourly_rate || 0,
      consultation_fee: existingLawyerProfile?.consultation_fee || 0,
      languages: existingLawyerProfile?.languages || ['English'],
    },
  });

  // Update form when existingLawyerProfile changes
  useEffect(() => {
    if (existingLawyerProfile) {
      // If we have existing lawyer profile data, update the form
      form.reset({
        full_name: existingLawyerProfile.full_name || profile?.full_name || '',
        email: existingLawyerProfile.email || profile?.email || '',
        specialization: existingLawyerProfile.specialization || [],
        bio: existingLawyerProfile.bio || profileBio || '',
        years_experience: existingLawyerProfile.years_experience || 0,
        hourly_rate: existingLawyerProfile.hourly_rate || 0,
        consultation_fee: existingLawyerProfile.consultation_fee || 0,
        languages: existingLawyerProfile.languages || ['English'],
      });

      // If the user has a lawyer profile, pre-select the lawyer role
      if (profile?.user_role === 'lawyer') {
        setSelectedRole('lawyer');
        setShowLawyerForm(canBeLawyer);
      }
    }
  }, [existingLawyerProfile, profile, form, profileBio, canBeLawyer]);

  // Handle role selection
  const handleRoleSelect = async (role: UserRole) => {
    setSelectedRole(role);
    setShowLawyerForm(role === 'lawyer' && canBeLawyer);

    // If onRoleChange is provided, save the role to the database
    if (onRoleChange) {
      try {
        await onRoleChange(role);
      } catch (error) {
        console.error('Error saving role:', error);
      }
    }
  };

  // Handle form submission
  const onSubmit = async (data: LawyerProfileFormValues) => {
    setIsSubmitting(true);
    try {
      // Simulate a delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 500));
      onComplete(selectedRole, data);
    } catch (error) {
      console.error('Error submitting lawyer profile:', error);
      toast.error('Failed to complete setup');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle continue without lawyer profile
  const handleContinue = async () => {
    setIsSubmitting(true);
    try {
      // Simulate a delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 500));
      onComplete(selectedRole);
    } catch (error) {
      console.error('Error completing setup:', error);
      toast.error('Failed to complete setup');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define the specializations and languages options
  const specializations = [
    'Contract Law',
    'Corporate Law',
    'Intellectual Property',
    'Real Estate Law',
    'Family Law',
    'Criminal Law',
    'Immigration Law',
    'Tax Law',
    'Employment Law',
    'Environmental Law',
    'Bankruptcy Law',
    'Personal Injury',
    'Estate Planning',
  ];

  const languages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Chinese',
    'Japanese',
    'Korean',
    'Arabic',
    'Russian',
    'Portuguese',
    'Italian',
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">Choose Your Role</h1>
        <p className="text-neutral-500">
          Select how you'll be using NotAMess Forms.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card
          className={`cursor-pointer transition-all hover:border-primary ${selectedRole === 'user' ? 'border-2 border-primary' : 'border'}`}
          onClick={() => handleRoleSelect('user')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Standard User
            </CardTitle>
            <CardDescription>Create and manage legal documents</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              As a standard user, you can create, edit, and manage your legal
              documents. You'll have access to templates and document sharing
              features.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              variant={selectedRole === 'user' ? 'default' : 'outline'}
              className="w-full"
              onClick={() => handleRoleSelect('user')}
            >
              {selectedRole === 'user' ? 'Selected' : 'Select Role'}
            </Button>
          </CardFooter>
        </Card>

        <Card
          className={`cursor-pointer transition-all hover:border-primary ${!canBeLawyer ? 'opacity-60' : ''} ${selectedRole === 'lawyer' ? 'border-2 border-primary' : 'border'}`}
          onClick={() => canBeLawyer && handleRoleSelect('lawyer')}
        >
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Lawyer
            </CardTitle>
            <CardDescription>
              Provide legal services and consultations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              As a lawyer, you can offer consultations, review documents, and
              manage client relationships.
              {!canBeLawyer && (
                <span className="block mt-2 text-amber-600 font-medium">
                  Requires Enterprise plan
                </span>
              )}
            </p>
          </CardContent>
          <CardFooter>
            <Button
              variant={selectedRole === 'lawyer' ? 'default' : 'outline'}
              className="w-full"
              onClick={() => canBeLawyer && handleRoleSelect('lawyer')}
              disabled={!canBeLawyer}
            >
              {selectedRole === 'lawyer' ? 'Selected' : 'Select Role'}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {showLawyerForm && (
        <div className="mt-8 space-y-6">
          <div className="space-y-2">
            <h2 className="text-xl font-bold">Complete Your Lawyer Profile</h2>
            <p className="text-neutral-500">
              {isLoadingLawyerProfile
                ? 'Loading your existing lawyer profile data...'
                : existingLawyerProfile
                  ? 'Update your lawyer profile information.'
                  : 'Provide additional information to set up your lawyer profile.'}
            </p>
          </div>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className={`space-y-6 ${isLoadingLawyerProfile ? 'opacity-70 pointer-events-none' : ''}`}
            >
              {isLoadingLawyerProfile && (
                <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-10">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="full_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="specialization"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Specialization</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          {specializations.map((spec) => (
                            <div
                              key={spec}
                              className="flex items-center space-x-2"
                            >
                              <input
                                type="checkbox"
                                id={`spec-${spec}`}
                                checked={field.value.includes(spec)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    field.onChange([...field.value, spec]);
                                  } else {
                                    field.onChange(
                                      field.value.filter((val) => val !== spec)
                                    );
                                  }
                                }}
                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                              />
                              <label
                                htmlFor={`spec-${spec}`}
                                className="text-sm"
                              >
                                {spec}
                              </label>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Select your areas of legal expertise
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="languages"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Languages</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          {languages.map((lang) => (
                            <div
                              key={lang}
                              className="flex items-center space-x-2"
                            >
                              <input
                                type="checkbox"
                                id={`lang-${lang}`}
                                checked={field.value.includes(lang)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    field.onChange([...field.value, lang]);
                                  } else {
                                    field.onChange(
                                      field.value.filter((val) => val !== lang)
                                    );
                                  }
                                }}
                                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                              />
                              <label
                                htmlFor={`lang-${lang}`}
                                className="text-sm"
                              >
                                {lang}
                              </label>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Languages you can provide services in
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="years_experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Years of Experience</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="5"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hourly_rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hourly Rate ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="150"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="consultation_fee"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Consultation Fee ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="100"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="md:col-span-2">
                  <FormField
                    control={form.control}
                    name="bio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bio</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tell clients about your background and expertise"
                            className="resize-none min-h-[120px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-between items-center">
                {existingLawyerProfile && (
                  <p className="text-sm text-muted-foreground">
                    <span className="text-green-600 font-medium">✓</span> Using
                    your existing lawyer profile data
                  </p>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoadingLawyerProfile}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : existingLawyerProfile ? (
                    'Update Profile'
                  ) : (
                    'Complete Setup'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      )}

      {!showLawyerForm && (
        <div className="flex justify-end">
          <Button
            onClick={handleContinue}
            disabled={isSubmitting || isLoadingLawyerProfile}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Complete Setup'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
