import { SVGProps } from "react";

export function Slack(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      fill="none"
      viewBox="0 0 14 14"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M5.118 0c-.772 0-1.396.627-1.396 1.4s.625 1.399 1.396 1.4h1.396V1.4C6.514.628 5.89.001 5.118 0m0 3.733H1.396c-.772 0-1.397.628-1.396 1.4-.001.772.624 1.4 1.395 1.4h3.723c.771 0 1.396-.627 1.396-1.4s-.625-1.4-1.396-1.4M13.958 5.133c0-.772-.624-1.4-1.396-1.4s-1.397.628-1.396 1.4v1.4h1.396c.772 0 1.397-.627 1.396-1.4m-3.722 0V1.4C10.236.628 9.612 0 8.84 0S7.444.627 7.444 1.4v3.733c0 .772.624 1.4 1.396 1.4s1.396-.627 1.396-1.4M8.84 14c.771 0 1.396-.627 1.396-1.4s-.625-1.399-1.396-1.4H7.444v1.4c0 .772.624 1.399 1.396 1.4m0-3.734h3.722c.772 0 1.397-.627 1.396-1.4a1.4 1.4 0 0 0-1.396-1.4H8.84c-.771 0-1.396.627-1.396 1.4s.624 1.4 1.396 1.4M0 8.866c0 .773.624 1.4 1.396 1.4s1.397-.627 1.396-1.4v-1.4H1.396c-.772.001-1.397.628-1.396 1.4m3.722 0V12.6c-.001.772.624 1.399 1.396 1.4s1.396-.627 1.396-1.4V8.868a1.397 1.397 0 1 0-2.791-.001"
        clipRule="evenodd"
      />
    </svg>
  );
}
