'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useDocuments } from '@/lib/hooks';
import { DocumentShareLink } from '@/lib/types/database-modules';
import { formatDistanceToNow } from 'date-fns';
import { Calendar, Eye, Key, Link2, Lock } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ShareLinkStatsProps {
  documentId: string;
}

export function ShareLinkStats({ documentId }: ShareLinkStatsProps) {
  // Get the documents hook
  const {
    getShareLinkStats,
    loading: hookLoading,
    error: hookError,
  } = useDocuments();

  // Log the document ID for debugging
  console.log('ShareLinkStats component rendering for document:', documentId);
  const [shareLinks, setShareLinks] = useState<DocumentShareLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchShareLinks = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('ShareLinkStats: Fetching links for document:', documentId);

        // Get all share links for the document using the hook
        const data = await getShareLinkStats(documentId);

        // Always set the links, even if empty
        setShareLinks(data);
        console.log('ShareLinkStats: Fetched links successfully:', data.length);
      } catch (err) {
        console.error('ShareLinkStats: Error fetching links:', err);
        setError('An unexpected error occurred while loading share statistics');
        // Set empty array on error
        setShareLinks([]);
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchShareLinks();
    } else {
      console.warn('ShareLinkStats: No document ID provided');
      setShareLinks([]);
      setLoading(false);
    }
  }, [documentId, getShareLinkStats]);

  // Function to get the access type icon
  const getAccessTypeIcon = (accessType: string) => {
    switch (accessType) {
      case 'pin_protected':
        return <Key className="h-4 w-4" />;
      case 'password_protected':
        return <Lock className="h-4 w-4" />;
      default:
        return <Link2 className="h-4 w-4" />;
    }
  };

  // Function to format the date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Never';
    return formatDistanceToNow(new Date(dateString), { addSuffix: true });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-40" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (shareLinks.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Share Link Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            No share links have been created for this document yet.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Share Link Statistics</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {shareLinks.map((link) => (
            <div
              key={link.id}
              className="border rounded-md p-4 flex flex-col space-y-2"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getAccessTypeIcon(link.access_type)}
                  <span className="font-medium">
                    {link.access_type === 'public'
                      ? 'Public Link'
                      : link.access_type === 'pin_protected'
                        ? 'PIN Protected'
                        : 'Password Protected'}
                  </span>
                  <Badge
                    variant={link.is_active ? 'default' : 'secondary'}
                    className="ml-2"
                  >
                    {link.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <Badge
                  variant="outline"
                  className={
                    link.permission === 'edit'
                      ? 'bg-blue-50 text-blue-700 border-blue-200'
                      : 'bg-gray-50 text-gray-700 border-gray-200'
                  }
                >
                  {link.permission}
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-muted-foreground" />
                  <span>
                    <strong>{link.access_count || 0}</strong> views
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Last viewed: {formatDate(link.last_accessed_at)}</span>
                </div>
              </div>

              {link.expires_at && (
                <div className="text-sm text-muted-foreground">
                  Expires {formatDate(link.expires_at)}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
