export interface TemplateContext {
  legalRequirements: string[];
  sections: {
    title: string;
    items: string[];
  }[];
  requiredClauses: string[];
  formattingGuidelines: string[];
  officialRequirements: string[];
  rawContent: string;
}

/**
 * Server-side function to load templates
 * This can be imported and used in server components or API routes
 */
export async function loadTemplateFromServer(
  jurisdiction: string,
  formType: string,
  category: string,
  version?: string
): Promise<TemplateContext> {
  const url = new URL(
    `/api/templates/load`,
    process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  );

  url.searchParams.set('jurisdiction', jurisdiction);
  url.searchParams.set('formType', formType);
  url.searchParams.set('category', category);
  if (version) {
    url.searchParams.set('version', version);
  }

  const response = await fetch(url.toString(), { next: { revalidate: 3600 } });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        `Template not found for ${jurisdiction}/${category}/${formType}${
          version ? ` version ${version}` : ''
        }`
    );
  }

  return response.json();
}
