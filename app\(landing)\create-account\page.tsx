'use client';

import Create<PERSON>ccountForm from '@/components/forms/auth/create-account';
import { AuthPageToggle } from '@/components/ux/comp';
import { User } from '@/components/ux/icons';
import { LeftDashes, RightDashes } from '@/components/ux/lines';
import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { AlertTriangle, UserIcon } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useEffect, useState } from 'react';
import { toast } from 'sonner';

export const dynamic = 'force-dynamic';

function CreateAccountContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    if (!searchParams) return;

    // Check for error parameters in the URL
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      const errorMessage = errorDescription
        ? decodeURIComponent(errorDescription.replace(/\+/g, ' '))
        : 'An authentication error occurred';

      setAuthError(errorMessage);

      // Show toast notification
      toast.error('Authentication Error', {
        description: errorMessage,
        action: {
          label: 'View Details',
          onClick: () =>
            router.push(
              `/auth-error?message=${encodeURIComponent(errorMessage)}`
            ),
        },
      });
    }
  }, [searchParams, router]);
  return (
    <main className="mx-auto flex min-h-screen py-40 relative w-full max-w-7xl flex-col items-center justify-center bg-neutral-100 p-4">
      <LeftDashes />
      <RightDashes />
      <div className="flex h-full flex-col justify-between lg:col-span-2 lg:p-2">
        {/* form */}
        <div className="flex w-full flex-1 flex-col items-center justify-center md:px-4">
          <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
            {/* icon */}
            <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white">
              <div className="absolute inset-0 z-1 bg-linear-to-b from-transparent to-gray-100" />
              <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-gray-200 bg-white">
                <User className="h-8 w-8" />
              </div>
            </div>
            {/* message */}
            <div className={cn('space-y-1 text-center')}>
              <h2
                className={cn(
                  FONT_BIRCOLAGE_GROTESQUE.className,
                  'text-2xl font-semibold'
                )}
              >
                Create Account
              </h2>
              <p className="text-gray-400">
                Enter your details to Create Account.
              </p>
            </div>
          </div>
          <div className="flex w-full flex-col items-center border-b py-2">
            <AuthPageToggle />
          </div>

          {authError && (
            <div className="mt-4 w-full max-w-md rounded-md bg-red-50 p-4 text-sm text-red-800">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4" />
                <p className="font-medium">Authentication Error</p>
              </div>
              <p className="mt-2 pl-6">{authError}</p>
              <button
                onClick={() =>
                  router.push(
                    `/auth-error?message=${encodeURIComponent(authError)}`
                  )
                }
                className="mt-2 pl-6 text-red-600 hover:underline"
              >
                View Details
              </button>
            </div>
          )}

          <CreateAccountForm />
        </div>
      </div>
    </main>
  );
}

export default function CreateAccount() {
  return (
    <Suspense
      fallback={
        <main className="mx-auto flex min-h-screen py-40 relative w-full max-w-7xl flex-col items-center justify-center bg-neutral-100 p-4">
          <LeftDashes />
          <RightDashes />
          <div className="flex h-full flex-col justify-between lg:col-span-2 lg:p-2">
            <div className="flex w-full flex-1 flex-col items-center justify-center md:px-4">
              <div className="flex w-full flex-col items-center justify-center border-b pb-4 pt-2">
                <div className="relative z-0 flex h-24 w-24 flex-col items-center justify-center overflow-hidden rounded-full border border-gray-200 bg-white">
                  <div className="absolute inset-0 z-1 bg-linear-to-b from-transparent to-gray-100" />
                  <div className="relative z-2 flex h-16 w-16 flex-col items-center justify-center rounded-full border border-gray-200 bg-white">
                    <User className="h-8 w-8" />
                  </div>
                </div>
                <div className={cn('space-y-1 text-center')}>
                  <h2
                    className={cn(
                      FONT_BIRCOLAGE_GROTESQUE.className,
                      'text-2xl font-semibold'
                    )}
                  >
                    Create Account
                  </h2>
                  <p className="text-gray-400">Loading form...</p>
                </div>
              </div>
            </div>
          </div>
        </main>
      }
    >
      <CreateAccountContent />
    </Suspense>
  );
}
