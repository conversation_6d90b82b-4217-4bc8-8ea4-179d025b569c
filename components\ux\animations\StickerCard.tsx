'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

const decorativeElementVariants = {
  initial: {
    width: 30,
    height: 30,
  },
  hover: {
    width: 50,
    height: 50,
  },
};

const cornerElementVariants = {
  initial: {
    width: 28,
    height: 28,
  },
  hover: {
    width: 42,
    height: 42,
    boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.05)',
  },
};

export const StickerCard = ({
  title,
  description,
  Icon,
}: {
  title: string;
  description: string;
  Icon: React.ElementType;
}) => (
  <div className='relative'>
    <motion.div
      initial='initial'
      whileHover='hover'
      className={cn(
        'relative z-10 mt-0 h-64 w-44 justify-end flex flex-col overflow-hidden',
        'rounded-lg bg-white px-4 pt-5 pb-[18px] shadow-[inset_0_0_0_1px] shadow-gray-200'
      )}
    >
      <motion.div
        variants={cornerElementVariants}
        transition={{ duration: 0.18 }}
        className='absolute top-0 right-0 -translate-y-2 translate-x-2 rounded-bl-lg border bg-gray-50'
      />
      <motion.div
        variants={decorativeElementVariants}
        transition={{ duration: 0.18 }}
        className='absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 rotate-45 bg-white shadow-[0_1px_0_0_] shadow-gray-200'
      />
      <div className='flex flex-col space-y-4'>
        <div className='relative flex items-center gap-1'>
          <div className='absolute -left-4 h-5 w-[3px] rounded-r-sm bg-accent-100' />
          <Icon className='size-4 shrink-0 text-accent-300' />
          <h3 className='font-medium text-gray-600'>{title}</h3>
        </div>
        {/* <Button variant={'shadow_accent'} className='w-fit'>
          {description}
        </Button> */}
      </div>
    </motion.div>
  </div>
);
