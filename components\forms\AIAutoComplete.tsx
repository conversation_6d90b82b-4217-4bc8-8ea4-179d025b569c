import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { AIService, FormFieldSuggestion } from '@/lib/services/AIService';
import { cn } from '@/lib/utils/cn';
import {
  BrainCircuit,
  Check,
  ChevronsUpDown,
  SparklesIcon,
} from 'lucide-react';
import { useEffect, useState } from 'react';

export interface AutoCompleteOption {
  value: string;
  label: string;
  confidence: number;
  explanation?: string;
}

interface AIAutoCompleteProps {
  fieldName: string;
  fieldType: string;
  formContext: Record<string, any>;
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
}

export function AIAutoComplete({
  fieldName,
  fieldType,
  formContext,
  value,
  onValueChange,
  placeholder = 'Select option',
  disabled = false,
  className,
  required = false,
}: AIAutoCompleteProps) {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState<AutoCompleteOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAIBadge, setShowAIBadge] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<FormFieldSuggestion[]>([]);

  const aiService = AIService.getInstance();

  // Load predefined options based on field type
  useEffect(() => {
    switch (fieldType) {
      case 'country':
        setOptions([
          { value: 'us', label: 'United States', confidence: 1 },
          { value: 'ca', label: 'Canada', confidence: 1 },
          { value: 'uk', label: 'United Kingdom', confidence: 1 },
          { value: 'au', label: 'Australia', confidence: 1 },
        ]);
        break;
      case 'state':
        setOptions([
          { value: 'ca', label: 'California', confidence: 1 },
          { value: 'ny', label: 'New York', confidence: 1 },
          { value: 'tx', label: 'Texas', confidence: 1 },
          { value: 'fl', label: 'Florida', confidence: 1 },
        ]);
        break;
      case 'gender':
        setOptions([
          { value: 'male', label: 'Male', confidence: 1 },
          { value: 'female', label: 'Female', confidence: 1 },
          { value: 'non-binary', label: 'Non-binary', confidence: 1 },
          {
            value: 'prefer-not-to-say',
            label: 'Prefer not to say',
            confidence: 1,
          },
        ]);
        break;
      default:
        // For other field types, options will be loaded from AI
        setOptions([]);
    }
  }, [fieldType]);

  // Load AI suggestions when popover opens and we don't have predefined options
  useEffect(() => {
    if (open && options.length === 0 && !loading) {
      loadAISuggestions();
    }
  }, [open, options.length]);

  const loadAISuggestions = async () => {
    setLoading(true);
    setError(null);

    try {
      const suggestions = await aiService.suggestFieldValue(
        fieldName,
        fieldType,
        formContext
      );

      setAiSuggestions(suggestions);

      // Convert suggestions to options format
      const aiOptions = suggestions.map((suggestion) => ({
        value: suggestion.value,
        label: suggestion.value,
        confidence: suggestion.confidence,
        explanation: suggestion.explanation,
      }));

      setOptions(aiOptions);
    } catch (err) {
      console.error('Error fetching AI suggestions:', err);
      setError('Failed to load suggestions');
    } finally {
      setLoading(false);
    }
  };

  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue);
    setOpen(false);

    // Check if the selected value was an AI suggestion
    const isSuggestion = aiSuggestions.some(
      (suggestion) => suggestion.value === selectedValue
    );
    setShowAIBadge(isSuggestion);
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge className="ml-auto bg-green-500">High Confidence</Badge>;
    } else if (confidence >= 0.5) {
      return <Badge className="ml-auto bg-yellow-500">Medium Confidence</Badge>;
    } else {
      return <Badge className="ml-auto bg-red-500">Low Confidence</Badge>;
    }
  };

  return (
    <div className={cn('space-y-1', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'w-full justify-between',
              !value && 'text-muted-foreground'
            )}
            disabled={disabled}
          >
            <div className="flex items-center gap-2 truncate">
              {value ? (
                <>
                  {options.find((option) => option.value === value)?.label ||
                    value}
                  {showAIBadge && (
                    <Badge className="h-5 bg-primary/20 text-primary">
                      <BrainCircuit className="h-3 w-3 mr-1" />
                      AI
                    </Badge>
                  )}
                </>
              ) : (
                placeholder
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full min-w-[240px] p-0">
          <Command>
            <CommandInput placeholder={`Search ${fieldName}...`} />
            <CommandList>
              <CommandEmpty>
                {loading ? (
                  <div className="flex flex-col items-center justify-center py-6 space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                ) : error ? (
                  <div className="py-6 text-center text-sm text-destructive">
                    {error}
                  </div>
                ) : (
                  <div className="py-6 text-center text-sm">
                    No options found. Try a different search or wait for AI
                    suggestions.
                  </div>
                )}
              </CommandEmpty>
              {options.length > 0 && (
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={handleSelect}
                      className="flex justify-between"
                    >
                      <div className="flex flex-col">
                        <div className="flex items-center">
                          {option.label}
                          {value === option.value && (
                            <Check className="ml-2 h-4 w-4" />
                          )}
                        </div>
                        {option.explanation && (
                          <span className="text-xs text-muted-foreground">
                            {option.explanation}
                          </span>
                        )}
                      </div>
                      {option.confidence < 1 &&
                        getConfidenceBadge(option.confidence)}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
              <div className="flex items-center justify-between px-3 py-2 text-xs text-muted-foreground border-t">
                {options.some((opt) => opt.confidence < 1) ? (
                  <div className="flex items-center gap-1">
                    <SparklesIcon className="h-3 w-3 text-primary" />
                    AI-powered suggestions
                  </div>
                ) : (
                  <div>Select an option</div>
                )}
                {loading ? (
                  <div>Loading...</div>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 text-xs"
                    onClick={loadAISuggestions}
                  >
                    Refresh
                  </Button>
                )}
              </div>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
