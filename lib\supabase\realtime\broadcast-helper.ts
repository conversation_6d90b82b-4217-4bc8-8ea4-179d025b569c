'use client';

import { supabaseClient } from '@/lib/supabase/singleton-client';

/**
 * Helper functions for working with Supabase Realtime Broadcast
 *
 * These functions make it easier to send and receive broadcast messages
 * using the Supabase Realtime API.
 */

/**
 * Send a broadcast message to a specific channel
 *
 * @param topic The channel/topic to send the message to
 * @param event The event type (e.g., 'INSERT', 'UPDATE', 'DELETE', or custom event)
 * @param payload The data to send
 * @returns A promise that resolves when the message is sent
 */
export async function sendBroadcast(
  topic: string,
  event: string,
  payload: any
): Promise<void> {
  try {
    // Use the SQL function to send a broadcast message
    await supabaseClient.rpc('send_realtime_message', {
      topic,
      event,
      payload,
    });
  } catch (error) {
    console.error(`Error sending broadcast to ${topic}:`, error);
    throw error;
  }
}

/**
 * Send a direct database broadcast using SQL
 *
 * This function executes a SQL query to send a broadcast message
 * directly from the database.
 *
 * @param topic The channel/topic to send the message to
 * @param event The event type
 * @param payload The data to send
 * @returns A promise that resolves when the message is sent
 */
export async function sendDatabaseBroadcast(
  topic: string,
  event: string,
  payload: any
): Promise<void> {
  try {
    // Convert payload to a JSON string
    const payloadJson = JSON.stringify(payload);

    // Execute the SQL query to send a broadcast message
    await supabaseClient.rpc('send_realtime_message', {
      topic,
      event,
      payload: payloadJson,
    });
  } catch (error) {
    console.error(`Error sending database broadcast to ${topic}:`, error);
    throw error;
  }
}

/**
 * Send a notification to a specific user
 *
 * @param userId The ID of the user to send the notification to
 * @param title The notification title
 * @param content The notification content
 * @param type The notification type
 * @param actionUrl Optional URL to navigate to when the notification is clicked
 * @param relatedId Optional ID of a related entity
 * @param relatedType Optional type of the related entity
 * @returns A promise that resolves when the notification is created
 */
export async function sendNotification(
  userId: string,
  title: string,
  content: string,
  type: string,
  actionUrl?: string,
  relatedId?: string,
  relatedType?: string
): Promise<void> {
  try {
    // Insert a new notification into the database
    // This will trigger the broadcast_notification_changes function
    const { error } = await supabaseClient.from('notifications').insert({
      user_id: userId,
      title,
      content,
      type,
      read: false,
      action_url: actionUrl,
      related_id: relatedId,
      related_type: relatedType,
    });

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error(`Error sending notification to user ${userId}:`, error);
    throw error;
  }
}

// Create a named object for export
const BroadcastHelper = {
  sendBroadcast,
  sendDatabaseBroadcast,
  sendNotification,
};

export default BroadcastHelper;
