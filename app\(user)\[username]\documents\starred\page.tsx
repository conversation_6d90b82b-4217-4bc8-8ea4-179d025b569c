import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { pageProps } from '@/types';
import { Calendar, FileText, MoreHorizontal, Star, User } from 'lucide-react';

export default async function StarredDocumentsPage({ params }: pageProps) {
  const username = (await params).username;

  // Mock starred documents data
  const starredDocuments = [
    {
      id: 1,
      title: 'Contract Agreement',
      dateStarred: 'Mar 15, 2023',
      lastEdited: '2 days ago',
      editedBy: 'You',
      type: 'Contract',
      tags: ['Legal', 'Important'],
    },
    {
      id: 2,
      title: 'Series A Investment Terms',
      dateStarred: 'Feb 28, 2023',
      lastEdited: '1 week ago',
      editedBy: '<PERSON>',
      type: 'Agreement',
      tags: ['Finance', 'Confidential'],
    },
    {
      id: 3,
      title: 'Software License Agreement',
      dateStarred: 'Jan 12, 2023',
      lastEdited: '3 weeks ago',
      editedBy: '<PERSON>',
      type: 'License',
      tags: ['Software', 'Legal'],
    },
  ];

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center gap-2">
          <Star className="size-5 text-amber-400 fill-amber-400" />
          <h1 className="text-2xl font-bold">Starred Documents</h1>
        </div>
        <p className="text-neutral-500 mt-1">
          Documents you&apos;ve marked as important
        </p>
      </section>

      <section>
        {starredDocuments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {starredDocuments.map((doc) => (
              <Card key={doc.id} className="overflow-hidden">
                <div className="bg-neutral-100 h-40 flex items-center justify-center relative">
                  <FileText className="size-16 text-neutral-400" />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 h-8 w-8 bg-white/80 hover:bg-white shadow-sm"
                  >
                    <Star className="size-4 fill-amber-400 text-amber-400" />
                  </Button>
                </div>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold">{doc.title}</h3>
                      <div className="flex items-center gap-1 text-sm text-neutral-500 mt-1">
                        <Calendar className="size-3.5" />
                        <span>Starred on {doc.dateStarred}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-neutral-500 mt-1">
                        <User className="size-3.5" />
                        <span>
                          Last edited by{' '}
                          <span className="font-medium">{doc.editedBy}</span> •{' '}
                          {doc.lastEdited}
                        </span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Open</DropdownMenuItem>
                        <DropdownMenuItem>Make a Copy</DropdownMenuItem>
                        <DropdownMenuItem>Share</DropdownMenuItem>
                        <DropdownMenuItem>Remove Star</DropdownMenuItem>
                        <Separator />
                        <DropdownMenuItem className="text-red-600">
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-3">
                    {doc.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-0.5 bg-neutral-100 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    <span className="px-2 py-0.5 bg-blue-50 text-blue-700 text-xs rounded-full ml-auto">
                      {doc.type}
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-10 text-center">
              <Star className="size-12 text-neutral-300 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-600 text-lg">
                No starred documents
              </h3>
              <p className="text-neutral-500 mt-1 max-w-md mx-auto">
                Star important documents to find them quickly. Click the star
                icon on any document to add it here.
              </p>
              <Button className="mt-4">Browse Documents</Button>
            </CardContent>
          </Card>
        )}
      </section>
    </main>
  );
}
