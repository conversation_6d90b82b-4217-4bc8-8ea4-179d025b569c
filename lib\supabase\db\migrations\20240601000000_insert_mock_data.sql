-- Migration: Insert Mock Data
-- Description: Inserts mock data for all tables in the database
-- Created: 2024-06-01

-- First, let's create a function to get the current user ID
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID AS $$
BEGIN
  RETURN auth.uid();
END;
$$ LANGUAGE plpgsql;

-- Insert mock data for profiles
INSERT INTO public.profiles (id, username, full_name, avatar_url, email, phone_number, is_onboarded, plan, role)
VALUES
  (get_current_user_id(), 'current_user', 'Current User', 'https://ui-avatars.com/api/?name=Current+User&background=0D8ABC&color=fff', auth.email(), '+1234567890', true, 'Standard', 'user')
ON CONFLICT (id) DO UPDATE SET
  username = EXCLUDED.username,
  full_name = EXCLUDED.full_name,
  avatar_url = EXCLUDED.avatar_url,
  email = EXCLUDED.email,
  phone_number = EXCLUDED.phone_number,
  is_onboarded = EXCLUDED.is_onboarded,
  plan = EXCLUDED.plan,
  role = EXCLUDED.role;

-- Insert mock data for user_settings
INSERT INTO public.user_settings (user_id, theme, language, notifications_enabled, email_notifications, display_preferences, export_preferences)
VALUES
  (get_current_user_id(), 'light', 'en', true,
   '{"comment_added": true, "task_assigned": true, "contract_signed": true, "document_shared": true, "document_updated": true}'::jsonb,
   '{"document_view": "card", "sidebar_collapsed": false, "show_document_previews": true}'::jsonb,
   '{"default_format": "pdf", "include_metadata": true, "include_signatures": true}'::jsonb)
ON CONFLICT (user_id) DO UPDATE SET
  theme = EXCLUDED.theme,
  language = EXCLUDED.language,
  notifications_enabled = EXCLUDED.notifications_enabled,
  email_notifications = EXCLUDED.email_notifications,
  display_preferences = EXCLUDED.display_preferences,
  export_preferences = EXCLUDED.export_preferences;

-- Insert mock data for usage_limits
INSERT INTO public.usage_limits (plan, document_limit, storage_limit, collaborators_limit, smart_contracts_limit, lawyer_consultations_limit, features)
VALUES
  ('Free', 10, 100000000, 3, 1, 1, '{"advanced_templates": false, "smart_contract_deployment": false, "lawyer_consultations": true, "priority_support": false, "custom_branding": false}'::jsonb),
  ('Standard', 50, 1000000000, 10, 5, 3, '{"advanced_templates": true, "smart_contract_deployment": true, "lawyer_consultations": true, "priority_support": false, "custom_branding": false}'::jsonb),
  ('Enterprise', null, null, null, null, null, '{"advanced_templates": true, "smart_contract_deployment": true, "lawyer_consultations": true, "priority_support": true, "custom_branding": true}'::jsonb)
ON CONFLICT (plan) DO NOTHING;

-- Insert mock data for user_usage
INSERT INTO public.user_usage (user_id, documents_count, storage_used, collaborators_count, smart_contracts_count, lawyer_consultations_count)
VALUES
  (get_current_user_id(), 5, 25000000, 2, 1, 1)
ON CONFLICT (user_id) DO UPDATE SET
  documents_count = EXCLUDED.documents_count,
  storage_used = EXCLUDED.storage_used,
  collaborators_count = EXCLUDED.collaborators_count,
  smart_contracts_count = EXCLUDED.smart_contracts_count,
  lawyer_consultations_count = EXCLUDED.lawyer_consultations_count;

-- Insert mock data for document_tags
INSERT INTO public.document_tags (name)
VALUES
  ('Contract'),
  ('Agreement'),
  ('NDA'),
  ('Employment'),
  ('Legal'),
  ('Important'),
  ('Draft'),
  ('Final'),
  ('Template')
ON CONFLICT (name) DO NOTHING;

-- Insert mock data for documents
INSERT INTO public.documents (title, description, content, document_type, status, is_template, owner_id, metadata)
VALUES
  ('Employment Agreement', 'Standard employment agreement template', '{"sections":[{"title":"Introduction","content":"This Employment Agreement is made and entered into on [Date] by and between [Company Name] and [Employee Name]."},{"title":"Terms of Employment","content":"The Employee agrees to be employed on the terms and conditions set out in this Agreement."},{"title":"Compensation","content":"The Employee shall be paid a salary of [Amount] per [Period]."},{"title":"Termination","content":"Either party may terminate this Agreement by providing [Notice Period] notice."}]}'::jsonb, 'contract', 'draft', false, get_current_user_id(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'::jsonb),

  ('Non-Disclosure Agreement', 'Confidentiality agreement for business discussions', '{"sections":[{"title":"Parties","content":"This Non-Disclosure Agreement is made between [Party A] and [Party B]."},{"title":"Confidential Information","content":"Both parties agree to keep confidential any proprietary information disclosed during business discussions."},{"title":"Term","content":"This Agreement shall remain in effect for [Duration] from the date of signing."},{"title":"Governing Law","content":"This Agreement shall be governed by the laws of [Jurisdiction]."}]}'::jsonb, 'agreement', 'published', false, get_current_user_id(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'::jsonb),

  ('Rental Agreement Template', 'Template for property rental agreements', '{"sections":[{"title":"Property Details","content":"The property located at [Address] is being rented to [Tenant Name]."},{"title":"Rent","content":"The monthly rent is [Amount] due on the [Day] of each month."},{"title":"Term","content":"The lease term is [Duration] beginning on [Start Date]."},{"title":"Security Deposit","content":"A security deposit of [Amount] is required."}]}'::jsonb, 'contract', 'template', true, get_current_user_id(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'::jsonb),

  ('Business Proposal', 'Proposal for new business venture', '{"sections":[{"title":"Executive Summary","content":"This proposal outlines a business opportunity for [Project Name]."},{"title":"Market Analysis","content":"The target market consists of [Market Description]."},{"title":"Financial Projections","content":"We project revenue of [Amount] in the first year."},{"title":"Implementation Plan","content":"The project will be implemented in [Number] phases."}]}'::jsonb, 'report', 'draft', false, get_current_user_id(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'::jsonb),

  ('Service Agreement', 'Agreement for providing professional services', '{"sections":[{"title":"Services","content":"The Provider agrees to provide [Service Description] to the Client."},{"title":"Compensation","content":"The Client agrees to pay [Amount] for the services."},{"title":"Term","content":"This Agreement shall remain in effect until [End Date]."},{"title":"Termination","content":"Either party may terminate this Agreement with [Notice Period] notice."}]}'::jsonb, 'contract', 'archived', false, get_current_user_id(), '{"version": "1.0", "author": "Current User", "created_date": "2024-06-01"}'::jsonb)
ON CONFLICT DO NOTHING;

-- Link documents to tags
WITH
  doc1 AS (SELECT id FROM public.documents WHERE title = 'Employment Agreement' LIMIT 1),
  doc2 AS (SELECT id FROM public.documents WHERE title = 'Non-Disclosure Agreement' LIMIT 1),
  doc3 AS (SELECT id FROM public.documents WHERE title = 'Rental Agreement Template' LIMIT 1),
  doc4 AS (SELECT id FROM public.documents WHERE title = 'Business Proposal' LIMIT 1),
  doc5 AS (SELECT id FROM public.documents WHERE title = 'Service Agreement' LIMIT 1),
  tag1 AS (SELECT id FROM public.document_tags WHERE name = 'Contract' LIMIT 1),
  tag2 AS (SELECT id FROM public.document_tags WHERE name = 'Agreement' LIMIT 1),
  tag3 AS (SELECT id FROM public.document_tags WHERE name = 'NDA' LIMIT 1),
  tag4 AS (SELECT id FROM public.document_tags WHERE name = 'Employment' LIMIT 1),
  tag5 AS (SELECT id FROM public.document_tags WHERE name = 'Legal' LIMIT 1),
  tag6 AS (SELECT id FROM public.document_tags WHERE name = 'Important' LIMIT 1),
  tag7 AS (SELECT id FROM public.document_tags WHERE name = 'Draft' LIMIT 1),
  tag8 AS (SELECT id FROM public.document_tags WHERE name = 'Final' LIMIT 1),
  tag9 AS (SELECT id FROM public.document_tags WHERE name = 'Template' LIMIT 1)
INSERT INTO public.document_to_tags (document_id, tag_id)
VALUES
  ((SELECT id FROM doc1), (SELECT id FROM tag1)),
  ((SELECT id FROM doc1), (SELECT id FROM tag4)),
  ((SELECT id FROM doc1), (SELECT id FROM tag5)),
  ((SELECT id FROM doc2), (SELECT id FROM tag2)),
  ((SELECT id FROM doc2), (SELECT id FROM tag3)),
  ((SELECT id FROM doc2), (SELECT id FROM tag5)),
  ((SELECT id FROM doc3), (SELECT id FROM tag1)),
  ((SELECT id FROM doc3), (SELECT id FROM tag9)),
  ((SELECT id FROM doc4), (SELECT id FROM tag7)),
  ((SELECT id FROM doc5), (SELECT id FROM tag1)),
  ((SELECT id FROM doc5), (SELECT id FROM tag2)),
  ((SELECT id FROM doc5), (SELECT id FROM tag8))
ON CONFLICT DO NOTHING;

-- Insert mock data for lawyers
INSERT INTO public.lawyers (user_id, full_name, email, phone, specialization, bio, years_experience, hourly_rate, availability, avatar_url, is_verified, status)
VALUES
  ('00000000-0000-0000-0000-000000000001', 'John Smith', '<EMAIL>', '+1234567890', ARRAY['Contract Law', 'Corporate Law'], 'Experienced corporate lawyer with over 15 years of practice.', 15, 250, '{"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "15:00"}}'::jsonb, 'https://ui-avatars.com/api/?name=John+Smith&background=0D8ABC&color=fff', true, 'active'),

  ('00000000-0000-0000-0000-000000000002', 'Sarah Johnson', '<EMAIL>', '+1987654321', ARRAY['Intellectual Property', 'Technology Law'], 'Specialized in intellectual property and technology law with focus on startups.', 8, 200, '{"monday": {"start": "10:00", "end": "18:00"}, "tuesday": {"start": "10:00", "end": "18:00"}, "wednesday": {"start": "10:00", "end": "18:00"}, "thursday": {"start": "10:00", "end": "18:00"}, "friday": {"start": "10:00", "end": "16:00"}}'::jsonb, 'https://ui-avatars.com/api/?name=Sarah+Johnson&background=0D8ABC&color=fff', true, 'active'),

  ('00000000-0000-0000-0000-000000000003', 'Michael Chen', '<EMAIL>', '+1122334455', ARRAY['Real Estate Law', 'Contract Law'], 'Expert in real estate transactions and contract negotiations.', 12, 225, '{"monday": {"start": "08:00", "end": "16:00"}, "tuesday": {"start": "08:00", "end": "16:00"}, "wednesday": {"start": "08:00", "end": "16:00"}, "thursday": {"start": "08:00", "end": "16:00"}, "friday": {"start": "08:00", "end": "14:00"}}'::jsonb, 'https://ui-avatars.com/api/?name=Michael+Chen&background=0D8ABC&color=fff', true, 'active')
ON CONFLICT DO NOTHING;

-- Insert mock data for lawyer_consultations
INSERT INTO public.lawyer_consultations (lawyer_id, client_id, document_id, status, scheduled_at, duration_minutes, consultation_type, meeting_link, notes, price, payment_status)
VALUES
  ((SELECT id FROM public.lawyers WHERE full_name = 'John Smith' LIMIT 1), get_current_user_id(), (SELECT id FROM public.documents WHERE title = 'Employment Agreement' LIMIT 1), 'scheduled', (NOW() + INTERVAL '3 days')::timestamp, 60, 'video', 'https://meet.example.com/abc123', 'Discuss employment agreement terms', 250, 'paid'),

  ((SELECT id FROM public.lawyers WHERE full_name = 'Sarah Johnson' LIMIT 1), get_current_user_id(), (SELECT id FROM public.documents WHERE title = 'Non-Disclosure Agreement' LIMIT 1), 'completed', (NOW() - INTERVAL '5 days')::timestamp, 45, 'video', 'https://meet.example.com/def456', 'Review NDA clauses', 150, 'paid'),

  ((SELECT id FROM public.lawyers WHERE full_name = 'Michael Chen' LIMIT 1), get_current_user_id(), (SELECT id FROM public.documents WHERE title = 'Service Agreement' LIMIT 1), 'scheduled', (NOW() + INTERVAL '7 days')::timestamp, 30, 'phone', NULL, 'Quick review of service agreement', 112.50, 'pending')
ON CONFLICT DO NOTHING;

-- Insert mock data for lawyer_reviews
INSERT INTO public.lawyer_reviews (lawyer_id, user_id, consultation_id, rating, review_text, is_anonymous)
VALUES
  ((SELECT id FROM public.lawyers WHERE full_name = 'Sarah Johnson' LIMIT 1), get_current_user_id(), (SELECT id FROM public.lawyer_consultations WHERE lawyer_id = (SELECT id FROM public.lawyers WHERE full_name = 'Sarah Johnson' LIMIT 1) LIMIT 1), 5, 'Excellent advice and very professional. Highly recommended!', false)
ON CONFLICT DO NOTHING;

-- Insert mock data for contract_templates
INSERT INTO public.contract_templates (name, description, contract_type, source_code, abi, parameters, is_public, created_by)
VALUES
  ('Simple Token', 'A simple ERC20 token contract', 'token', 'pragma solidity ^0.8.0;\n\nimport "@openzeppelin/contracts/token/ERC20/ERC20.sol";\n\ncontract SimpleToken is ERC20 {\n    constructor(string memory name, string memory symbol, uint256 initialSupply) ERC20(name, symbol) {\n        _mint(msg.sender, initialSupply * 10 ** decimals());\n    }\n}', '[{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint256","name":"initialSupply","type":"uint256"}],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]'::jsonb, '{"name": {"type": "string", "description": "Token name"}, "symbol": {"type": "string", "description": "Token symbol"}, "initialSupply": {"type": "uint256", "description": "Initial token supply"}}'::jsonb, true, get_current_user_id()),

  ('Escrow Contract', 'A simple escrow contract for secure transactions', 'escrow', 'pragma solidity ^0.8.0;\n\ncontract Escrow {\n    address public buyer;\n    address public seller;\n    address public arbiter;\n    uint public amount;\n    bool public isReleased;\n    bool public isCancelled;\n    \n    constructor(address _buyer, address _seller, address _arbiter) payable {\n        buyer = _buyer;\n        seller = _seller;\n        arbiter = _arbiter;\n        amount = msg.value;\n    }\n    \n    function releasePayment() public {\n        require(msg.sender == buyer || msg.sender == arbiter, "Only buyer or arbiter can release payment");\n        require(!isReleased, "Payment already released");\n        require(!isCancelled, "Contract cancelled");\n        \n        isReleased = true;\n        payable(seller).transfer(amount);\n    }\n    \n    function cancelContract() public {\n        require(msg.sender == buyer || msg.sender == arbiter, "Only buyer or arbiter can cancel");\n        require(!isReleased, "Payment already released");\n        require(!isCancelled, "Contract already cancelled");\n        \n        isCancelled = true;\n        payable(buyer).transfer(amount);\n    }\n}', '[{"inputs":[{"internalType":"address","name":"_buyer","type":"address"},{"internalType":"address","name":"_seller","type":"address"},{"internalType":"address","name":"_arbiter","type":"address"}],"stateMutability":"payable","type":"constructor"},{"inputs":[],"name":"amount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"arbiter","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"buyer","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"cancelContract","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"isCancelled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isReleased","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"releasePayment","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"seller","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]'::jsonb, '{"buyer": {"type": "address", "description": "Buyer address"}, "seller": {"type": "address", "description": "Seller address"}, "arbiter": {"type": "address", "description": "Arbiter address"}}'::jsonb, true, get_current_user_id()),

  ('NFT Collection', 'An ERC721 NFT collection contract', 'nft', 'pragma solidity ^0.8.0;\n\nimport "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";\nimport "@openzeppelin/contracts/access/Ownable.sol";\n\ncontract NFTCollection is ERC721Enumerable, Ownable {\n    using Strings for uint256;\n    \n    string public baseURI;\n    uint256 public maxSupply;\n    uint256 public price;\n    bool public saleIsActive = false;\n    \n    constructor(string memory _name, string memory _symbol, string memory _baseURI, uint256 _maxSupply, uint256 _price) ERC721(_name, _symbol) {\n        baseURI = _baseURI;\n        maxSupply = _maxSupply;\n        price = _price;\n    }\n    \n    function mint(uint256 numberOfTokens) public payable {\n        require(saleIsActive, "Sale is not active");\n        require(numberOfTokens <= 20, "Can only mint 20 tokens at a time");\n        require(totalSupply() + numberOfTokens <= maxSupply, "Purchase would exceed max supply");\n        require(price * numberOfTokens <= msg.value, "Ether value sent is not correct");\n        \n        for(uint256 i = 0; i < numberOfTokens; i++) {\n            uint256 tokenId = totalSupply() + 1;\n            _safeMint(msg.sender, tokenId);\n        }\n    }\n    \n    function _baseURI() internal view virtual override returns (string memory) {\n        return baseURI;\n    }\n    \n    function setBaseURI(string memory _newBaseURI) public onlyOwner {\n        baseURI = _newBaseURI;\n    }\n    \n    function setPrice(uint256 _newPrice) public onlyOwner {\n        price = _newPrice;\n    }\n    \n    function flipSaleState() public onlyOwner {\n        saleIsActive = !saleIsActive;\n    }\n    \n    function withdraw() public onlyOwner {\n        uint256 balance = address(this).balance;\n        payable(msg.sender).transfer(balance);\n    }\n}', '[{"inputs":[{"internalType":"string","name":"_name","type":"string"},{"internalType":"string","name":"_symbol","type":"string"},{"internalType":"string","name":"_baseURI","type":"string"},{"internalType":"uint256","name":"_maxSupply","type":"uint256"},{"internalType":"uint256","name":"_price","type":"uint256"}],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"approved","type":"address"},{"indexed":true,"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"operator","type":"address"},{"indexed":false,"internalType":"bool","name":"approved","type":"bool"}],"name":"ApprovalForAll","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":true,"internalType":"uint256","name":"tokenId","type":"uint256"}],"name":"Transfer","type":"event"}]'::jsonb, '{"name": {"type": "string", "description": "Collection name"}, "symbol": {"type": "string", "description": "Collection symbol"}, "baseURI": {"type": "string", "description": "Base URI for token metadata"}, "maxSupply": {"type": "uint256", "description": "Maximum supply of tokens"}, "price": {"type": "uint256", "description": "Price per token in wei"}}'::jsonb, true, get_current_user_id())
ON CONFLICT DO NOTHING;

-- Insert mock data for smart_contracts
INSERT INTO public.smart_contracts (name, description, contract_type, contract_address, chain_id, status, source_code, abi, document_id, owner_id, template_id)
VALUES
  ('My Token', 'My custom ERC20 token', 'token', '0x1234567890123456789012345678901234567890', 1, 'deployed', 'pragma solidity ^0.8.0;\n\nimport "@openzeppelin/contracts/token/ERC20/ERC20.sol";\n\ncontract MyToken is ERC20 {\n    constructor() ERC20("MyToken", "MTK") {\n        _mint(msg.sender, 1000000 * 10 ** decimals());\n    }\n}', '[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]'::jsonb, (SELECT id FROM public.documents WHERE title = 'Service Agreement' LIMIT 1), get_current_user_id(), (SELECT id FROM public.contract_templates WHERE name = 'Simple Token' LIMIT 1))
ON CONFLICT DO NOTHING;

-- Insert mock data for contract_signers
INSERT INTO public.contract_signers (contract_id, user_id, email, wallet_address, status)
VALUES
  ((SELECT id FROM public.smart_contracts WHERE name = 'My Token' LIMIT 1), get_current_user_id(), (SELECT email FROM public.profiles WHERE id = get_current_user_id() LIMIT 1), '******************************************', 'signed')
ON CONFLICT DO NOTHING;

-- Insert mock data for contract_events
INSERT INTO public.contract_events (contract_id, event_type, event_data, tx_hash, block_number, timestamp)
VALUES
  ((SELECT id FROM public.smart_contracts WHERE name = 'My Token' LIMIT 1), 'Deployment', '{"gas_used": 1500000, "gas_price": 20000000000}'::jsonb, '0x9876543210abcdef9876543210abcdef9876543210abcdef9876543210abcdef', 12345678, NOW())
ON CONFLICT DO NOTHING;

-- Insert mock data for projects
INSERT INTO public.projects (name, description, owner_id, status, due_date)
VALUES
  ('Contract Management System', 'A project to develop a contract management system', get_current_user_id(), 'active', (NOW() + INTERVAL '30 days')::timestamp),
  ('Legal Document Templates', 'Creating standardized legal document templates', get_current_user_id(), 'active', (NOW() + INTERVAL '15 days')::timestamp)
ON CONFLICT DO NOTHING;

-- Insert mock data for project_members
INSERT INTO public.project_members (project_id, user_id, role)
VALUES
  ((SELECT id FROM public.projects WHERE name = 'Contract Management System' LIMIT 1), get_current_user_id(), 'owner'),
  ((SELECT id FROM public.projects WHERE name = 'Legal Document Templates' LIMIT 1), get_current_user_id(), 'owner')
ON CONFLICT DO NOTHING;

-- Insert mock data for project_tasks
INSERT INTO public.project_tasks (project_id, title, description, status, priority, assigned_to, due_date, created_by)
VALUES
  ((SELECT id FROM public.projects WHERE name = 'Contract Management System' LIMIT 1), 'Design Database Schema', 'Create the database schema for the contract management system', 'completed', 'high', get_current_user_id(), (NOW() - INTERVAL '5 days')::timestamp, get_current_user_id()),

  ((SELECT id FROM public.projects WHERE name = 'Contract Management System' LIMIT 1), 'Implement User Authentication', 'Set up user authentication and authorization', 'in_progress', 'high', get_current_user_id(), (NOW() + INTERVAL '3 days')::timestamp, get_current_user_id()),

  ((SELECT id FROM public.projects WHERE name = 'Contract Management System' LIMIT 1), 'Create Contract Templates', 'Develop reusable contract templates', 'todo', 'medium', get_current_user_id(), (NOW() + INTERVAL '10 days')::timestamp, get_current_user_id()),

  ((SELECT id FROM public.projects WHERE name = 'Legal Document Templates' LIMIT 1), 'Research Legal Requirements', 'Research legal requirements for different jurisdictions', 'in_progress', 'medium', get_current_user_id(), (NOW() + INTERVAL '2 days')::timestamp, get_current_user_id()),

  ((SELECT id FROM public.projects WHERE name = 'Legal Document Templates' LIMIT 1), 'Draft Template Structure', 'Create the structure for legal document templates', 'todo', 'high', get_current_user_id(), (NOW() + INTERVAL '7 days')::timestamp, get_current_user_id())
ON CONFLICT DO NOTHING;

-- Insert mock data for wallet_connections
INSERT INTO public.wallet_connections (user_id, wallet_address, wallet_type, chain_id, is_primary)
VALUES
  (get_current_user_id(), '******************************************', 'MetaMask', 1, true),
  (get_current_user_id(), '******************************************', 'WalletConnect', 137, false)
ON CONFLICT DO NOTHING;

-- Insert mock data for subscriptions
INSERT INTO public.subscriptions (user_id, plan, status, current_period_start, current_period_end, cancel_at_period_end, payment_method)
VALUES
  (get_current_user_id(), 'Standard', 'active', (NOW() - INTERVAL '15 days')::timestamp, (NOW() + INTERVAL '15 days')::timestamp, false, 'credit_card')
ON CONFLICT DO NOTHING;
