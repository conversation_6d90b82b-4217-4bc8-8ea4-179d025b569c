'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet } from '@/components/ui/sheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDocuments } from '@/lib/hooks';
import { Folder, FolderTree, Hash, Plus, Tag } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { DocumentOrganizer } from '../organizer/DocumentOrganizer';

interface OrganizerSheetProps {
  isOpen: boolean;
  onClose: () => void;
  initialTab?: 'folders' | 'tags';
  onSelectFolder?: (folderId: string | null) => void;
  onSelectTag?: (tagId: string | null) => void;
}

export function OrganizerSheet({
  isOpen,
  onClose,
  initialTab = 'folders',
  onSelectFolder,
  onSelectTag,
}: OrganizerSheetProps) {
  const [activeTab, setActiveTab] = useState<string>(initialTab);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedTagId, setSelectedTagId] = useState<string | null>(null);
  const [newFolderName, setNewFolderName] = useState('');
  const [newTagName, setNewTagName] = useState('');
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);
  const [isCreatingTag, setIsCreatingTag] = useState(false);

  const { createFolder, createTag, fetchFolders, fetchTags, folders, tags } =
    useDocuments();

  // Fetch folders and tags when the sheet opens
  useEffect(() => {
    if (isOpen) {
      fetchFolders();
      fetchTags();
    }
  }, [isOpen, fetchFolders, fetchTags]);

  const handleSelectFolder = (folderId: string | null) => {
    setSelectedFolderId(folderId);
    if (onSelectFolder) {
      onSelectFolder(folderId);
    }
    onClose();
  };

  const handleSelectTag = (tagId: string | null) => {
    setSelectedTagId(tagId);
    if (onSelectTag) {
      onSelectTag(tagId);
    }
    onClose();
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error('Please enter a folder name');
      return;
    }

    setIsCreatingFolder(true);
    try {
      await createFolder({
        name: newFolderName.trim(),
        parent_id: null,
      });
      toast.success('Folder created successfully');
      setNewFolderName('');
      setIsCreatingFolder(false);
      fetchFolders();
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('Failed to create folder');
      setIsCreatingFolder(false);
    }
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) {
      toast.error('Please enter a tag name');
      return;
    }

    setIsCreatingTag(true);
    try {
      await createTag({
        name: newTagName.trim(),
      });
      toast.success('Tag created successfully');
      setNewTagName('');
      setIsCreatingTag(false);
      fetchTags();
    } catch (error) {
      console.error('Error creating tag:', error);
      toast.error('Failed to create tag');
      setIsCreatingTag(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-between">
          <Sheet.Title>Organize Documents</Sheet.Title>
        </div>

        <div className="py-6">
          <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="folders" className="flex items-center gap-2">
                <FolderTree className="h-4 w-4" />
                Folders
              </TabsTrigger>
              <TabsTrigger value="tags" className="flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Tags
              </TabsTrigger>
            </TabsList>

            <TabsContent value="folders" className="mt-6 space-y-4">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <Label htmlFor="new-folder">New Folder</Label>
                  <div className="flex items-center">
                    <Folder className="mr-2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="new-folder"
                      value={newFolderName}
                      onChange={(e) => setNewFolderName(e.target.value)}
                      placeholder="Enter folder name"
                      className="flex-1"
                    />
                  </div>
                </div>
                <Button
                  onClick={handleCreateFolder}
                  disabled={isCreatingFolder || !newFolderName.trim()}
                  size="sm"
                >
                  <Plus className="mr-1 h-4 w-4" />
                  Create
                </Button>
              </div>

              <div className="mt-4">
                <DocumentOrganizer
                  onSelectFolder={handleSelectFolder}
                  selectedFolderId={selectedFolderId}
                  activeTab="folders"
                />
              </div>
            </TabsContent>

            <TabsContent value="tags" className="mt-6 space-y-4">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <Label htmlFor="new-tag">New Tag</Label>
                  <div className="flex items-center">
                    <Hash className="mr-2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="new-tag"
                      value={newTagName}
                      onChange={(e) => setNewTagName(e.target.value)}
                      placeholder="Enter tag name"
                      className="flex-1"
                    />
                  </div>
                </div>
                <Button
                  onClick={handleCreateTag}
                  disabled={isCreatingTag || !newTagName.trim()}
                  size="sm"
                >
                  <Plus className="mr-1 h-4 w-4" />
                  Create
                </Button>
              </div>

              <div className="mt-4">
                <DocumentOrganizer
                  onSelectTag={handleSelectTag}
                  selectedTagId={selectedTagId}
                  activeTab="tags"
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="pt-4 border-t flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </Sheet>
  );
}
