import { exchangeCodeForTokens } from '@/lib/services/google-calendar';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => (await cookies()).getAll(),
          setAll: async (cookiesArray) => {
            const cookieStore = await cookies();
            for (const { name, value, options } of cookiesArray) {
              cookieStore.set(name, value, options);
            }
          },
        },
      }
    );

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the authorization code and state from the query parameters
    const code = request.nextUrl.searchParams.get('code');
    const stateParam = request.nextUrl.searchParams.get('state');

    if (!code || !stateParam) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Parse the state parameter
    let state;
    try {
      state = JSON.parse(Buffer.from(stateParam, 'base64').toString());
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid state parameter' },
        { status: 400 }
      );
    }

    // Verify that the user ID in the state matches the current user
    if (state.userId !== user.id) {
      return NextResponse.json({ error: 'User ID mismatch' }, { status: 403 });
    }

    // Exchange the authorization code for access and refresh tokens
    const { access_token, refresh_token, expires_in } =
      await exchangeCodeForTokens(code);

    // Calculate the token expiration date
    const tokenExpiresAt = new Date();
    tokenExpiresAt.setSeconds(tokenExpiresAt.getSeconds() + expires_in);

    // Store the tokens in the database
    const { error: insertError } = await supabase
      .from('calendar_integrations')
      .upsert(
        {
          user_id: user.id,
          provider: 'google',
          access_token,
          refresh_token,
          token_expires_at: tokenExpiresAt.toISOString(),
          sync_enabled: true,
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: 'user_id,provider',
        }
      );

    if (insertError) {
      console.error('Error storing Google Calendar tokens:', insertError);
      return NextResponse.json(
        { error: 'Failed to store Google Calendar tokens' },
        { status: 500 }
      );
    }

    // Redirect to the original redirect URL
    const redirectUrl = state.redirectUrl || '/';
    return NextResponse.redirect(new URL(redirectUrl, request.nextUrl.origin));
  } catch (error) {
    console.error('Error in Google Calendar callback route:', error);
    return NextResponse.json(
      { error: 'Failed to complete Google Calendar authorization' },
      { status: 500 }
    );
  }
}
