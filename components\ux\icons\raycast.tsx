import { SVGProps } from "react";

export function Raycast(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 176 177"
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="m175.88 88.5-9.17 9.18-34.79-34.79V44.54l43.96 43.96ZM88 .59l-9.17 9.18 34.78 34.78h18.35L88 .59ZM68.32 20.28l-9.18 9.17 15.1 15.1h18.35L68.32 20.28Zm63.64 63.64v18.35l15.1 15.1 9.18-9.17-24.28-24.28ZM126.67 118l5.25-5.26H63.69v-68.2l-5.26 5.26-9.84-9.8-9.17 9.18 9.84 9.84L44 64.23v10.51l-15.1-15.1-9.18 9.18L44 93.09v21L9.22 79.33 0 88.5l88 87.92 9.18-9.17-34.79-34.79h21l24.27 24.27 9.18-9.18-15.1-15.1h10.51l5.26-5.26 9.84 9.84 9.17-9.18-9.85-9.85Z"
        clipRule="evenodd"
      />
    </svg>
  );
}
