export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      collaboration_projects: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          user_id: string;
          status: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          user_id: string;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          user_id?: string;
          status?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'collaboration_projects_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      collaboration_project_members: {
        Row: {
          id: string;
          project_id: string;
          user_id: string;
          role: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          user_id: string;
          role?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          user_id?: string;
          role?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'collaboration_project_members_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'collaboration_projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_project_members_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      collaboration_tasks: {
        Row: {
          id: string;
          project_id: string;
          title: string;
          description: string | null;
          status: string;
          priority: string | null;
          due_date: string | null;
          created_by: string;
          assigned_to: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          title: string;
          description?: string | null;
          status?: string;
          priority?: string | null;
          due_date?: string | null;
          created_by: string;
          assigned_to?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          title?: string;
          description?: string | null;
          status?: string;
          priority?: string | null;
          due_date?: string | null;
          created_by?: string;
          assigned_to?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'collaboration_tasks_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'collaboration_projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_tasks_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_tasks_assigned_to_fkey';
            columns: ['assigned_to'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      collaboration_comments: {
        Row: {
          id: string;
          project_id: string;
          task_id: string | null;
          user_id: string;
          content: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          task_id?: string | null;
          user_id: string;
          content: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          task_id?: string | null;
          user_id?: string;
          content?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'collaboration_comments_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'collaboration_projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_comments_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'collaboration_tasks';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_comments_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      collaboration_documents: {
        Row: {
          id: string;
          project_id: string;
          title: string;
          description: string | null;
          file_path: string | null;
          file_type: string | null;
          file_size: number | null;
          created_by: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          title: string;
          description?: string | null;
          file_path?: string | null;
          file_type?: string | null;
          file_size?: number | null;
          created_by: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          title?: string;
          description?: string | null;
          file_path?: string | null;
          file_type?: string | null;
          file_size?: number | null;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'collaboration_documents_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'collaboration_projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'collaboration_documents_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      calendar_integrations: {
        Row: {
          access_token: string | null;
          calendar_id: string | null;
          created_at: string | null;
          id: string;
          provider: string;
          refresh_token: string | null;
          sync_enabled: boolean | null;
          token_expires_at: string | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          access_token?: string | null;
          calendar_id?: string | null;
          created_at?: string | null;
          id?: string;
          provider: string;
          refresh_token?: string | null;
          sync_enabled?: boolean | null;
          token_expires_at?: string | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          access_token?: string | null;
          calendar_id?: string | null;
          created_at?: string | null;
          id?: string;
          provider?: string;
          refresh_token?: string | null;
          sync_enabled?: boolean | null;
          token_expires_at?: string | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      client_metadata: {
        Row: {
          client_id: string;
          created_at: string | null;
          id: string;
          notes: string | null;
          status: string | null;
          updated_at: string | null;
        };
        Insert: {
          client_id: string;
          created_at?: string | null;
          id?: string;
          notes?: string | null;
          status?: string | null;
          updated_at?: string | null;
        };
        Update: {
          client_id?: string;
          created_at?: string | null;
          id?: string;
          notes?: string | null;
          status?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      client_notes: {
        Row: {
          client_id: string;
          content: string;
          created_at: string | null;
          id: string;
          is_pinned: boolean | null;
          lawyer_id: string;
          updated_at: string | null;
        };
        Insert: {
          client_id: string;
          content: string;
          created_at?: string | null;
          id?: string;
          is_pinned?: boolean | null;
          lawyer_id: string;
          updated_at?: string | null;
        };
        Update: {
          client_id?: string;
          content?: string;
          created_at?: string | null;
          id?: string;
          is_pinned?: boolean | null;
          lawyer_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'client_notes_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'client_notes_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      consultation_calendar_events: {
        Row: {
          consultation_id: string;
          created_at: string | null;
          external_event_id: string;
          id: string;
          last_synced_at: string | null;
          provider: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          consultation_id: string;
          created_at?: string | null;
          external_event_id: string;
          id?: string;
          last_synced_at?: string | null;
          provider: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          consultation_id?: string;
          created_at?: string | null;
          external_event_id?: string;
          id?: string;
          last_synced_at?: string | null;
          provider?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'consultation_calendar_events_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'consultation_metrics';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'consultation_calendar_events_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_consultations';
            referencedColumns: ['id'];
          },
        ];
      };
      consultation_report_settings: {
        Row: {
          created_at: string | null;
          email_recipients: string[] | null;
          id: string;
          include_metrics: string[] | null;
          is_enabled: boolean | null;
          report_type: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          email_recipients?: string[] | null;
          id?: string;
          include_metrics?: string[] | null;
          is_enabled?: boolean | null;
          report_type: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          email_recipients?: string[] | null;
          id?: string;
          include_metrics?: string[] | null;
          is_enabled?: boolean | null;
          report_type?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      document_activities: {
        Row: {
          activity_data: Json | null;
          activity_type: string;
          created_at: string;
          document_id: string;
          id: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          activity_data?: Json | null;
          activity_type: string;
          created_at?: string;
          document_id: string;
          id?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          activity_data?: Json | null;
          activity_type?: string;
          created_at?: string;
          document_id?: string;
          id?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_activities_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_activities_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_activities_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_attachments: {
        Row: {
          id: string;
          document_id: string;
          file_name: string;
          file_path: string;
          file_type: string;
          file_size: number;
          file_url: string;
          uploaded_by: string;
          created_at: string;
          updated_at: string;
          description: string | null;
          is_deleted: boolean;
        };
        Insert: {
          id?: string;
          document_id: string;
          file_name: string;
          file_path: string;
          file_type: string;
          file_size: number;
          file_url: string;
          uploaded_by: string;
          created_at?: string;
          updated_at?: string;
          description?: string | null;
          is_deleted?: boolean;
        };
        Update: {
          id?: string;
          document_id?: string;
          file_name?: string;
          file_path?: string;
          file_type?: string;
          file_size?: number;
          file_url?: string;
          uploaded_by?: string;
          created_at?: string;
          updated_at?: string;
          description?: string | null;
          is_deleted?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: 'document_attachments_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_attachments_uploaded_by_fkey';
            columns: ['uploaded_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_collaborations: {
        Row: {
          created_at: string;
          created_by: string | null;
          document_id: string;
          expires_at: string | null;
          id: string;
          permission: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          created_by?: string | null;
          document_id: string;
          expires_at?: string | null;
          id?: string;
          permission?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          created_by?: string | null;
          document_id?: string;
          expires_at?: string | null;
          id?: string;
          permission?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_collaborations_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_collaborations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_collaborations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_collaborations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_comments: {
        Row: {
          content: string;
          created_at: string;
          document_id: string;
          id: string;
          resolved: boolean;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          document_id: string;
          id?: string;
          resolved?: boolean;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          document_id?: string;
          id?: string;
          resolved?: boolean;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_comments_document_id_fkey_v2';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_comments_document_id_fkey_v2';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_comments_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_share_links: {
        Row: {
          access_count: number;
          access_pin: string | null;
          access_type: string;
          created_at: string;
          created_by: string;
          document_id: string;
          edit_password: string | null;
          expires_at: string | null;
          id: string;
          is_active: boolean;
          last_accessed_at: string | null;
          permission: string;
          token: string;
        };
        Insert: {
          access_count?: number;
          access_pin?: string | null;
          access_type?: string;
          created_at?: string;
          created_by: string;
          document_id: string;
          edit_password?: string | null;
          expires_at?: string | null;
          id?: string;
          is_active?: boolean;
          last_accessed_at?: string | null;
          permission?: string;
          token: string;
        };
        Update: {
          access_count?: number;
          access_pin?: string | null;
          access_type?: string;
          created_at?: string;
          created_by?: string;
          document_id?: string;
          edit_password?: string | null;
          expires_at?: string | null;
          id?: string;
          is_active?: boolean;
          last_accessed_at?: string | null;
          permission?: string;
          token?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_share_links_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_share_links_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_share_links_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
        ];
      };
      document_shares: {
        Row: {
          can_edit: boolean;
          created_at: string;
          document_id: string;
          id: string;
          user_id: string;
        };
        Insert: {
          can_edit?: boolean;
          created_at?: string;
          document_id: string;
          id?: string;
          user_id: string;
        };
        Update: {
          can_edit?: boolean;
          created_at?: string;
          document_id?: string;
          id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_shares_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_shares_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_shares_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_tags: {
        Row: {
          created_at: string;
          id: string;
          name: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          name: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          name?: string;
        };
        Relationships: [];
      };
      document_to_tags: {
        Row: {
          document_id: string;
          tag_id: string;
        };
        Insert: {
          document_id: string;
          tag_id: string;
        };
        Update: {
          document_id?: string;
          tag_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'document_to_tags_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_to_tags_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_to_tags_tag_id_fkey';
            columns: ['tag_id'];
            isOneToOne: false;
            referencedRelation: 'document_tags';
            referencedColumns: ['id'];
          },
        ];
      };
      document_versions: {
        Row: {
          change_summary: string | null;
          content: Json;
          created_at: string;
          created_by: string;
          document_id: string;
          id: string;
          version: number;
        };
        Insert: {
          change_summary?: string | null;
          content: Json;
          created_at?: string;
          created_by: string;
          document_id: string;
          id?: string;
          version: number;
        };
        Update: {
          change_summary?: string | null;
          content?: Json;
          created_at?: string;
          created_by?: string;
          document_id?: string;
          id?: string;
          version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'document_versions_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_versions_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'document_versions_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
        ];
      };
      documents: {
        Row: {
          content: Json | null;
          created_at: string;
          description: string | null;
          document_type: string;
          file_size: number | null;
          file_type: string | null;
          file_url: string | null;
          id: string;
          is_template: boolean;
          metadata: Json | null;
          owner_id: string;
          shared_with: Json | null;
          status: string;
          template_id: string | null;
          title: string;
          updated_at: string;
          version: number;
        };
        Insert: {
          content?: Json | null;
          created_at?: string;
          description?: string | null;
          document_type: string;
          file_size?: number | null;
          file_type?: string | null;
          file_url?: string | null;
          id?: string;
          is_template?: boolean;
          metadata?: Json | null;
          owner_id: string;
          shared_with?: Json | null;
          status?: string;
          template_id?: string | null;
          title: string;
          updated_at?: string;
          version?: number;
        };
        Update: {
          content?: Json | null;
          created_at?: string;
          description?: string | null;
          document_type?: string;
          file_size?: number | null;
          file_type?: string | null;
          file_url?: string | null;
          id?: string;
          is_template?: boolean;
          metadata?: Json | null;
          owner_id?: string;
          shared_with?: Json | null;
          status?: string;
          template_id?: string | null;
          title?: string;
          updated_at?: string;
          version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'documents_owner_id_fkey';
            columns: ['owner_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'documents_template_id_fkey';
            columns: ['template_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'documents_template_id_fkey';
            columns: ['template_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_availability: {
        Row: {
          created_at: string | null;
          day_of_week: number;
          end_time: string;
          id: string;
          is_available: boolean | null;
          lawyer_id: string;
          start_time: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          day_of_week: number;
          end_time: string;
          id?: string;
          is_available?: boolean | null;
          lawyer_id: string;
          start_time: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          day_of_week?: number;
          end_time?: string;
          id?: string;
          is_available?: boolean | null;
          lawyer_id?: string;
          start_time?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_availability_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_availability_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_availability_exceptions: {
        Row: {
          created_at: string | null;
          end_time: string | null;
          exception_date: string;
          id: string;
          is_available: boolean | null;
          lawyer_id: string;
          reason: string | null;
          start_time: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          end_time?: string | null;
          exception_date: string;
          id?: string;
          is_available?: boolean | null;
          lawyer_id: string;
          reason?: string | null;
          start_time?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          end_time?: string | null;
          exception_date?: string;
          id?: string;
          is_available?: boolean | null;
          lawyer_id?: string;
          reason?: string | null;
          start_time?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_availability_exceptions_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_availability_exceptions_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_consultation_messages: {
        Row: {
          attachment_name: string | null;
          attachment_type: string | null;
          attachment_url: string | null;
          consultation_id: string;
          created_at: string | null;
          has_attachment: boolean | null;
          id: string;
          is_read: boolean | null;
          message: string;
          sender_id: string;
          updated_at: string | null;
        };
        Insert: {
          attachment_name?: string | null;
          attachment_type?: string | null;
          attachment_url?: string | null;
          consultation_id: string;
          created_at?: string | null;
          has_attachment?: boolean | null;
          id?: string;
          is_read?: boolean | null;
          message: string;
          sender_id: string;
          updated_at?: string | null;
        };
        Update: {
          attachment_name?: string | null;
          attachment_type?: string | null;
          attachment_url?: string | null;
          consultation_id?: string;
          created_at?: string | null;
          has_attachment?: boolean | null;
          id?: string;
          is_read?: boolean | null;
          message?: string;
          sender_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultation_messages_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'consultation_metrics';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultation_messages_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_consultations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultation_messages_sender_id_fkey';
            columns: ['sender_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_consultations: {
        Row: {
          consultation_date: string | null;
          consultation_type: string | null;
          created_at: string;
          document_id: string | null;
          duration_minutes: number | null;
          feedback_provided: boolean | null;
          id: string;
          lawyer_id: string;
          meeting_link: string | null;
          notes: string | null;
          payment_amount: number | null;
          payment_status: string | null;
          recurring_consultation_id: string | null;
          status: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          consultation_date?: string | null;
          consultation_type?: string | null;
          created_at?: string;
          document_id?: string | null;
          duration_minutes?: number | null;
          feedback_provided?: boolean | null;
          id?: string;
          lawyer_id: string;
          meeting_link?: string | null;
          notes?: string | null;
          payment_amount?: number | null;
          payment_status?: string | null;
          recurring_consultation_id?: string | null;
          status?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          consultation_date?: string | null;
          consultation_type?: string | null;
          created_at?: string;
          document_id?: string | null;
          duration_minutes?: number | null;
          feedback_provided?: boolean | null;
          id?: string;
          lawyer_id?: string;
          meeting_link?: string | null;
          notes?: string | null;
          payment_amount?: number | null;
          payment_status?: string | null;
          recurring_consultation_id?: string | null;
          status?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_recurring_consultation_id_fkey';
            columns: ['recurring_consultation_id'];
            isOneToOne: false;
            referencedRelation: 'recurring_consultations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_document_reviews: {
        Row: {
          consultation_id: string | null;
          created_at: string | null;
          document_id: string;
          feedback: string | null;
          id: string;
          lawyer_id: string;
          rating: number | null;
          review_notes: string | null;
          status: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          consultation_id?: string | null;
          created_at?: string | null;
          document_id: string;
          feedback?: string | null;
          id?: string;
          lawyer_id: string;
          rating?: number | null;
          review_notes?: string | null;
          status?: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          consultation_id?: string | null;
          created_at?: string | null;
          document_id?: string;
          feedback?: string | null;
          id?: string;
          lawyer_id?: string;
          rating?: number | null;
          review_notes?: string | null;
          status?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_document_reviews_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'consultation_metrics';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_consultations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_document_reviews_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_reviews: {
        Row: {
          consultation_id: string | null;
          created_at: string;
          id: string;
          is_anonymous: boolean | null;
          lawyer_id: string;
          rating: number;
          review_text: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          consultation_id?: string | null;
          created_at?: string;
          id?: string;
          is_anonymous?: boolean | null;
          lawyer_id: string;
          rating: number;
          review_text?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          consultation_id?: string | null;
          created_at?: string;
          id?: string;
          is_anonymous?: boolean | null;
          lawyer_id?: string;
          rating?: number;
          review_text?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_reviews_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'consultation_metrics';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_reviews_consultation_id_fkey';
            columns: ['consultation_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_consultations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_reviews_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_reviews_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_reviews_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyers: {
        Row: {
          availability: Json | null;
          avatar_url: string | null;
          average_rating: number | null;
          bio: string | null;
          consultation_count: number | null;
          consultation_duration_options: number[] | null;
          consultation_fee: number | null;
          created_at: string;
          education: string | null;
          email: string;
          full_name: string;
          hourly_rate: number | null;
          id: string;
          is_verified: boolean | null;
          languages: string[] | null;
          practice_areas: string[] | null;
          profile_complete: boolean | null;
          profile_image_url: string | null;
          rating: number | null;
          specialization: string[];
          status: string | null;
          updated_at: string;
          user_id: string;
          years_experience: number | null;
        };
        Insert: {
          availability?: Json | null;
          avatar_url?: string | null;
          average_rating?: number | null;
          bio?: string | null;
          consultation_count?: number | null;
          consultation_duration_options?: number[] | null;
          consultation_fee?: number | null;
          created_at?: string;
          education?: string | null;
          email: string;
          full_name: string;
          hourly_rate?: number | null;
          id?: string;
          is_verified?: boolean | null;
          languages?: string[] | null;
          practice_areas?: string[] | null;
          profile_complete?: boolean | null;
          profile_image_url?: string | null;
          rating?: number | null;
          specialization: string[];
          status?: string | null;
          updated_at?: string;
          user_id: string;
          years_experience?: number | null;
        };
        Update: {
          availability?: Json | null;
          avatar_url?: string | null;
          average_rating?: number | null;
          bio?: string | null;
          consultation_count?: number | null;
          consultation_duration_options?: number[] | null;
          consultation_fee?: number | null;
          created_at?: string;
          education?: string | null;
          email?: string;
          full_name?: string;
          hourly_rate?: number | null;
          id?: string;
          is_verified?: boolean | null;
          languages?: string[] | null;
          practice_areas?: string[] | null;
          profile_complete?: boolean | null;
          profile_image_url?: string | null;
          rating?: number | null;
          specialization?: string[];
          status?: string | null;
          updated_at?: string;
          user_id?: string;
          years_experience?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyers_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      notifications: {
        Row: {
          action_url: string | null;
          content: string;
          created_at: string;
          id: string;
          read: boolean;
          related_id: string | null;
          related_type: string | null;
          title: string;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          action_url?: string | null;
          content: string;
          created_at?: string;
          id?: string;
          read?: boolean;
          related_id?: string | null;
          related_type?: string | null;
          title: string;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          action_url?: string | null;
          content?: string;
          created_at?: string;
          id?: string;
          read?: boolean;
          related_id?: string | null;
          related_type?: string | null;
          title?: string;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'notifications_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      organization_members: {
        Row: {
          created_at: string;
          id: string;
          organization_id: string;
          role: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          organization_id: string;
          role?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          organization_id?: string;
          role?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'organization_members_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
        ];
      };
      organizations: {
        Row: {
          billing_info: Json | null;
          contact_email: string | null;
          created_at: string;
          description: string | null;
          features: Json | null;
          id: string;
          logo_url: string | null;
          name: string;
          primary_color: string | null;
          secondary_color: string | null;
          subscription_level: string;
          updated_at: string;
        };
        Insert: {
          billing_info?: Json | null;
          contact_email?: string | null;
          created_at?: string;
          description?: string | null;
          features?: Json | null;
          id?: string;
          logo_url?: string | null;
          name: string;
          primary_color?: string | null;
          secondary_color?: string | null;
          subscription_level?: string;
          updated_at?: string;
        };
        Update: {
          billing_info?: Json | null;
          contact_email?: string | null;
          created_at?: string;
          description?: string | null;
          features?: Json | null;
          id?: string;
          logo_url?: string | null;
          name?: string;
          primary_color?: string | null;
          secondary_color?: string | null;
          subscription_level?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          email: string | null;
          full_name: string | null;
          id: string;
          is_onboarded: boolean;
          phone_number: string | null;
          plan: Database['public']['Enums']['plan_new'] | null;
          updated_at: string | null;
          user_role: Database['public']['Enums']['user_role'] | null;
          username: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          is_onboarded?: boolean;
          phone_number?: string | null;
          plan?: Database['public']['Enums']['plan_new'] | null;
          updated_at?: string | null;
          user_role?: Database['public']['Enums']['user_role'] | null;
          username?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          is_onboarded?: boolean;
          phone_number?: string | null;
          plan?: Database['public']['Enums']['plan_new'] | null;
          updated_at?: string | null;
          user_role?: Database['public']['Enums']['user_role'] | null;
          username?: string | null;
        };
        Relationships: [];
      };
      project_comments: {
        Row: {
          content: string;
          created_at: string;
          id: string;
          project_id: string;
          task_id: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          id?: string;
          project_id: string;
          task_id?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          id?: string;
          project_id?: string;
          task_id?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_comments_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_comments_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'project_tasks';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_comments_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      project_documents: {
        Row: {
          added_by: string | null;
          created_at: string;
          document_id: string;
          id: string;
          project_id: string;
          updated_at: string;
        };
        Insert: {
          added_by?: string | null;
          created_at?: string;
          document_id: string;
          id?: string;
          project_id: string;
          updated_at?: string;
        };
        Update: {
          added_by?: string | null;
          created_at?: string;
          document_id?: string;
          id?: string;
          project_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_documents_added_by_fkey';
            columns: ['added_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_documents_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_documents_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_documents_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      project_invitations: {
        Row: {
          created_at: string;
          email: string;
          id: string;
          invited_by: string;
          message: string | null;
          project_id: string;
          role: string;
          status: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          email: string;
          id?: string;
          invited_by: string;
          message?: string | null;
          project_id: string;
          role?: string;
          status?: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          email?: string;
          id?: string;
          invited_by?: string;
          message?: string | null;
          project_id?: string;
          role?: string;
          status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_invitations_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      project_members: {
        Row: {
          created_at: string;
          id: string;
          project_id: string;
          role: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          project_id: string;
          role?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          project_id?: string;
          role?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_members_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_members_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      project_messages: {
        Row: {
          content: string;
          created_at: string;
          id: string;
          project_id: string | null;
          user_id: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          id?: string;
          project_id?: string | null;
          user_id: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          id?: string;
          project_id?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_messages_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_messages_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      project_tasks: {
        Row: {
          assigned_to: string | null;
          created_at: string;
          created_by: string;
          description: string | null;
          due_date: string | null;
          id: string;
          priority: string;
          project_id: string;
          status: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          assigned_to?: string | null;
          created_at?: string;
          created_by: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority?: string;
          project_id: string;
          status?: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          assigned_to?: string | null;
          created_at?: string;
          created_by?: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority?: string;
          project_id?: string;
          status?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_tasks_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_tasks_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      projects: {
        Row: {
          created_at: string;
          description: string | null;
          end_date: string | null;
          id: string;
          is_private: boolean | null;
          name: string;
          owner_id: string;
          start_date: string | null;
          status: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          end_date?: string | null;
          id?: string;
          is_private?: boolean | null;
          name: string;
          owner_id: string;
          start_date?: string | null;
          status?: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          end_date?: string | null;
          id?: string;
          is_private?: boolean | null;
          name?: string;
          owner_id?: string;
          start_date?: string | null;
          status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'projects_owner_id_fkey';
            columns: ['owner_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      push_subscriptions: {
        Row: {
          created_at: string | null;
          id: string;
          subscription: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          subscription: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          subscription?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      recurring_consultations: {
        Row: {
          consultation_type: string;
          created_at: string | null;
          day_of_week: number;
          description: string | null;
          document_id: string | null;
          duration_minutes: number;
          end_date: string | null;
          frequency: string;
          id: string;
          is_active: boolean;
          lawyer_id: string;
          location: string | null;
          start_date: string;
          start_time: string;
          title: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          consultation_type?: string;
          created_at?: string | null;
          day_of_week: number;
          description?: string | null;
          document_id?: string | null;
          duration_minutes: number;
          end_date?: string | null;
          frequency: string;
          id?: string;
          is_active?: boolean;
          lawyer_id: string;
          location?: string | null;
          start_date: string;
          start_time: string;
          title: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          consultation_type?: string;
          created_at?: string | null;
          day_of_week?: number;
          description?: string | null;
          document_id?: string | null;
          duration_minutes?: number;
          end_date?: string | null;
          frequency?: string;
          id?: string;
          is_active?: boolean;
          lawyer_id?: string;
          location?: string | null;
          start_date?: string;
          start_time?: string;
          title?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'recurring_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'recurring_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'recurring_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'recurring_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      roles: {
        Row: {
          created_at: string | null;
          id: number;
          name: Database['public']['Enums']['user_role'];
          permissions: Database['public']['Enums']['permission'][];
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: never;
          name: Database['public']['Enums']['user_role'];
          permissions: Database['public']['Enums']['permission'][];
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: never;
          name?: Database['public']['Enums']['user_role'];
          permissions?: Database['public']['Enums']['permission'][];
          updated_at?: string | null;
        };
        Relationships: [];
      };
      saved_reports: {
        Row: {
          created_at: string | null;
          id: string;
          report_data: Json | null;
          report_name: string;
          report_parameters: Json | null;
          report_type: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          report_data?: Json | null;
          report_name: string;
          report_parameters?: Json | null;
          report_type: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          report_data?: Json | null;
          report_name?: string;
          report_parameters?: Json | null;
          report_type?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      subscriptions: {
        Row: {
          auto_renew: boolean;
          created_at: string;
          end_date: string | null;
          id: string;
          payment_id: string | null;
          payment_method: Json | null;
          plan: string;
          start_date: string;
          status: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          auto_renew?: boolean;
          created_at?: string;
          end_date?: string | null;
          id?: string;
          payment_id?: string | null;
          payment_method?: Json | null;
          plan?: string;
          start_date?: string;
          status?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          auto_renew?: boolean;
          created_at?: string;
          end_date?: string | null;
          id?: string;
          payment_id?: string | null;
          payment_method?: Json | null;
          plan?: string;
          start_date?: string;
          status?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'subscriptions_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      tasks: {
        Row: {
          assignee_id: string | null;
          created_at: string;
          description: string | null;
          due_date: string | null;
          id: string;
          priority: string;
          project_id: string | null;
          status: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          assignee_id?: string | null;
          created_at?: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority?: string;
          project_id?: string | null;
          status?: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          assignee_id?: string | null;
          created_at?: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority?: string;
          project_id?: string | null;
          status?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tasks_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      team_members: {
        Row: {
          id: string;
          joined_at: string;
          role: string;
          team_id: string;
          user_id: string;
        };
        Insert: {
          id?: string;
          joined_at?: string;
          role?: string;
          team_id: string;
          user_id: string;
        };
        Update: {
          id?: string;
          joined_at?: string;
          role?: string;
          team_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'team_members_team_id_fkey';
            columns: ['team_id'];
            isOneToOne: false;
            referencedRelation: 'teams';
            referencedColumns: ['id'];
          },
        ];
      };
      teams: {
        Row: {
          created_at: string;
          description: string | null;
          id: string;
          name: string;
          organization_id: string;
          permissions: Json | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: string;
          name: string;
          organization_id: string;
          permissions?: Json | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: string;
          name?: string;
          organization_id?: string;
          permissions?: Json | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'teams_organization_id_fkey';
            columns: ['organization_id'];
            isOneToOne: false;
            referencedRelation: 'organizations';
            referencedColumns: ['id'];
          },
        ];
      };
      templates: {
        Row: {
          category: string | null;
          content: Json;
          created_at: string;
          created_by: string | null;
          description: string | null;
          document_type: string;
          id: string;
          is_global: boolean;
          thumbnail_url: string | null;
          title: string;
          updated_at: string;
        };
        Insert: {
          category?: string | null;
          content?: Json;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          document_type: string;
          id?: string;
          is_global?: boolean;
          thumbnail_url?: string | null;
          title: string;
          updated_at?: string;
        };
        Update: {
          category?: string | null;
          content?: Json;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          document_type?: string;
          id?: string;
          is_global?: boolean;
          thumbnail_url?: string | null;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'templates_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      usage_limits: {
        Row: {
          collaborators_limit: number | null;
          created_at: string;
          document_limit: number | null;
          features: Json | null;
          id: string;
          lawyer_consultations_limit: number | null;
          plan: string;
          storage_limit: number | null;
          updated_at: string;
        };
        Insert: {
          collaborators_limit?: number | null;
          created_at?: string;
          document_limit?: number | null;
          features?: Json | null;
          id?: string;
          lawyer_consultations_limit?: number | null;
          plan: string;
          storage_limit?: number | null;
          updated_at?: string;
        };
        Update: {
          collaborators_limit?: number | null;
          created_at?: string;
          document_limit?: number | null;
          features?: Json | null;
          id?: string;
          lawyer_consultations_limit?: number | null;
          plan?: string;
          storage_limit?: number | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      user_settings: {
        Row: {
          created_at: string;
          display_preferences: Json | null;
          email_notifications: Json | null;
          export_preferences: Json | null;
          id: string;
          language: string;
          notifications_enabled: boolean;
          security_preferences: Json | null;
          theme: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          display_preferences?: Json | null;
          email_notifications?: Json | null;
          export_preferences?: Json | null;
          id?: string;
          language?: string;
          notifications_enabled?: boolean;
          security_preferences?: Json | null;
          theme?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          display_preferences?: Json | null;
          email_notifications?: Json | null;
          export_preferences?: Json | null;
          id?: string;
          language?: string;
          notifications_enabled?: boolean;
          security_preferences?: Json | null;
          theme?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_settings_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      user_usage: {
        Row: {
          collaborators_count: number;
          created_at: string;
          documents_count: number;
          id: string;
          lawyer_consultations_count: number;
          storage_used: number;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          collaborators_count?: number;
          created_at?: string;
          documents_count?: number;
          id?: string;
          lawyer_consultations_count?: number;
          storage_used?: number;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          collaborators_count?: number;
          created_at?: string;
          documents_count?: number;
          id?: string;
          lawyer_consultations_count?: number;
          storage_used?: number;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_usage_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: true;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      wallet_connections: {
        Row: {
          chain_id: number;
          created_at: string;
          id: string;
          is_primary: boolean;
          last_used: string | null;
          updated_at: string;
          user_id: string;
          wallet_address: string;
          wallet_type: string;
        };
        Insert: {
          chain_id: number;
          created_at?: string;
          id?: string;
          is_primary?: boolean;
          last_used?: string | null;
          updated_at?: string;
          user_id: string;
          wallet_address: string;
          wallet_type: string;
        };
        Update: {
          chain_id?: number;
          created_at?: string;
          id?: string;
          is_primary?: boolean;
          last_used?: string | null;
          updated_at?: string;
          user_id?: string;
          wallet_address?: string;
          wallet_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'wallet_connections_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      waitlist: {
        Row: {
          created_at: string;
          email: string;
          full_name: string;
          id: string;
          role: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          email: string;
          full_name: string;
          id?: string;
          role: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          email?: string;
          full_name?: string;
          id?: string;
          role?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      client_consultation_metrics: {
        Row: {
          avg_duration_minutes: number | null;
          cancelled_consultations: number | null;
          client_email: string | null;
          client_id: string | null;
          client_name: string | null;
          completed_consultations: number | null;
          document_consultations: number | null;
          recurring_consultations: number | null;
          total_consultations: number | null;
          total_minutes: number | null;
          upcoming_consultations: number | null;
          video_consultations: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultations_user_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      consultation_metrics: {
        Row: {
          client_email: string | null;
          client_name: string | null;
          consultation_date: string | null;
          consultation_type: string | null;
          created_at: string | null;
          day_of_week: number | null;
          document_id: string | null;
          duration_minutes: number | null;
          hour: number | null;
          id: string | null;
          is_recurring: boolean | null;
          lawyer_id: string | null;
          lawyer_name: string | null;
          lawyer_specialization: string[] | null;
          month: number | null;
          recurring_consultation_id: string | null;
          status: string | null;
          updated_at: string | null;
          user_id: string | null;
          year: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'document_summaries';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_document_id_fkey';
            columns: ['document_id'];
            isOneToOne: false;
            referencedRelation: 'documents';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_recurring_consultation_id_fkey';
            columns: ['recurring_consultation_id'];
            isOneToOne: false;
            referencedRelation: 'recurring_consultations';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      document_summaries: {
        Row: {
          created_at: string | null;
          description: string | null;
          document_type: string | null;
          id: string | null;
          is_template: boolean | null;
          owner_full_name: string | null;
          owner_id: string | null;
          owner_username: string | null;
          status: string | null;
          tags: Json | null;
          title: string | null;
          updated_at: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'documents_owner_id_fkey';
            columns: ['owner_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_dashboard_stats: {
        Row: {
          average_rating: number | null;
          completed_consultations: number | null;
          full_name: string | null;
          lawyer_id: string | null;
          total_consultations: number | null;
          total_earnings: number | null;
          total_reviews: number | null;
          upcoming_consultations: number | null;
          user_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyers_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      lawyer_performance_metrics: {
        Row: {
          avg_duration_minutes: number | null;
          cancelled_consultations: number | null;
          completed_consultations: number | null;
          document_consultations: number | null;
          lawyer_id: string | null;
          lawyer_name: string | null;
          lawyer_specialization: string[] | null;
          recurring_consultations: number | null;
          total_consultations: number | null;
          total_minutes: number | null;
          upcoming_consultations: number | null;
          video_consultations: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      monthly_consultation_stats: {
        Row: {
          cancelled_consultations: number | null;
          completed_consultations: number | null;
          completed_minutes: number | null;
          lawyer_id: string | null;
          lawyer_name: string | null;
          month: number | null;
          total_consultations: number | null;
          total_minutes: number | null;
          year: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyer_dashboard_stats';
            referencedColumns: ['lawyer_id'];
          },
          {
            foreignKeyName: 'lawyer_consultations_lawyer_id_fkey';
            columns: ['lawyer_id'];
            isOneToOne: false;
            referencedRelation: 'lawyers';
            referencedColumns: ['id'];
          },
        ];
      };
      time_slot_popularity: {
        Row: {
          cancelled_count: number | null;
          completed_count: number | null;
          consultation_count: number | null;
          day_of_week: number | null;
          hour: number | null;
        };
        Relationships: [];
      };
    };
    Functions: {
      add_client_note: {
        Args: {
          client_uuid: string;
          lawyer_uuid: string;
          note_content: string;
          is_note_pinned?: boolean;
        };
        Returns: string;
      };
      add_contract_signer: {
        Args: {
          contract_uuid: string;
          signer_email: string;
          wallet_address?: string;
        };
        Returns: string;
      };
      add_document_activity: {
        Args: {
          document_uuid: string;
          activity_type: string;
          activity_data?: Json;
        };
        Returns: string;
      };
      add_document_to_project: {
        Args: { project_uuid: string; document_uuid: string };
        Returns: string;
      };
      add_lawyer_review: {
        Args: {
          lawyer_uuid: string;
          consultation_uuid: string;
          rating: number;
          review_text?: string;
        };
        Returns: string;
      };
      add_project_member: {
        Args: {
          project_uuid: string;
          user_email: string;
          member_role?: string;
        };
        Returns: string;
      };
      cancel_future_recurring_consultations: {
        Args: { recurring_id: string; from_date?: string };
        Returns: number;
      };
      create_document_from_template: {
        Args: {
          template_id: string;
          user_id: string;
          title?: string;
          description?: string;
        };
        Returns: string;
      };
      create_document_notification: {
        Args:
          | {
              p_user_id: string;
              p_document_id: string;
              p_action: string;
              p_actor_id?: string;
            }
          | {
              p_user_id: string;
              p_document_id: string;
              p_title: string;
              p_content: string;
              p_action_url?: string;
            };
        Returns: string;
      };
      create_notification: {
        Args: {
          p_user_id: string;
          p_title: string;
          p_content: string;
          p_type: string;
          p_action_url?: string;
          p_related_id?: string;
          p_related_type?: string;
        };
        Returns: string;
      };
      create_project_notification: {
        Args: {
          p_user_id: string;
          p_project_id: string;
          p_action: string;
          p_actor_id?: string;
        };
        Returns: string;
      };
      generate_lawyer_performance_report: {
        Args: { lawyer_uuid: string; start_date?: string; end_date?: string };
        Returns: Json;
      };
      generate_recurring_consultations: {
        Args: { recurring_id: string; generate_until_date?: string };
        Returns: {
          consultation_id: string;
          consultation_date: string;
        }[];
      };
      get_available_lawyers: {
        Args: { specialization_filter?: string[]; min_rating?: number };
        Returns: {
          availability: Json | null;
          avatar_url: string | null;
          average_rating: number | null;
          bio: string | null;
          consultation_count: number | null;
          consultation_duration_options: number[] | null;
          consultation_fee: number | null;
          created_at: string;
          education: string | null;
          email: string;
          full_name: string;
          hourly_rate: number | null;
          id: string;
          is_verified: boolean | null;
          languages: string[] | null;
          practice_areas: string[] | null;
          profile_complete: boolean | null;
          profile_image_url: string | null;
          rating: number | null;
          specialization: string[];
          status: string | null;
          updated_at: string;
          user_id: string;
          years_experience: number | null;
        }[];
      };
      get_client_metadata: {
        Args: { client_uuid: string };
        Returns: {
          id: string;
          client_id: string;
          status: string;
          notes: string;
          created_at: string;
          updated_at: string;
        }[];
      };
      get_client_notes: {
        Args: { client_uuid: string; lawyer_uuid: string };
        Returns: {
          id: string;
          client_id: string;
          lawyer_id: string;
          content: string;
          is_pinned: boolean;
          created_at: string;
          updated_at: string;
        }[];
      };
      get_document_activities: {
        Args: { document_uuid: string; limit_count?: number };
        Returns: {
          id: string;
          document_id: string;
          user_id: string;
          activity_type: string;
          activity_data: Json;
          created_at: string;
          user_full_name: string;
          user_email: string;
          user_avatar_url: string;
        }[];
      };
      get_document_collaborators: {
        Args: { document_uuid: string };
        Returns: {
          id: string;
          email: string;
          full_name: string;
          can_edit: boolean;
          created_at: string;
        }[];
      };
      get_document_comments: {
        Args: { document_uuid: string };
        Returns: Json[];
      };
      get_document_versions: {
        Args: { document_uuid: string };
        Returns: {
          id: string;
          document_id: string;
          version: number;
          content: Json;
          created_at: string;
          created_by: string;
          created_by_name: string;
          change_summary: string;
        }[];
      };
      get_documents_shared_with_user: {
        Args: { user_uuid: string };
        Returns: {
          content: Json | null;
          created_at: string;
          description: string | null;
          document_type: string;
          file_size: number | null;
          file_type: string | null;
          file_url: string | null;
          id: string;
          is_template: boolean;
          metadata: Json | null;
          owner_id: string;
          shared_with: Json | null;
          status: string;
          template_id: string | null;
          title: string;
          updated_at: string;
          version: number;
        }[];
      };
      get_lawyer_upcoming_consultations: {
        Args: { lawyer_uuid: string };
        Returns: {
          id: string;
          client_name: string;
          client_email: string;
          consultation_date: string;
          duration_minutes: number;
          status: string;
          document_id: string;
          document_title: string;
          notes: string;
        }[];
      };
      get_project_members: {
        Args: { project_id_param: string };
        Returns: {
          id: string;
          project_id: string;
          user_id: string;
          role: string;
          created_at: string;
          updated_at: string;
          full_name: string;
          email: string;
          avatar_url: string;
        }[];
      };
      get_share_link_stats: {
        Args: { document_id_param: string };
        Returns: {
          id: string;
          token: string;
          permission: string;
          created_at: string;
          expires_at: string;
          is_active: boolean;
          access_count: number;
          last_accessed_at: string;
          access_type: string;
        }[];
      };
      get_user_calendar_integrations: {
        Args: { user_uuid: string };
        Returns: {
          id: string;
          user_id: string;
          provider: string;
          access_token: string;
          refresh_token: string;
          token_expires_at: string;
          calendar_id: string;
          sync_enabled: boolean;
          created_at: string;
          updated_at: string;
        }[];
      };
      get_user_lawyer_profile: {
        Args: { user_uuid: string };
        Returns: {
          id: string;
          full_name: string;
          email: string;
          specialization: string[];
          bio: string;
          years_experience: number;
          hourly_rate: number;
          consultation_fee: number;
          availability: Json;
          avatar_url: string;
          is_verified: boolean;
          status: string;
          profile_complete: boolean;
          average_rating: number;
          consultation_count: number;
        }[];
      };
      get_user_projects: {
        Args: Record<PropertyKey, never>;
        Returns: {
          id: string;
          name: string;
          description: string;
          owner_id: string;
          status: string;
          start_date: string;
          end_date: string;
          created_at: string;
          updated_at: string;
          is_private: boolean;
          membercount: number;
        }[];
      };
      get_user_settings: {
        Args: Record<PropertyKey, never>;
        Returns: {
          created_at: string;
          display_preferences: Json | null;
          email_notifications: Json | null;
          export_preferences: Json | null;
          id: string;
          language: string;
          notifications_enabled: boolean;
          security_preferences: Json | null;
          theme: string;
          updated_at: string;
          user_id: string;
        };
      };
      get_user_subscription: {
        Args: Record<PropertyKey, never>;
        Returns: {
          subscription: Database['public']['Tables']['subscriptions']['Row'];
          limits: Database['public']['Tables']['usage_limits']['Row'];
          usage: Database['public']['Tables']['user_usage']['Row'];
        }[];
      };
      hash_password: {
        Args: { password: string };
        Returns: string;
      };
      is_organization_admin: {
        Args: { org_id: string; auth_user_id: string };
        Returns: boolean;
      };
      is_organization_member: {
        Args: { org_id: string; auth_user_id: string };
        Returns: boolean;
      };
      is_team_member: {
        Args: { team_id_param: string; auth_user_id: string };
        Returns: boolean;
      };
      mark_all_notifications_read: {
        Args: Record<PropertyKey, never> | { p_user_id: string };
        Returns: undefined;
      };
      mark_notification_as_read: {
        Args: { notification_id: string };
        Returns: boolean;
      };
      mark_notification_read: {
        Args: { p_notification_id: string };
        Returns: boolean;
      };
      remove_project_member: {
        Args: { member_uuid: string };
        Returns: boolean;
      };
      restore_document_version: {
        Args: { document_uuid: string; version_number: number };
        Returns: string;
      };
      schedule_lawyer_consultation: {
        Args: {
          lawyer_uuid: string;
          document_uuid: string;
          consultation_date: string;
          duration_minutes?: number;
          notes?: string;
        };
        Returns: string;
      };
      send_push_notification: {
        Args: {
          user_uuid: string;
          title: string;
          body: string;
          url?: string;
          icon?: string;
          tag?: string;
          data?: Json;
        };
        Returns: boolean;
      };
      send_realtime_message: {
        Args: { topic: string; event: string; payload: Json };
        Returns: undefined;
      };
      share_document: {
        Args: { document_uuid: string; user_email: string; can_edit?: boolean };
        Returns: string;
      };
      sign_contract: {
        Args: {
          signer_uuid: string;
          signature: string;
          wallet_address?: string;
        };
        Returns: boolean;
      };
      track_share_link_access: {
        Args: { link_id: string; provided_pin?: string };
        Returns: boolean;
      };
      unshare_document: {
        Args: { share_uuid: string };
        Returns: boolean;
      };
      update_consultation_status: {
        Args: { consultation_uuid: string; new_status: string };
        Returns: boolean;
      };
      update_user_settings: {
        Args: {
          theme?: string;
          language?: string;
          notifications_enabled?: boolean;
          email_notifications?: Json;
          display_preferences?: Json;
          export_preferences?: Json;
        };
        Returns: {
          created_at: string;
          display_preferences: Json | null;
          email_notifications: Json | null;
          export_preferences: Json | null;
          id: string;
          language: string;
          notifications_enabled: boolean;
          security_preferences: Json | null;
          theme: string;
          updated_at: string;
          user_id: string;
        };
      };
      user_exists: {
        Args: { user_id: string };
        Returns: boolean;
      };
      verify_password: {
        Args: { password: string; hash: string };
        Returns: boolean;
      };
    };
    Enums: {
      permission:
        | 'create_form'
        | 'edit_form'
        | 'delete_form'
        | 'view_form'
        | 'manage_users'
        | 'manage_roles'
        | 'review_document'
        | 'manage_consultations';
      plan_new: 'Free' | 'Standard' | 'Enterprise';
      user_role: 'user' | 'lawyer' | 'admin';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      permission: [
        'create_form',
        'edit_form',
        'delete_form',
        'view_form',
        'manage_users',
        'manage_roles',
        'review_document',
        'manage_consultations',
      ],
      plan_new: ['Free', 'Standard', 'Enterprise'],
      user_role: ['user', 'lawyer', 'admin'],
    },
  },
} as const;
