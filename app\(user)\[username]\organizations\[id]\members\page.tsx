'use client';

import { AddMemberDialog } from '@/components/organizations/AddMemberDialog';
import { OrganizationMembersList } from '@/components/organizations/OrganizationMembersList';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useOrganizations } from '@/lib/hooks';
import { OrganizationMember } from '@/lib/types/database-modules';
import { ArrowLeft, Loader2, Search, UserPlus, Users } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

export default function OrganizationMembersPage() {
  const { username, id } = useParams();
  const { getOrganization } = useOrganizations();

  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [organizationName, setOrganizationName] = useState('');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMembers, setFilteredMembers] = useState<OrganizationMember[]>(
    []
  );
  const [activeTab, setActiveTab] = useState('all');
  const [isAddMemberDialogOpen, setIsAddMemberDialogOpen] = useState(false);

  useEffect(() => {
    async function loadMembers() {
      setLoading(true);
      try {
        const orgId = Array.isArray(id) ? id[0] : id;

        if (!orgId) {
          toast.error('Organization ID is missing');
          setLoading(false);
          return;
        }

        const org = await getOrganization(orgId);

        if (org) {
          setOrganizationName(org.name);
          setMembers(org.members || []);
        }
      } catch (error: any) {
        toast.error('Failed to load organization members', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setLoading(false);
      }
    }

    loadMembers();
  }, [id, getOrganization]);

  // Filter members based on search query and active tab
  useEffect(() => {
    if (!members) {
      setFilteredMembers([]);
      return;
    }

    let filtered = [...members];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (member) =>
          member.user?.full_name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          member.user?.email?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply role filter
    if (activeTab !== 'all') {
      filtered = filtered.filter((member) => member.role === activeTab);
    }

    setFilteredMembers(filtered);
  }, [members, searchQuery, activeTab]);

  // Get the organization ID with type safety
  const orgId = Array.isArray(id) ? id[0] : id;

  // If no organization ID, show an error message
  if (!orgId) {
    return (
      <div className="p-6">
        <div className="flex flex-col items-center justify-center py-8">
          <h3 className="text-lg font-medium mb-2">Organization Not Found</h3>
          <p className="text-sm text-muted-foreground">
            The organization ID is missing.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Link href={`/${username}/organizations/${orgId}`}>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{organizationName} Members</h1>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative w-full md:max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
          <Input
            className="pl-10"
            placeholder="Search members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2 ml-auto">
          <Tabs defaultValue="all" onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="owner">Owners</TabsTrigger>
              <TabsTrigger value="admin">Admins</TabsTrigger>
              <TabsTrigger value="member">Members</TabsTrigger>
            </TabsList>
          </Tabs>

          <Button onClick={() => setIsAddMemberDialogOpen(true)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Member
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5 text-primary" />
            Organization Members
          </CardTitle>
          <CardDescription>
            Manage members and their roles within the organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
              <p className="text-muted-foreground">Loading members...</p>
            </div>
          ) : filteredMembers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Users className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Members Found</h3>
              <p className="text-sm text-muted-foreground max-w-md mb-4">
                {searchQuery
                  ? 'No members match your search criteria.'
                  : activeTab !== 'all'
                    ? `No members with the role "${activeTab}" found.`
                    : "This organization doesn't have any members yet."}
              </p>
              <Button onClick={() => setIsAddMemberDialogOpen(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                Add Member
              </Button>
            </div>
          ) : (
            <OrganizationMembersList
              organizationId={orgId}
              initialMembers={filteredMembers}
            />
          )}
        </CardContent>
      </Card>

      <AddMemberDialog
        open={isAddMemberDialogOpen}
        onOpenChange={setIsAddMemberDialogOpen}
        organizationId={orgId}
      />
    </div>
  );
}
