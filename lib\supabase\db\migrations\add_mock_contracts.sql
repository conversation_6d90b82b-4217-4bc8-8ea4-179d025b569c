-- Add mock contract templates and smart contracts

-- Insert mock data for contract_templates
INSERT INTO public.contract_templates (name, description, contract_type, source_code, abi, parameters, is_public, created_by)
VALUES
  ('Simple Token', 'A simple ERC20 token contract', 'token', 'pragma solidity ^0.8.0;\n\nimport "@openzeppelin/contracts/token/ERC20/ERC20.sol";\n\ncontract SimpleToken is ERC20 {\n    constructor(string memory name, string memory symbol, uint256 initialSupply) ERC20(name, symbol) {\n        _mint(msg.sender, initialSupply * 10 ** decimals());\n    }\n}', '[{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint256","name":"initialSupply","type":"uint256"}],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]', '{"name": {"type": "string", "description": "Token name"}, "symbol": {"type": "string", "description": "Token symbol"}, "initialSupply": {"type": "uint256", "description": "Initial token supply"}}', true, auth.uid());

-- Insert mock data for smart_contracts
INSERT INTO public.smart_contracts (name, description, contract_type, contract_address, chain_id, status, source_code, abi, owner_id)
VALUES
  ('My Token', 'My custom ERC20 token', 'token', '0x1234567890123456789012345678901234567890', 1, 'deployed', 'pragma solidity ^0.8.0;\n\nimport "@openzeppelin/contracts/token/ERC20/ERC20.sol";\n\ncontract MyToken is ERC20 {\n    constructor() ERC20("MyToken", "MTK") {\n        _mint(msg.sender, 1000000 * 10 ** decimals());\n    }\n}', '[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"}]', auth.uid());

-- Insert mock data for contract_events
INSERT INTO public.contract_events (contract_id, event_type, event_data, tx_hash, block_number, timestamp)
SELECT 
  id, 
  'Deployment', 
  '{"gas_used": 1500000, "gas_price": 20000000000}', 
  '0x9876543210abcdef9876543210abcdef9876543210abcdef9876543210abcdef', 
  12345678, 
  NOW()
FROM public.smart_contracts 
WHERE name = 'My Token';
