'use client';

import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useSettings } from '@/lib/hooks';
import {
  SubscriptionInfo,
  UsageLimits,
  UserSettings,
} from '@/lib/types/database-modules';
import { useEffect, useState } from 'react';
import { GeneralSettings } from './general-settings';
import { NotificationsSettings } from './notifications-settings';
import { SubscriptionSettings } from './subscription-settings-simple';

interface SettingsTabsProps {
  initialSettings?: UserSettings | null;
  initialSubscription?: SubscriptionInfo | null;
  availablePlans?: UsageLimits[];
}

export function SettingsTabs({
  initialSettings,
  initialSubscription,
  availablePlans,
}: SettingsTabsProps) {
  const {
    loading,
    error,
    settings,
    subscription,
    plans,
    fetchSettings,
    fetchSubscription,
    fetchAvailablePlans,
  } = useSettings();

  const [activeTab, setActiveTab] = useState('general');

  // Load settings data if not provided
  useEffect(() => {
    if (!initialSettings) {
      fetchSettings();
    }

    if (!initialSubscription) {
      fetchSubscription();
    }

    if (!availablePlans) {
      fetchAvailablePlans();
    }
  }, [
    initialSettings,
    initialSubscription,
    availablePlans,
    fetchSettings,
    fetchSubscription,
    fetchAvailablePlans,
  ]);

  // Use initial data or fetched data
  const settingsData = initialSettings || settings;
  const subscriptionData = initialSubscription || subscription;
  const plansData = availablePlans || plans;

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="subscription">Subscription</TabsTrigger>
        <TabsTrigger value="notifications">Notifications</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="mt-6">
        <GeneralSettings settings={settingsData} />
      </TabsContent>

      <TabsContent value="subscription" className="mt-6">
        <SubscriptionSettings
          subscription={subscriptionData}
          availablePlans={plansData}
        />
      </TabsContent>

      <TabsContent value="notifications" className="mt-6">
        <NotificationsSettings settings={settingsData} />
      </TabsContent>
    </Tabs>
  );
}
