'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserAvatar } from '@/components/ui/user-avatar';
import { CalendarEvent } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import {
  formatDate,
  getEventColorClass,
  getMonthDays,
  isEventOnDay,
} from '@/lib/utils/calendar-utils';
import { isSameDay, isSameMonth, isToday } from 'date-fns';
import { Calendar, Clock, FileText, MapPin, Video } from 'lucide-react';
import { useState } from 'react';

interface MonthViewProps {
  currentDate: Date;
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  onDateClick: (date: Date) => void;
  isReadOnly?: boolean;
}

export function MonthView({
  currentDate,
  events,
  onEventClick,
  onDateClick,
  isReadOnly = false,
}: MonthViewProps) {
  const { weeks } = getMonthDays(currentDate);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);

  // Handle event click
  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    if (isReadOnly) {
      setSelectedEvent(event);
      setIsEventDialogOpen(true);
    } else {
      onEventClick(event);
    }
  };

  // Handle date cell click
  const handleDateClick = (date: Date) => {
    onDateClick(date);
  };

  // Get events for a specific day
  const getEventsForDay = (day: Date) => {
    return events.filter((event) => isEventOnDay(event, day));
  };

  // Render events for a day
  const renderEvents = (day: Date) => {
    const dayEvents = getEventsForDay(day);
    const maxEventsToShow = 3;
    const hasMoreEvents = dayEvents.length > maxEventsToShow;

    return (
      <div className="mt-1 overflow-y-auto max-h-[80px]">
        {dayEvents.slice(0, maxEventsToShow).map((event) => (
          <div
            key={event.id}
            onClick={(e) => handleEventClick(event, e)}
            className={cn(
              'text-xs px-1 py-0.5 rounded truncate mb-1 border cursor-pointer',
              getEventColorClass(event)
            )}
          >
            {formatDate(new Date(event.start), 'h:mm a')} {event.title}
          </div>
        ))}
        {hasMoreEvents && (
          <div className="text-xs text-muted-foreground px-1">
            +{dayEvents.length - maxEventsToShow} more
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="grid grid-cols-7 gap-px bg-muted rounded-lg overflow-hidden">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
          <div key={day} className="bg-background p-2 text-center font-medium">
            {day}
          </div>
        ))}

        {/* Calendar days */}
        {weeks.map((week, weekIndex) =>
          week.map((day, dayIndex) => {
            const isCurrentMonth = isSameMonth(day, currentDate);
            const isSelected = isSameDay(day, currentDate);
            const isTodayDate = isToday(day);

            return (
              <div
                key={`${weekIndex}-${dayIndex}`}
                onClick={() => handleDateClick(day)}
                className={cn(
                  'bg-background min-h-[100px] p-1 relative',
                  !isCurrentMonth && 'text-muted-foreground',
                  isSelected && 'bg-accent/50',
                  'hover:bg-accent/30 cursor-pointer'
                )}
              >
                <div className="flex justify-between items-center">
                  <span
                    className={cn(
                      'h-6 w-6 text-center text-sm',
                      isTodayDate &&
                        'bg-primary text-primary-foreground rounded-full'
                    )}
                  >
                    {formatDate(day, 'd')}
                  </span>
                </div>
                {renderEvents(day)}
              </div>
            );
          })
        )}
      </div>

      {/* Event details dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedEvent?.title}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {selectedEvent && (
              <>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(
                      new Date(selectedEvent.start),
                      'EEEE, MMMM d, yyyy'
                    )}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {formatDate(new Date(selectedEvent.start), 'h:mm a')} -
                    {formatDate(new Date(selectedEvent.end), 'h:mm a')}
                  </span>
                </div>

                {selectedEvent.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedEvent.location}</span>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  {selectedEvent.consultationType === 'video' ? (
                    <Video className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span>
                    {selectedEvent.consultationType === 'video'
                      ? 'Video Consultation'
                      : 'Document Review'}
                  </span>
                </div>

                {selectedEvent.status && (
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={
                        selectedEvent.status === 'scheduled'
                          ? 'bg-blue-50 text-blue-700 border-blue-200'
                          : selectedEvent.status === 'confirmed'
                            ? 'bg-green-50 text-green-700 border-green-200'
                            : selectedEvent.status === 'completed'
                              ? 'bg-purple-50 text-purple-700 border-purple-200'
                              : 'bg-red-50 text-red-700 border-red-200'
                      }
                    >
                      {selectedEvent.status.charAt(0).toUpperCase() +
                        selectedEvent.status.slice(1)}
                    </Badge>
                  </div>
                )}

                {(selectedEvent.client || selectedEvent.lawyer) && (
                  <div className="flex items-center gap-2 mt-4">
                    {selectedEvent.client && (
                      <div className="flex items-center gap-2">
                        <UserAvatar
                          userId={selectedEvent.client.id}
                          avatarUrl={selectedEvent.client.avatar_url}
                          fallbackText={selectedEvent.client.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.client.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Client
                          </p>
                        </div>
                      </div>
                    )}

                    {selectedEvent.lawyer && (
                      <div className="flex items-center gap-2 ml-auto">
                        <UserAvatar
                          userId={selectedEvent.lawyer.id}
                          avatarUrl={selectedEvent.lawyer.avatar_url}
                          fallbackText={selectedEvent.lawyer.full_name}
                          className="h-8 w-8"
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {selectedEvent.lawyer.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Lawyer
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {selectedEvent.description && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">Description</h4>
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                      {selectedEvent.description}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => setIsEventDialogOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
