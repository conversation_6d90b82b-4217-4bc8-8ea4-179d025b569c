'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MultiSelect } from '@/components/ui/multi-select';
import { Textarea } from '@/components/ui/textarea';
import { useCollaboration } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { supabase } from '@/lib/supabase/client';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface Document {
  id: string;
  title: string;
  owner_id: string;
  created_at: string;
}

interface Lawyer {
  id: string;
  user_id: string;
  full_name: string;
  email: string;
  specialization: string[];
}

export default function NewProjectPage() {
  const params = useParams();
  const username = params.username as string;
  const { profile } = userStore();
  const { createProject } = useCollaboration();
  const router = useRouter();
  const [isCreating, setIsCreating] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  );
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [lawyers, setLawyers] = useState<Lawyer[]>([]);
  const [selectedLawyers, setSelectedLawyers] = useState<string[]>([]);
  const [isPrivate, setIsPrivate] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch documents and lawyers when the page loads
  useEffect(() => {
    fetchDocuments();
    fetchLawyers();
  }, []);

  const fetchDocuments = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('documents')
        .select('id, title, owner_id, created_at')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchLawyers = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('lawyers')
        .select('id, user_id, full_name, email, specialization')
        .eq('status', 'active')
        .order('full_name');

      if (error) throw error;
      setLawyers(data || []);
    } catch (error) {
      console.error('Error fetching lawyers:', error);
      toast.error('Failed to load lawyers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProject = async () => {
    if (!name.trim()) {
      toast.error('Project name is required');
      return;
    }

    setIsCreating(true);
    try {
      // Create the project
      const newProject = await createProject(
        name.trim(),
        description.trim(),
        startDate.toISOString(),
        endDate.toISOString()
      );

      if (!newProject) {
        throw new Error('Failed to create project');
      }

      // Add selected documents to the project
      if (selectedDocuments.length > 0) {
        // Skip added_by if profile is null
        const documentInserts = selectedDocuments.map((docId) => {
          const insert: any = {
            project_id: newProject.id,
            document_id: docId,
          };

          if (profile?.id) {
            insert.added_by = profile.id;
          }

          return insert;
        });

        const { error: docsError } = await supabase
          .from('project_documents')
          .insert(documentInserts);

        if (docsError) {
          console.error('Error adding documents to project:', docsError);
          // Continue even if document linking fails
        }
      }

      // Add selected lawyers as collaborators
      if (selectedLawyers.length > 0) {
        for (const lawyerId of selectedLawyers) {
          const lawyer = lawyers.find((l) => l.id === lawyerId);
          if (lawyer) {
            // Use type assertion to handle the role type mismatch
            const memberData: any = {
              project_id: newProject.id,
              user_id: lawyer.user_id,
              role: 'member', // This is valid as per the updated CHECK constraint
            };

            const { error: memberError } = await supabase
              .from('project_members')
              .insert(memberData);

            if (memberError) {
              console.error(
                `Error adding lawyer ${lawyer.full_name} to project:`,
                memberError
              );
              // Continue with other lawyers even if one fails
            }
          }
        }
      }

      toast.success('Project created successfully');
      router.push(`/${username}/collaboration/projects/${newProject.id}`);
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center gap-2 mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push(`/${username}/collaboration/projects`)}
        >
          <ArrowLeft className="size-5" />
        </Button>
        <h1 className="text-2xl font-bold">Create New Project</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Project Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                placeholder="Enter project name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isCreating}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter project description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={isCreating}
                className="min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label>Start Date</Label>
                <DatePicker
                  date={startDate}
                  setDate={(date) => setStartDate(date || new Date())}
                  disabled={isCreating}
                />
              </div>
              <div className="grid gap-2">
                <Label>End Date</Label>
                <DatePicker
                  date={endDate}
                  setDate={(date) => setEndDate(date || new Date())}
                  disabled={isCreating}
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label>Documents</Label>
              {isLoading ? (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading documents...</span>
                </div>
              ) : (
                <MultiSelect
                  options={documents.map((doc) => ({
                    label: doc.title,
                    value: doc.id,
                  }))}
                  selected={selectedDocuments}
                  onChange={setSelectedDocuments}
                  placeholder="Search and select documents..."
                  disabled={isCreating}
                />
              )}
            </div>

            <div className="grid gap-2">
              <Label>Lawyer Collaborators</Label>
              {isLoading ? (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading lawyers...</span>
                </div>
              ) : (
                <MultiSelect
                  options={lawyers.map((lawyer) => ({
                    label: `${lawyer.full_name} (${lawyer.specialization?.join(', ') || 'General'})`,
                    value: lawyer.id,
                  }))}
                  selected={selectedLawyers}
                  onChange={setSelectedLawyers}
                  placeholder="Search and select lawyers..."
                  disabled={isCreating}
                />
              )}
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="private"
                checked={isPrivate}
                onCheckedChange={(checked) => setIsPrivate(!!checked)}
                disabled={isCreating}
              />
              <Label htmlFor="private" className="text-sm font-normal">
                Make this project private (only invited members can access)
              </Label>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() =>
                  router.push(`/${username}/collaboration/projects`)
                }
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button onClick={handleCreateProject} disabled={isCreating}>
                {isCreating && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Create Project
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
