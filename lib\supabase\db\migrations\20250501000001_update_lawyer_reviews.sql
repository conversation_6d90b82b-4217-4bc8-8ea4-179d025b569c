-- Migration to update lawyer_reviews table to use user_id instead of client_id
-- This migration is idempotent and can be run multiple times

-- Check if client_id column exists and user_id doesn't exist
DO $$
BEGIN
    -- If client_id exists but user_id doesn't, rename the column
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'lawyer_reviews'
        AND column_name = 'client_id'
    ) AND NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'lawyer_reviews'
        AND column_name = 'user_id'
    ) THEN
        -- Rename client_id to user_id
        ALTER TABLE public.lawyer_reviews RENAME COLUMN client_id TO user_id;

        -- Update foreign key constraint if it exists
        IF EXISTS (
            SELECT 1
            FROM information_schema.table_constraints tc
            JOIN information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = 'lawyer_reviews'
            AND ccu.column_name = 'client_id'
        ) THEN
            -- Drop the old foreign key constraint
            EXECUTE (
                SELECT 'ALTER TABLE public.lawyer_reviews DROP CONSTRAINT ' || tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.constraint_column_usage ccu
                ON tc.constraint_name = ccu.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
                AND tc.table_schema = 'public'
                AND tc.table_name = 'lawyer_reviews'
                AND ccu.column_name = 'client_id'
                LIMIT 1
            );

            -- Add new foreign key constraint
            ALTER TABLE public.lawyer_reviews
            ADD CONSTRAINT lawyer_reviews_user_id_fkey
            FOREIGN KEY (user_id)
            REFERENCES auth.users(id);
        END IF;
    END IF;
END $$;

-- Update RLS policies
DROP POLICY IF EXISTS "Users can view and manage their own reviews" ON public.lawyer_reviews;

-- Create new policy using user_id
CREATE POLICY "Users can view and manage their own reviews"
ON public.lawyer_reviews
FOR ALL
USING (user_id = auth.uid());
