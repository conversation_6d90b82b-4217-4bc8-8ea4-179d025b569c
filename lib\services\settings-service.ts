import { supabaseClient } from '@/lib/supabase/client';
import { toast } from 'sonner';
import { RealtimeChannel } from '@supabase/supabase-js';

// Settings types
export interface UserSettings {
  id: string;
  user_id: string;
  theme: string;
  language: string;
  notifications_enabled: boolean;
  email_notifications: {
    document_updates: boolean;
    signatures: boolean;
    consultations: boolean;
    payments: boolean;
    marketing: boolean;
    comment_added: boolean;
    task_assigned: boolean;
    document_shared: boolean;
    document_updated: boolean;
  };
  display_preferences: {
    document_view: 'card' | 'table';
    sidebar_collapsed: boolean;
    show_document_previews: boolean;
    fontSize: 'small' | 'medium' | 'large';
    reduceMotion: boolean;
    highContrast: boolean;
    documentFont?: {
      fallback: string;
      className: string;
    };
  };
  export_preferences: {
    default_format: string;
    include_metadata: boolean;
    include_signatures: boolean;
  };
  security_preferences: {
    two_factor_enabled: boolean;
    login_notifications: boolean;
    session_timeout: number;
  };
  created_at: string;
  updated_at: string;
}

// Settings update types
export type NotificationSettings = {
  notifications_enabled?: boolean;
  email_notifications?: Partial<UserSettings['email_notifications']>;
};

export type DisplaySettings = {
  theme?: string;
  language?: string;
  display_preferences?: Partial<UserSettings['display_preferences']>;
};

export type ExportSettings = {
  export_preferences?: Partial<UserSettings['export_preferences']>;
};

export type SecuritySettings = {
  security_preferences?: Partial<UserSettings['security_preferences']>;
};

export type SettingsUpdate = NotificationSettings &
  DisplaySettings &
  ExportSettings &
  SecuritySettings;

class SettingsService {
  private static instance: SettingsService;
  private settingsChannel: RealtimeChannel | null = null;
  private userId: string | null = null;
  private settingsChangeCallbacks: ((settings: UserSettings) => void)[] = [];

  private constructor() {
    // Initialize Realtime subscription when the service is created
    this.initRealtimeSubscription();
  }

  public static getInstance(): SettingsService {
    if (!SettingsService.instance) {
      SettingsService.instance = new SettingsService();
    }
    return SettingsService.instance;
  }

  /**
   * Initialize Realtime subscription for settings changes
   */
  private async initRealtimeSubscription(): Promise<void> {
    try {
      // Get the current user
      const { data: authData } = await supabaseClient.auth.getUser();
      if (!authData?.user) return;

      this.userId = authData.user.id;

      // Create a channel for user settings
      this.settingsChannel = supabaseClient
        .channel(`user_settings:${this.userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'user_settings',
            filter: `user_id=eq.${this.userId}`,
          },
          async (payload) => {
            // When settings change, notify all callbacks
            if (payload.new) {
              const newSettings = payload.new as UserSettings;
              this.notifySettingsChange(newSettings);

              // Show toast notification for settings update
              toast.success('Settings updated', {
                description: 'Your settings have been updated successfully',
              });
            }
          }
        )
        .subscribe();
    } catch (error) {
      console.error('Error initializing Realtime subscription:', error);
    }
  }

  /**
   * Register a callback for settings changes
   */
  public onSettingsChange(
    callback: (settings: UserSettings) => void
  ): () => void {
    this.settingsChangeCallbacks.push(callback);

    // Return a function to unregister the callback
    return () => {
      this.settingsChangeCallbacks = this.settingsChangeCallbacks.filter(
        (cb) => cb !== callback
      );
    };
  }

  /**
   * Notify all callbacks about settings changes
   */
  private notifySettingsChange(settings: UserSettings): void {
    this.settingsChangeCallbacks.forEach((callback) => {
      try {
        callback(settings);
      } catch (error) {
        console.error('Error in settings change callback:', error);
      }
    });
  }

  /**
   * Get user settings
   */
  async getUserSettings(): Promise<UserSettings | null> {
    try {
      // Get the current user
      const { data: authData, error: authError } =
        await supabaseClient.auth.getUser();
      if (authError || !authData.user) {
        console.error('No authenticated user found:', authError);
        return null;
      }

      // Get user settings from the database
      const { data, error } = await supabaseClient
        .from('user_settings')
        .select('*')
        .eq('user_id', authData.user.id)
        .single();

      if (error) {
        console.error('Error fetching user settings:', error);
        return null;
      }

      return data as UserSettings;
    } catch (error) {
      console.error('Error in getUserSettings:', error);
      return null;
    }
  }

  /**
   * Update user settings
   */
  async updateSettings(updates: SettingsUpdate): Promise<UserSettings | null> {
    try {
      // Get the current user
      const { data: authData, error: authError } =
        await supabaseClient.auth.getUser();
      if (authError || !authData.user) {
        console.error('No authenticated user found:', authError);
        toast.error('Authentication error', {
          description: 'You must be logged in to update settings',
        });
        return null;
      }

      // Get current settings to merge with updates
      const currentSettings = await this.getUserSettings();
      if (!currentSettings) {
        console.error('No settings found to update');
        toast.error('Settings not found', {
          description: 'Unable to find your settings',
        });
        return null;
      }

      // Prepare the update object
      const updateData: any = {};

      // Update notification settings
      if (updates.notifications_enabled !== undefined) {
        updateData.notifications_enabled = updates.notifications_enabled;
      }

      if (updates.email_notifications) {
        updateData.email_notifications = {
          ...currentSettings.email_notifications,
          ...updates.email_notifications,
        };
      }

      // Update display settings
      if (updates.theme) {
        updateData.theme = updates.theme;
      }

      if (updates.language) {
        updateData.language = updates.language;
      }

      if (updates.display_preferences) {
        updateData.display_preferences = {
          ...currentSettings.display_preferences,
          ...updates.display_preferences,
        };
      }

      // Update export settings
      if (updates.export_preferences) {
        updateData.export_preferences = {
          ...currentSettings.export_preferences,
          ...updates.export_preferences,
        };
      }

      // Update security settings
      if (updates.security_preferences) {
        updateData.security_preferences = {
          ...currentSettings.security_preferences,
          ...updates.security_preferences,
        };
      }

      // Show toast notification for settings update
      const promise = supabaseClient
        .from('user_settings')
        .update(updateData)
        .eq('user_id', authData.user.id)
        .select()
        .single();

      toast.promise(promise, {
        loading: 'Updating settings...',
        success: 'Settings updated successfully',
        error: 'Failed to update settings',
      });

      // Wait for the update to complete
      const { data, error } = await promise;

      if (error) {
        console.error('Error updating user settings:', error);
        return null;
      }

      return data as UserSettings;
    } catch (error) {
      console.error('Error in updateSettings:', error);
      toast.error('Failed to update settings', {
        description: 'An unexpected error occurred',
      });
      return null;
    }
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(
    updates: NotificationSettings
  ): Promise<UserSettings | null> {
    return this.updateSettings(updates);
  }

  /**
   * Update display settings
   */
  async updateDisplaySettings(
    updates: DisplaySettings
  ): Promise<UserSettings | null> {
    return this.updateSettings(updates);
  }

  /**
   * Update export settings
   */
  async updateExportSettings(
    updates: ExportSettings
  ): Promise<UserSettings | null> {
    return this.updateSettings(updates);
  }

  /**
   * Update security settings
   */
  async updateSecuritySettings(
    updates: SecuritySettings
  ): Promise<UserSettings | null> {
    return this.updateSettings(updates);
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings(): Promise<UserSettings | null> {
    try {
      // Get the current user
      const { data: authData, error: authError } =
        await supabaseClient.auth.getUser();
      if (authError || !authData.user) {
        console.error('No authenticated user found:', authError);
        toast.error('Authentication error', {
          description: 'You must be logged in to reset settings',
        });
        return null;
      }

      // Create a promise for the reset operation
      const resetPromise = (async () => {
        // Delete the current settings
        const { error: deleteError } = await supabaseClient
          .from('user_settings')
          .delete()
          .eq('user_id', authData.user.id);

        if (deleteError) {
          console.error('Error deleting user settings:', deleteError);
          throw new Error('Failed to delete settings');
        }

        // Insert default settings
        const { data, error } = await supabaseClient
          .from('user_settings')
          .insert({ user_id: authData.user.id })
          .select()
          .single();

        if (error) {
          console.error('Error creating default user settings:', error);
          throw new Error('Failed to create default settings');
        }

        return data;
      })();

      // Show confirmation toast
      toast.promise(resetPromise, {
        loading: 'Resetting settings to defaults...',
        success: 'Settings reset to defaults',
        error: 'Failed to reset settings',
      });

      // Wait for the reset operation to complete
      try {
        const data = await resetPromise;
        return data as UserSettings;
      } catch (error) {
        console.error('Error resetting settings:', error);
        return null;
      }
    } catch (error) {
      console.error('Error in resetSettings:', error);
      toast.error('Failed to reset settings', {
        description: 'An unexpected error occurred',
      });
      return null;
    }
  }

  /**
   * Send a test notification
   */
  async sendTestNotification(type: 'email' | 'app' | 'sms'): Promise<boolean> {
    try {
      // Get the current user
      const { data: authData, error: authError } =
        await supabaseClient.auth.getUser();
      if (authError || !authData.user) {
        console.error('No authenticated user found:', authError);
        toast.error('Authentication error', {
          description: 'You must be logged in to send a test notification',
        });
        return false;
      }

      // Create a test notification
      const notificationData = {
        user_id: authData.user.id,
        title: `Test ${type} Notification`,
        content: `This is a test ${type} notification sent from your settings page.`,
        type: 'system',
        read: false,
        action_url: null,
      };

      // Create a promise for the notification operation
      const notificationPromise = supabaseClient
        .from('notifications')
        .insert(notificationData)
        .select();

      // Show toast notification
      toast.promise(notificationPromise, {
        loading: `Sending test ${type} notification...`,
        success: `Test ${type} notification sent`,
        error: `Failed to send test ${type} notification`,
      });

      // Wait for the notification operation to complete
      const { data, error } = await notificationPromise;

      if (error) {
        console.error('Error sending test notification:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in sendTestNotification:', error);
      toast.error('Failed to send test notification', {
        description: 'An unexpected error occurred',
      });
      return false;
    }
  }
}

// Export a singleton instance
export const settingsService = SettingsService.getInstance();
