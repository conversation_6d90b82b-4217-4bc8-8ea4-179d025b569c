'use client';

import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, Sparkles, ThumbsDown, ThumbsUp, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface AISuggestionPopoverProps {
  trigger: React.ReactNode;
  suggestions: {
    id: string;
    text: string;
    explanation: string;
  }[];
  onAccept: (suggestion: string) => void;
  onDismiss: () => void;
}

export function AISuggestionPopover({
  trigger,
  suggestions,
  onAccept,
  onDismiss,
}: AISuggestionPopoverProps) {
  const [open, setOpen] = useState(false);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);
  const [feedback, setFeedback] = useState<
    Record<string, 'positive' | 'negative' | null>
  >({});

  const currentSuggestion = suggestions[currentSuggestionIndex];

  const handleNext = () => {
    if (currentSuggestionIndex < suggestions.length - 1) {
      setCurrentSuggestionIndex(currentSuggestionIndex + 1);
    } else {
      setOpen(false);
      onDismiss();
    }
  };

  const handleAccept = () => {
    onAccept(currentSuggestion.text);
    setFeedback({
      ...feedback,
      [currentSuggestion.id]: 'positive',
    });
    toast.success('Suggestion applied', {
      description: 'The AI suggestion has been applied to your document.',
    });
    handleNext();
  };

  const handleDismiss = () => {
    setFeedback({
      ...feedback,
      [currentSuggestion.id]: 'negative',
    });
    handleNext();
  };

  const handleFeedback = (type: 'positive' | 'negative') => {
    setFeedback({
      ...feedback,
      [currentSuggestion.id]: type,
    });

    toast.success('Thank you for your feedback', {
      description: 'Your feedback helps improve our AI suggestions.',
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent className="w-80" align="start">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Sparkles className="h-4 w-4 text-accent-300 mr-2" />
              <h4 className="font-medium text-sm">AI Suggestion</h4>
            </div>
            <div className="text-xs text-neutral-500">
              {currentSuggestionIndex + 1} of {suggestions.length}
            </div>
          </div>

          <div className="p-3 bg-accent-10/5 rounded-md border border-accent-10/10 text-sm">
            {currentSuggestion.text}
          </div>

          <div>
            <p className="text-xs text-neutral-500 mb-1">
              Why this suggestion?
            </p>
            <p className="text-xs">{currentSuggestion.explanation}</p>
          </div>

          <div className="flex justify-between pt-2 border-t">
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleFeedback('positive')}
                disabled={feedback[currentSuggestion.id] !== null}
              >
                <ThumbsUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleFeedback('negative')}
                disabled={feedback[currentSuggestion.id] !== null}
              >
                <ThumbsDown className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8"
                onClick={handleDismiss}
              >
                <X className="h-3 w-3 mr-1" />
                Dismiss
              </Button>
              <Button size="sm" className="h-8" onClick={handleAccept}>
                <Check className="h-3 w-3 mr-1" />
                Apply
              </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
