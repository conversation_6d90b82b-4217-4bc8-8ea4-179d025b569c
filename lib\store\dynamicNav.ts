import { create } from 'zustand';

type stateUrl = string | null;
type actions = string | string[] | null;

type State = {
  url: stateUrl;
  actions: actions;
};

type Action = {
  updateUrl: (url: State['url']) => void;
  removeUrl: () => void;
  updateActions: (actions: State['actions']) => void;
  removeActions: () => void;
};

export const dynamicNavStates = create<State & Action>((set) => ({
  url: null,
  actions: null,
  updateUrl: (url) => set(() => ({ url: url })),
  removeUrl: () => set(() => ({ url: null })),
  updateActions: (actions) => set(() => ({ actions: actions })),
  removeActions: () => set(() => ({ actions: null })),
}));
