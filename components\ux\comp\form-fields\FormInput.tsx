'use client';

// RHF
import { useFormContext } from 'react-hook-form';

// ShadCn
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input, InputProps } from '@/components/ui/input';

type FormInputProps = {
  name: string;
  label?: string;
  labelHelper?: string;
  placeholder?: string;
  vertical?: boolean;
} & InputProps;

const FormInput = ({
  name,
  label,
  labelHelper,
  placeholder,
  vertical = false,
  ...props
}: FormInputProps) => {
  const { control } = useFormContext();

  const verticalInput = (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <div className="flex flex-col items-start space-y-1">
            {label && (
              <FormLabel className="ml-2 text-sm text-neutral-400">{`${label}`}</FormLabel>
            )}

            {labelHelper && <span className="text-xs"> {labelHelper}</span>}

            <FormControl>
              <Input
                {...field}
                placeholder={placeholder}
                className="w-[300px] md:w-[13rem]"
                {...props}
              />
            </FormControl>
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );

  const horizontalInput = (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <div className="flex items-center justify-between gap-5 text-sm">
            {label && <FormLabel>{`${label}:`}</FormLabel>}
            {labelHelper && <span className="text-xs"> {labelHelper}</span>}

            <div>
              <FormControl>
                <Input
                  {...field}
                  placeholder={placeholder}
                  className="w-[13rem]"
                  {...props}
                />
              </FormControl>
              <FormMessage />
            </div>
          </div>
        </FormItem>
      )}
    />
  );
  return vertical ? verticalInput : horizontalInput;
};

export default FormInput;
