import { buttonVariants } from '@/components/ui/button';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const DashboardAction = () => {
  const pathname = usePathname();
  const { profile } = userStore();
  return (
    <div className="flex flex-row items-center justify-center space-x-2">
      <Link
        href={`/${profile?.username}/forms/new`}
        className={cn(buttonVariants({ variant: 'shadow', size: 'sm' }), '')}
      >
        <span>New Form</span>
      </Link>
    </div>
  );
};

export default DashboardAction;
