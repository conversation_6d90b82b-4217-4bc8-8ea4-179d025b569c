import { createServerClient } from '@/lib/supabase/server-client';
import { Document } from './documents-types';

/**
 * Get a document by ID
 */
export async function getDocument(
  documentId: string
): Promise<Document | null> {
  const supabase = await createServerClient();

  const { data, error } = await supabase
    .from('documents')
    .select('*')
    .eq('id', documentId)
    .single();

  if (error) {
    console.error('Error fetching document:', error);
    throw error;
  }

  // Cast the database response to match our Document interface
  return data as Document;
}

/**
 * Increment the export count for a document
 */
export async function incrementExportCount(documentId: string): Promise<void> {
  const supabase = await createServerClient();

  const { error } = await supabase.rpc('increment_document_export_count', {
    document_id: documentId,
  });

  if (error) {
    // Fallback if the RPC function doesn't exist
    console.warn('R<PERSON> failed, trying direct update:', error);
    
    // Get the current export count
    const { data, error: getError } = await supabase
      .from('documents')
      .select('export_count')
      .eq('id', documentId)
      .single();
    
    if (getError) {
      console.error('Error getting document export count:', getError);
      throw getError;
    }
    
    // Update the export count
    const { error: updateError } = await supabase
      .from('documents')
      .update({
        export_count: ((data?.export_count || 0) + 1),
        last_exported_at: new Date().toISOString(),
      })
      .eq('id', documentId);
    
    if (updateError) {
      console.error('Error updating document export count:', updateError);
      throw updateError;
    }
  }
}
