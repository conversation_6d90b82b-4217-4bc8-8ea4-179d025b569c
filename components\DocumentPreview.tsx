import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useFormState } from '@/lib/contexts/FormStateContext';
import {
  DocumentGenerationOptions,
  DocumentGenerationService,
  DocumentStyling,
  DocumentTemplate,
} from '@/lib/services/DocumentGenerationService';
import { useCallback, useMemo, useState } from 'react';
import { toast } from 'sonner';

const themes: Record<DocumentStyling['theme'], DocumentStyling> = {
  professional: {
    theme: 'professional',
    fontFamily: 'Helvetica',
    fontSize: 12,
    lineHeight: 1.5,
    margins: { top: 40, right: 40, bottom: 40, left: 40 },
    colors: {
      primary: '#1a73e8',
      secondary: '#4285f4',
      text: '#202124',
      background: '#ffffff',
    },
  },
  light: {
    theme: 'light',
    fontFamily: 'Arial',
    fontSize: 11,
    lineHeight: 1.4,
    margins: { top: 30, right: 30, bottom: 30, left: 30 },
    colors: {
      primary: '#2196f3',
      secondary: '#64b5f6',
      text: '#333333',
      background: '#ffffff',
    },
  },
  dark: {
    theme: 'dark',
    fontFamily: 'Roboto',
    fontSize: 11,
    lineHeight: 1.4,
    margins: { top: 30, right: 30, bottom: 30, left: 30 },
    colors: {
      primary: '#90caf9',
      secondary: '#64b5f6',
      text: '#ffffff',
      background: '#121212',
    },
  },
};

function convertToDocumentTemplate(template: any): DocumentTemplate {
  return {
    metadata: {
      id: template.metadata.id,
      version: template.metadata.version,
      name: template.metadata.name,
      description: template.metadata.description,
      category: template.metadata.category,
      jurisdiction: template.metadata.jurisdiction,
      language: template.metadata.language,
      tags: template.metadata.tags,
      lastUpdated: template.metadata.lastUpdated,
      isPublished: template.metadata.isPublished,
      baseTemplate: template.metadata.baseTemplate,
    },
    content: template.sections[0]?.content || '',
    variables: template.sections[0]?.variables || [],
  };
}

export function DocumentPreview() {
  const { formState, template } = useFormState();
  const [selectedTheme, setSelectedTheme] =
    useState<DocumentStyling['theme']>('professional');
  const [selectedFormat, setSelectedFormat] =
    useState<DocumentGenerationOptions['format']>('pdf');
  const documentService = DocumentGenerationService.getInstance();

  const handleDownload = useCallback(async () => {
    try {
      const documentTemplate = convertToDocumentTemplate(template);
      const content = await documentService.generateDocument(
        documentTemplate,
        formState,
        {
          format: selectedFormat,
          styling: themes[selectedTheme],
        }
      );

      // Handle different content types
      if (content instanceof Blob || Buffer.isBuffer(content)) {
        const blob = Buffer.isBuffer(content)
          ? new Blob([Buffer.from(content)])
          : content;
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${template.metadata.name}.${selectedFormat}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      toast.success('Document downloaded successfully');
    } catch (error) {
      console.error('Failed to generate document:', error);
      toast.error('Failed to generate document');
    }
  }, [documentService, template, formState, selectedFormat, selectedTheme]);

  return (
    <div className="flex flex-col gap-4 h-full">
      <div className="flex items-center gap-4 p-4 bg-background border rounded-lg">
        <Select
          value={selectedTheme}
          onValueChange={(value) =>
            setSelectedTheme(value as DocumentStyling['theme'])
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select theme" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="professional">Professional</SelectItem>
            <SelectItem value="light">Light</SelectItem>
            <SelectItem value="dark">Dark</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={selectedFormat}
          onValueChange={(value) =>
            setSelectedFormat(value as DocumentGenerationOptions['format'])
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select format" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pdf">PDF</SelectItem>
            <SelectItem value="docx">DOCX</SelectItem>
            <SelectItem value="html">HTML</SelectItem>
          </SelectContent>
        </Select>

        <Button onClick={handleDownload}>Download</Button>
      </div>

      <div className="flex-1 min-h-0 bg-background border rounded-lg overflow-hidden">
        <DocumentPreviewContent
          format={selectedFormat}
          template={template}
          formState={formState}
          theme={themes[selectedTheme]}
          documentService={documentService}
        />
      </div>
    </div>
  );
}

// Separate component to handle conditional rendering
interface DocumentPreviewContentProps {
  format: DocumentGenerationOptions['format'];
  template: any;
  formState: any;
  theme: DocumentStyling;
  documentService: DocumentGenerationService;
}

function DocumentPreviewContent({
  format,
  template,
  formState,
  theme,
  documentService,
}: DocumentPreviewContentProps) {
  // Use useMemo to memoize the preview component
  const previewComponent = useMemo(() => {
    const documentTemplate = convertToDocumentTemplate(template);

    if (format === 'pdf') {
      return documentService.getPreviewComponent(
        documentTemplate,
        formState,
        theme
      );
    } else if (format === 'html') {
      return documentService.getHTMLPreviewComponent(
        documentTemplate,
        formState,
        theme
      );
    } else {
      return (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          Preview not available for {format.toUpperCase()} format
        </div>
      );
    }
  }, [format, template, formState, theme, documentService]);

  return previewComponent;
}
