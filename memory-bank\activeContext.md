# Active Development Context

## Current Phase: Document Management System Enhancements (Phase 8)

### Recent Changes

- Consolidated hooks in use-supabase.ts for centralized data access
- Removed TRPC from the project completely and replaced with direct Supabase client implementation
- Implemented document sharing functionality with permissions
- Added document preview capabilities with content section rendering
- Enhanced document editor with section-based editing
- Implemented document template system with customization options
- Created document activities tracking system
- Added document collaboration features
- Implemented document version history
- Enhanced authentication with proper session management
- Improved error handling with toast notifications
- Optimized data fetching with caching strategies

### Current Focus: Document Management System Completion

#### Document Management Core Functionality

- ✅ Design document database schema with proper relationships
- ✅ Create TypeScript interfaces for document entities
- ✅ Implement document service with CRUD operations
- ✅ Build React hooks for document management
- ✅ Design and implement document list UI
- ✅ Create document detail view with content sections
- ✅ Build document editor with section management
- ✅ Implement document template system
- ✅ Add proper navigation integration
- ✅ Implement document sharing UI with permissions
- [ ] Add file upload functionality for document attachments
- [ ] Enhance document search and filtering

#### Document Organization Features

- [ ] Implement document tagging UI
- [ ] Create folder structure for document organization
- [ ] Add document favorites/starring functionality
- [ ] Implement advanced search with filters
- [ ] Create document sorting options
- [ ] Add bulk operations (delete, tag, share)

#### Document Templates and Content

- ✅ Create robust template customization
- ✅ Add template categories and filtering
- [ ] Implement template marketplace concept
- ✅ Add document creation from templates

#### Collaboration Features

- ✅ Implement document sharing with permissions
- ✅ Add comments and annotations
- ✅ Create version history tracking
- [ ] Implement real-time collaborative editing
- [ ] Add change tracking and approval workflow

### Document Management Architecture

```mermaid
flowchart TD
    User[User Interface] --> Hooks[React Hooks]
    Hooks --> Service[Document Service]
    Service --> Database[Supabase Database]

    User --> ListPage[Document List]
    User --> DetailPage[Document Detail]
    User --> EditPage[Document Edit]
    User --> SharePage[Document Sharing]

    ListPage --> DocumentCard[Document Card]
    ListPage --> DocumentTable[Document Table]
    DetailPage --> ContentViewer[Content Viewer]
    DetailPage --> MetadataPanel[Metadata Panel]
    DetailPage --> ActivityPanel[Activity Panel]
    EditPage --> SectionEditor[Section Editor]
    SharePage --> PermissionControls[Permission Controls]

    Service --> Database[Supabase Database]
    Service --> RealTimeSubscription[Realtime Subscription]

    Database --> Documents[Documents Table]
    Database --> Tags[Document Tags]
    Database --> Junction[Document-Tag Junction]
    Database --> Activities[Document Activities]
    Database --> Collaborations[Document Collaborations]
    Database --> ShareLinks[Document Share Links]
    Database --> Versions[Document Versions]
```

### Implementation Steps

#### Current Sprint Tasks

- [ ] Complete file upload functionality for document attachments
- [ ] Enhance document search and filtering capabilities
- [ ] Implement document tagging UI
- [ ] Create folder organization structure
- [ ] Add document favorites/starring functionality
- [ ] Improve mobile responsiveness for document editing
- [ ] Fix any TypeScript errors in document-related components
- [ ] Optimize document loading performance

#### Next Sprint Tasks

- [ ] Implement advanced search with filters
- [ ] Add bulk operations (delete, tag, share)
- [ ] Implement template marketplace concept
- [ ] Implement real-time collaborative editing
- [ ] Add change tracking and approval workflow
- [ ] Create comprehensive testing suite for document features

### Completed Features

#### Authentication and User Management

- ✅ Implement Supabase authentication
- ✅ Create user profiles with role-based access
- ✅ Add session management
- ✅ Implement user settings
- ✅ Create profile editing functionality
- ✅ Add avatar management

#### Document Management Core

- ✅ Design and implement document database schema
- ✅ Create document service with CRUD operations
- ✅ Build document list views (card and table)
- ✅ Implement document detail view
- ✅ Create document editor with sections
- ✅ Add document sharing functionality
- ✅ Implement document version history
- ✅ Create document activities tracking

#### Lawyer Integration

- ✅ Design lawyer profiles system
- ✅ Implement consultation scheduling
- ✅ Create messaging system
- ✅ Add document review workflow
- ✅ Implement lawyer availability management
- ✅ Add consultation history

#### UI Components

- ✅ Implement enhanced navigation
- ✅ Create responsive layouts
- ✅ Add animations for transitions
- ✅ Implement toast notifications
- ✅ Create form components
- ✅ Add document preview functionality

### Updated Dashboard Hierarchy

```mermaid
flowchart TD
    Dashboard[Dashboard] --> Home
    Dashboard --> DocCreator[Document Creator]
    Dashboard --> SmartContracts[Smart Contracts]
    Dashboard --> LawyerSupport[Lawyer Support]
    Dashboard --> CollabHub[Collaboration Hub]
    Dashboard --> Settings

    Home --> HomeOverview[Overview]
    Home --> QuickStart[Quick Start]

    DocCreator --> TemplateLib[Template Library]
    DocCreator --> Editor
    DocCreator --> Drafts

    SmartContracts --> CreateSC[Create Smart Contract]
    SmartContracts --> ActiveContracts[Active Contracts]
    SmartContracts --> ContractHistory[History]

    LawyerSupport --> ScheduleConsult[Schedule Consultation]
    LawyerSupport --> PastConsult[Past Consultations]
    LawyerSupport --> AskQuestion[Ask a Question]

    CollabHub --> SharedDocs[Shared Documents]
    CollabHub --> NegotiationTracker[Negotiation Tracker]
    CollabHub --> TeamWorkspace[Team Workspace]

    Settings --> Account
    Settings --> Subscription
    Settings --> Security
    Settings --> Notifications
    Settings --> Preferences

    Account --> AccountDetails[Personal & Billing Info]
    Subscription --> SubDetails[Plan & Usage]
    Security --> SecurityOptions[2FA & Wallet]
    Notifications --> NotifPrefs[Alert Preferences]
    Preferences --> DisplaySettings[Display Settings]
```

##### Home

Purpose: Overview of user activity and quick access to core features.

- **Overview**: Summary dashboard with widgets for active documents, deadlines, consultations, and subscription status.
- **Quick Start**: Links to create documents, schedule consultations, or browse templates.

##### Document Creator

Purpose: Core feature for creating and customizing legal documents with AI and templates.

- **Template Library**: Searchable catalog of legal templates, filterable by industry or type.
- **Editor**: Interactive workspace for customizing templates with AI suggestions and export options.
- **Drafts**: List of in-progress documents with edit or delete options.

##### Smart Contracts

Purpose: Manages blockchain-enabled smart contracts for secure execution and storage.

- **Create Smart Contract**: Wizard to set up smart contracts with blockchain deployment.
- **Active Contracts**: Displays live smart contracts with status, timestamps, and signatures.
- **History**: Archives completed or expired smart contracts with downloadable records.

##### Lawyer Support

Purpose: Connects users with licensed lawyers for advice and consultations.

- **Schedule Consultation**: Calendar to book lawyer sessions with rates and specialties.
- **Past Consultations**: Logs previous sessions with notes and billing details.
- **Ask a Question**: Form for submitting quick legal queries (pay-per-use).

##### Collaboration Hub

Purpose: Facilitates document sharing, negotiation, and team workflows.

- **Shared Documents**: Lists shared documents with status and secure link options.
- **Negotiation Tracker**: Shows version history and comments for documents under negotiation.
- **Team Workspace**: (Enterprise Plan only) Space for team collaboration with role-based access.

##### Settings

Purpose: Manages user account details, subscription plans, security, and platform preferences.

- **Account**: Manages personal and billing information.

  - Details: Edit name, email, contact details; view billing history, update payment methods.
  - Visual: Form with fields for "Name," "Email," and a "Billing History" table.

- **Subscription**: Controls plan selection and upgrades, with usage stats.

  - Details: Current plan display, upgrade/downgrade options, usage metrics, pay-per-use purchases.
  - Visual: Card showing current plan with "Change Plan" button and usage bar.

- **Security**: Configures login settings and blockchain wallet integration.

  - Details: Two-factor authentication (2FA), password reset, blockchain wallet connection.
  - Visual: Toggle switches for 2FA, "Connect Wallet" button, lock icon.

- **Notifications**: Customizes alert preferences for deadlines, signatures, and consultations.

  - Details: Checkboxes for email, in-app, or SMS notifications.
  - Visual: List of notification types with on/off toggles and sample alerts.

- **Preferences**: Adjusts platform display and default settings.
  - Details: Light/dark mode, default export format (PDF/PNG), language options.
  - Visual: Theme dropdown, export format radio buttons, language selector.

### Technical Decisions

#### User Management Strategy

- Use role-based access control for permissions
- Implement hierarchical organization structure
- Support single sign-on integration
- Audit logging for all permission changes

#### Template Designer Approach

- Use React DnD for drag-and-drop functionality
- JSON schema for template definition
- Live preview of templates during editing
- Versioning for all template changes

#### Consulting Workflow Model

- Ticketing system approach for consulting requests
- Integrated messaging between clients and consultants
- Schedule management with calendar integration
- Document sharing and collaboration tools

### Testing Strategy

#### Unit Tests

- Test permission calculations
- Validate template designer components
- Verify consulting request state transitions
- Test notification delivery

#### Integration Tests

- Test user management across organization hierarchy
- Verify end-to-end template creation and publishing
- Test consulting workflows from request to completion
- Validate reporting and analytics

#### User Testing

- Conduct usability testing with enterprise customers
- Gather feedback on template designer usability
- Test performance with large organizations
- Validate self-service consulting request flows

### Success Metrics

#### Enterprise Adoption

- Number of enterprise organizations onboarded
- User engagement across roles
- Template usage metrics
- Consulting request volume

#### User Experience

- Time to create templates
- Form completion rates
- Support ticket volume
- Customer satisfaction scores

### Next Milestone

Complete enterprise UI components and implement consulting request workflow by end of sprint

### Known Issues

#### Document Management

- File upload functionality needs completion
- Document search and filtering needs enhancement
- Document tagging UI needs implementation
- Mobile responsiveness needs improvement for document editing
- Some TypeScript errors in document-related components
- Document loading performance could be optimized

#### Lawyer Integration

- Calendar integration needs completion
- Recurring consultations need testing
- Notification system for consultations needs enhancement
- Document review workflow needs refinement

#### UI Components

- Some animations may need optimization for older devices
- Need to implement reduced motion preferences support
- Form transitions need animation implementation
- Mobile responsiveness needs improvement in some areas

### Technical Debt

- Consolidate all hooks in use-supabase.ts
- Remove any unused code or components
- Fix TypeScript errors throughout the codebase
- Improve error handling consistency
- Enhance test coverage

### Launch Priorities

1. Complete file upload functionality
2. Enhance document search and filtering
3. Fix TypeScript errors
4. Optimize performance
5. Improve mobile responsiveness
6. Complete testing suite

### Development Environment

- Next.js 15 with App Router
- React 19 with Server Components
- TypeScript for type safety
- Supabase for authentication and database
- Zustand for state management
- Tailwind CSS for styling
- Shadcn/UI for components
- Bun for package management
