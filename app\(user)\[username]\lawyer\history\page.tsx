import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { avatar_male_001 } from '@/lib/imgs';
import { pageProps } from '@/types';
import {
  Calendar,
  Check,
  CheckCircle2,
  Clock,
  Download,
  FileText,
  Filter,
  Gavel,
  MessageSquare,
  MoreHorizontal,
  Paperclip,
  Search,
  Star,
  Video,
} from 'lucide-react';
import Image from 'next/image';

export default async function ConsultationHistoryPage({ params }: pageProps) {
  const username = (await params).username;

  // Mock consultations data
  const consultations = [
    {
      id: 1,
      lawyer: {
        name: 'Sarah <PERSON>',
        avatar: '/avatars/sarah-johnson.jpg',
        specialization: 'Contract Law',
      },
      date: '2023-12-01T10:00:00',
      duration: '60 min',
      status: 'Completed',
      topic: 'Employment Contract Review',
      documents: 2,
      notes: true,
      recording: true,
      rating: 4.8,
      cost: '$275',
      invoiceId: 'INV-2023-001',
    },
    {
      id: 2,
      lawyer: {
        name: 'Michael Chen',
        avatar: '/avatars/michael-chen.jpg',
        specialization: 'Intellectual Property',
      },
      date: '2023-11-15T14:30:00',
      duration: '45 min',
      status: 'Completed',
      topic: 'Patent Application Strategy',
      documents: 1,
      notes: true,
      recording: false,
      rating: 4.5,
      cost: '$180',
      invoiceId: 'INV-2023-002',
    },
    {
      id: 3,
      lawyer: {
        name: 'Elena Rodriguez',
        avatar: '/avatars/elena-rodriguez.jpg',
        specialization: 'Corporate Law',
      },
      date: '2023-12-10T11:00:00',
      duration: '30 min',
      status: 'Scheduled',
      topic: 'Series A Funding Legal Review',
      documents: 0,
      notes: false,
      recording: false,
      cost: '$145',
    },
    {
      id: 4,
      lawyer: {
        name: 'James Wilson',
        avatar: '/avatars/james-wilson.jpg',
        specialization: 'Blockchain & Cryptocurrency',
      },
      date: '2023-11-28T16:00:00',
      duration: '60 min',
      status: 'Completed',
      topic: 'Smart Contract Legal Implications',
      documents: 3,
      notes: true,
      recording: true,
      rating: 4.9,
      cost: '$260',
      invoiceId: 'INV-2023-003',
    },
    {
      id: 5,
      lawyer: {
        name: 'David Park',
        avatar: '/avatars/david-park.jpg',
        specialization: 'Technology Law',
      },
      date: '2023-12-15T13:30:00',
      duration: '45 min',
      status: 'Scheduled',
      topic: 'Software Licensing Agreement',
      documents: 1,
      notes: false,
      recording: false,
      cost: '$190',
    },
    {
      id: 6,
      lawyer: {
        name: 'Amanda Thompson',
        avatar: '/avatars/amanda-thompson.jpg',
        specialization: 'Securities Law',
      },
      date: '2023-11-20T09:00:00',
      duration: '60 min',
      status: 'Cancelled',
      topic: 'Securities Offering Compliance',
      documents: 0,
      notes: false,
      recording: false,
      cost: '$265',
      invoiceId: 'INV-2023-004',
      refundStatus: 'Partial',
    },
  ];

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Function to format time
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <Gavel className="size-5 text-neutral-500" />
              <h1 className="text-2xl font-bold">Consultation History</h1>
            </div>
            <p className="text-neutral-500 mt-1">
              View and manage your legal consultation sessions
            </p>
          </div>
          <Button className="gap-1">
            <Calendar className="size-4" />
            <span>Schedule New Consultation</span>
          </Button>
        </div>
      </section>

      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div className="relative w-full md:max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 size-4" />
            <Input className="pl-10" placeholder="Search consultations..." />
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <div className="flex items-center">
              <Button variant="outline" size="sm" className="h-9 gap-1">
                <Filter className="size-4" />
                <span>Filters</span>
              </Button>

              <Select defaultValue="newest">
                <SelectTrigger className="w-[160px] ml-2 h-9">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="cost_high">Cost: High to Low</SelectItem>
                  <SelectItem value="cost_low">Cost: Low to High</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs defaultValue="all" className="ml-2">
              <TabsList className="h-9">
                <TabsTrigger value="all" className="px-3">
                  All
                </TabsTrigger>
                <TabsTrigger value="completed" className="px-3">
                  Completed
                </TabsTrigger>
                <TabsTrigger value="scheduled" className="px-3">
                  Scheduled
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Lawyer</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead className="hidden md:table-cell">Topic</TableHead>
                <TableHead className="text-center">Status</TableHead>
                <TableHead className="hidden lg:table-cell">Duration</TableHead>
                <TableHead className="text-right hidden md:table-cell">
                  Cost
                </TableHead>
                <TableHead className="hidden lg:table-cell">
                  Resources
                </TableHead>
                <TableHead className="w-[60px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {consultations.map((consultation) => (
                <TableRow key={consultation.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Image
                        src={avatar_male_001}
                        alt={consultation.lawyer.name}
                        width={40}
                        height={40}
                        className="size-10 rounded-full"
                      />
                      <div>
                        <div className="font-medium">
                          {consultation.lawyer.name}
                        </div>
                        <div className="text-sm text-neutral-500">
                          {consultation.lawyer.specialization}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {formatDate(consultation.date)}
                    </div>
                    <div className="flex items-center gap-1 text-sm text-neutral-500">
                      <Clock className="size-3.5" />
                      <span>{formatTime(consultation.date)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell max-w-[200px] truncate">
                    {consultation.topic}
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge className={getStatusColor(consultation.status)}>
                      {consultation.status}
                    </Badge>
                    {consultation.status === 'Cancelled' &&
                      consultation.refundStatus && (
                        <div className="text-xs text-neutral-500 mt-1">
                          {consultation.refundStatus} refund
                        </div>
                      )}
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    {consultation.duration}
                  </TableCell>
                  <TableCell className="text-right hidden md:table-cell font-medium">
                    {consultation.cost}
                    {consultation.invoiceId && (
                      <div className="text-xs text-neutral-500">
                        {consultation.invoiceId}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-2">
                      {consultation.documents > 0 && (
                        <div className="flex items-center gap-1 text-sm">
                          <Paperclip className="size-3.5 text-neutral-500" />
                          <span>{consultation.documents}</span>
                        </div>
                      )}
                      {consultation.notes && (
                        <div
                          className="size-7 bg-blue-100 rounded-full flex items-center justify-center"
                          title="Consultation notes available"
                        >
                          <FileText className="size-3.5 text-blue-600" />
                        </div>
                      )}
                      {consultation.recording && (
                        <div
                          className="size-7 bg-purple-100 rounded-full flex items-center justify-center"
                          title="Recording available"
                        >
                          <Video className="size-3.5 text-purple-600" />
                        </div>
                      )}
                      {consultation.rating && (
                        <div className="flex items-center gap-0.5">
                          <Star className="size-3.5 fill-amber-400 text-amber-400" />
                          <span className="text-sm font-medium">
                            {consultation.rating}
                          </span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {consultation.status === 'Completed' && (
                          <>
                            <DropdownMenuItem className="gap-2">
                              <FileText className="size-4" />
                              <span>View Summary</span>
                            </DropdownMenuItem>
                            {consultation.recording && (
                              <DropdownMenuItem className="gap-2">
                                <Video className="size-4" />
                                <span>Watch Recording</span>
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="gap-2">
                              <Download className="size-4" />
                              <span>Download Invoice</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                          </>
                        )}
                        {consultation.status === 'Scheduled' && (
                          <>
                            <DropdownMenuItem className="gap-2">
                              <Calendar className="size-4" />
                              <span>Reschedule</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="gap-2">
                              <Check className="size-4" />
                              <span>Confirm Attendance</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="gap-2 text-red-600">
                              <span>Cancel Consultation</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                          </>
                        )}
                        <DropdownMenuItem className="gap-2">
                          <MessageSquare className="size-4" />
                          <span>Message Lawyer</span>
                        </DropdownMenuItem>
                        {consultation.status === 'Completed' &&
                          !consultation.rating && (
                            <DropdownMenuItem className="gap-2">
                              <Star className="size-4" />
                              <span>Leave Rating</span>
                            </DropdownMenuItem>
                          )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Consultation Summary</h3>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-full bg-green-100 flex items-center justify-center">
                    <CheckCircle2 className="size-4 text-green-600" />
                  </div>
                  <span>Completed</span>
                </div>
                <span className="font-medium">3</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <Calendar className="size-4 text-blue-600" />
                  </div>
                  <span>Scheduled</span>
                </div>
                <span className="font-medium">2</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-full bg-red-100 flex items-center justify-center">
                    <Clock className="size-4 text-red-600" />
                  </div>
                  <span>Cancelled</span>
                </div>
                <span className="font-medium">1</span>
              </div>
              <div className="pt-2 mt-2 border-t">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Total Spent:</span>
                  <span className="font-medium">$1,060</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Most Consulted Topics</h3>
              <Button variant="ghost" size="sm">
                Details
              </Button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-blue-100 flex items-center justify-center">
                    <FileText className="size-4 text-blue-600" />
                  </div>
                  <span>Contract Review</span>
                </div>
                <span className="font-medium">2</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-purple-100 flex items-center justify-center">
                    <FileText className="size-4 text-purple-600" />
                  </div>
                  <span>Intellectual Property</span>
                </div>
                <span className="font-medium">1</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-green-100 flex items-center justify-center">
                    <FileText className="size-4 text-green-600" />
                  </div>
                  <span>Corporate Law</span>
                </div>
                <span className="font-medium">1</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-amber-100 flex items-center justify-center">
                    <FileText className="size-4 text-amber-600" />
                  </div>
                  <span>Blockchain Law</span>
                </div>
                <span className="font-medium">1</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Upcoming Consultations</h3>
              <Button variant="ghost" size="sm">
                Calendar
              </Button>
            </div>

            <div className="space-y-4">
              {consultations
                .filter((c) => c.status === 'Scheduled')
                .slice(0, 2)
                .map((consultation) => (
                  <div key={consultation.id} className="flex items-start gap-3">
                    <Image
                      src={avatar_male_001}
                      alt={consultation.lawyer.name}
                      width={36}
                      height={36}
                      className="size-9 rounded-full"
                    />
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <div className="font-medium">
                          {consultation.lawyer.name}
                        </div>
                        <Badge className="bg-blue-100 text-blue-800">
                          {consultation.duration}
                        </Badge>
                      </div>
                      <div className="text-sm text-neutral-500 mt-1">
                        {consultation.topic}
                      </div>
                      <div className="flex items-center gap-1 text-sm text-neutral-500 mt-1">
                        <Calendar className="size-3.5" />
                        <span>
                          {formatDate(consultation.date)},{' '}
                          {formatTime(consultation.date)}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}

              <Button variant="outline" className="w-full mt-2">
                <Calendar className="size-4 mr-1" />
                <span>Manage Schedule</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
