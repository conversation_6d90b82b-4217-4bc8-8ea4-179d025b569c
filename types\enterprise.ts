import { z } from 'zod';

// Enterprise Type Schemas
export const TeamRoleSchema = z.enum(['editor', 'viewer']);
export type TeamRole = z.infer<typeof TeamRoleSchema>;

export const PermissionActionSchema = z.enum([
  'create',
  'read',
  'update',
  'delete',
  'manage',
]);
export type PermissionAction = z.infer<typeof PermissionActionSchema>;

export const SubscriptionLevelSchema = z.enum([
  'basic',
  'professional',
  'enterprise',
]);
export type SubscriptionLevel = z.infer<typeof SubscriptionLevelSchema>;

export const RequestPrioritySchema = z.enum([
  'low',
  'medium',
  'high',
  'urgent',
]);
export type RequestPriority = z.infer<typeof RequestPrioritySchema>;

export const RequestStatusSchema = z.enum([
  'pending',
  'accepted',
  'in_progress',
  'completed',
  'cancelled',
]);
export type RequestStatus = z.infer<typeof RequestStatusSchema>;

// Enterprise Data Types
export interface Feature {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

export interface Permission {
  id: string;
  resource: string;
  action: PermissionAction;
  conditions?: Record<string, any>;
  createdAt: Date;
}

export interface TeamMember {
  id: string;
  userId: string;
  teamId: string;
  role: TeamRole;
  joinedAt: Date;
  name?: string;
  email?: string;
  avatarUrl?: string;
}

export interface Team {
  id: string;
  name: string;
  organizationId: string;
  description?: string;
  members: TeamMember[];
  permissions: Permission[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Organization {
  id: string;
  name: string;
  adminUsers: string[];
  teams: Team[];
  subscriptionLevel: SubscriptionLevel;
  features: Feature[];
  createdAt: Date;
  updatedAt: Date;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  contactEmail?: string;
  billingInfo?: {
    address: string;
    country: string;
    taxId?: string;
  };
}

export interface Attachment {
  id: string;
  filename: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  createdAt: Date;
  uploadedBy: string;
}

export interface Communication {
  id: string;
  requestId: string;
  senderId: string;
  message: string;
  attachments: Attachment[];
  createdAt: Date;
  readAt?: Date;
}

export interface ConsultingRequest {
  id: string;
  organizationId: string;
  requesterId: string;
  title: string;
  description: string;
  requirementDetails: string;
  priority: RequestPriority;
  status: RequestStatus;
  assignedConsultantId?: string;
  estimatedHours?: number;
  attachments: Attachment[];
  communications: Communication[];
  createdAt: Date;
  updatedAt: Date;
  dueDate?: Date;
  completedAt?: Date;
  tags?: string[];
}

export interface ConsultingSession {
  id: string;
  requestId: string;
  consultantId: string;
  startTime: Date;
  endTime?: Date;
  notes?: string;
  outcome?: string;
  attachments?: Attachment[];
  clientFeedback?: {
    rating: number;
    comments?: string;
  };
}
