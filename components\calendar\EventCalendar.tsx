'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { CalendarEvent, CalendarView } from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import {
  addDays,
  addMonths,
  addWeeks,
  format,
  subDays,
  subMonths,
  subWeeks,
} from 'date-fns';
import { Calendar as CalendarIcon, Clock, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { AgendaView } from './AgendaView';
import { CalendarHeader } from './CalendarHeader';
import { DayView } from './DayView';
import { MobileCalendarView } from './MobileCalendarView';
import { MonthView } from './MonthView';
import { WeekView } from './WeekView';

interface CalendarProps {
  events: CalendarEvent[];
  onEventAdd?: (event: CalendarEvent) => void;
  onEventUpdate?: (event: CalendarEvent) => void;
  onEventDelete?: (eventId: string) => void;
  className?: string;
  initialView?: CalendarView;
  isReadOnly?: boolean;
  lawyerId?: string;
  clientId?: string;
  onDateSelect?: (date: Date) => void;
  onViewChange?: (view: CalendarView) => void;
}

export function EventCalendar({
  events,
  onEventAdd,
  onEventUpdate,
  onEventDelete,
  className,
  initialView = 'month',
  isReadOnly = false,
  lawyerId,
  clientId,
  onDateSelect,
  onViewChange,
}: CalendarProps) {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [view, setView] = useState<CalendarView>(initialView);
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(
    null
  );
  const [isNewEvent, setIsNewEvent] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if the device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIsMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIsMobile);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // Form state
  const [eventTitle, setEventTitle] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventStart, setEventStart] = useState<Date>(new Date());
  const [eventEnd, setEventEnd] = useState<Date>(new Date());
  const [eventAllDay, setEventAllDay] = useState(false);
  const [eventColor, setEventColor] = useState<CalendarEvent['color']>('sky');
  const [eventLocation, setEventLocation] = useState('');
  const [eventConsultationType, setEventConsultationType] = useState<
    'video' | 'document'
  >('video');

  // Update view when initialView changes
  useEffect(() => {
    setView(initialView);
  }, [initialView]);

  // Navigate to today
  const handleToday = () => {
    setCurrentDate(new Date());
  };

  // Navigate to previous period
  const handlePrevious = () => {
    switch (view) {
      case 'month':
        setCurrentDate(subMonths(currentDate, 1));
        break;
      case 'week':
        setCurrentDate(subWeeks(currentDate, 1));
        break;
      case 'day':
        setCurrentDate(subDays(currentDate, 1));
        break;
      case 'agenda':
        setCurrentDate(subDays(currentDate, 7));
        break;
    }
  };

  // Navigate to next period
  const handleNext = () => {
    switch (view) {
      case 'month':
        setCurrentDate(addMonths(currentDate, 1));
        break;
      case 'week':
        setCurrentDate(addWeeks(currentDate, 1));
        break;
      case 'day':
        setCurrentDate(addDays(currentDate, 1));
        break;
      case 'agenda':
        setCurrentDate(addDays(currentDate, 7));
        break;
    }
  };

  // Handle view change
  const handleViewChange = (newView: CalendarView) => {
    setView(newView);
    if (onViewChange) {
      onViewChange(newView);
    }
  };

  // Handle event click
  const handleEventClick = (event: CalendarEvent) => {
    if (isReadOnly) return;

    setSelectedEvent(event);
    setEventTitle(event.title);
    setEventDescription(event.description || '');
    setEventStart(new Date(event.start));
    setEventEnd(new Date(event.end));
    setEventAllDay(event.allDay || false);
    setEventColor(event.color || 'sky');
    setEventLocation(event.location || '');
    setEventConsultationType(event.consultationType || 'video');
    setIsNewEvent(false);
    setIsEventDialogOpen(true);
  };

  // Handle date click
  const handleDateClick = (date: Date) => {
    if (isReadOnly) {
      if (onDateSelect) {
        onDateSelect(date);
      }
      return;
    }

    // Create a new event at the clicked date
    const endDate = new Date(date);
    endDate.setHours(date.getHours() + 1);

    setSelectedEvent(null);
    setEventTitle('');
    setEventDescription('');
    setEventStart(date);
    setEventEnd(endDate);
    setEventAllDay(false);
    setEventColor('sky');
    setEventLocation('');
    setEventConsultationType('video');
    setIsNewEvent(true);
    setIsEventDialogOpen(true);
  };

  // Handle save event
  const handleSaveEvent = () => {
    if (!eventTitle.trim()) return;

    const eventData: CalendarEvent = {
      id: selectedEvent?.id || `event-${Date.now()}`,
      title: eventTitle.trim(),
      description: eventDescription.trim() || undefined,
      start: eventStart,
      end: eventEnd,
      allDay: eventAllDay,
      color: eventColor,
      location: eventLocation || undefined,
      consultationType: eventConsultationType,
      clientId,
      lawyerId,
    };

    if (isNewEvent) {
      if (onEventAdd) {
        onEventAdd(eventData);
      }
    } else {
      if (onEventUpdate) {
        onEventUpdate(eventData);
      }
    }

    setIsEventDialogOpen(false);
  };

  // Handle delete event
  const handleDeleteEvent = () => {
    if (selectedEvent && onEventDelete) {
      onEventDelete(selectedEvent.id);
    }

    setIsDeleteDialogOpen(false);
    setIsEventDialogOpen(false);
  };

  return (
    <div className={cn('space-y-4', className)}>
      {!isMobile && (
        <CalendarHeader
          currentDate={currentDate}
          view={view}
          onPrevious={handlePrevious}
          onNext={handleNext}
          onToday={handleToday}
          onViewChange={handleViewChange}
        />
      )}

      {isMobile ? (
        <MobileCalendarView
          currentDate={currentDate}
          events={events}
          onEventClick={handleEventClick}
          onDateClick={handleDateClick}
          isReadOnly={isReadOnly}
        />
      ) : (
        <>
          {view === 'month' && (
            <MonthView
              currentDate={currentDate}
              events={events}
              onEventClick={handleEventClick}
              onDateClick={handleDateClick}
              isReadOnly={isReadOnly}
            />
          )}

          {view === 'week' && (
            <WeekView
              currentDate={currentDate}
              events={events}
              onEventClick={handleEventClick}
              onDateClick={handleDateClick}
              isReadOnly={isReadOnly}
            />
          )}

          {view === 'day' && (
            <DayView
              currentDate={currentDate}
              events={events}
              onEventClick={handleEventClick}
              onTimeClick={handleDateClick}
              isReadOnly={isReadOnly}
            />
          )}

          {view === 'agenda' && (
            <AgendaView
              currentDate={currentDate}
              events={events}
              onEventClick={handleEventClick}
              isReadOnly={isReadOnly}
            />
          )}
        </>
      )}

      {/* Event dialog */}
      {!isReadOnly && (
        <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {isNewEvent ? 'Add Event' : 'Edit Event'}
              </DialogTitle>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={eventTitle}
                  onChange={(e) => setEventTitle(e.target.value)}
                  placeholder="Event title"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={eventDescription}
                  onChange={(e) => setEventDescription(e.target.value)}
                  placeholder="Event description"
                  className="min-h-[100px]"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label>Start</Label>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'justify-start text-left font-normal',
                            !eventStart && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {eventStart
                            ? format(eventStart, 'PPP')
                            : 'Pick a date'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={eventStart}
                          onSelect={(date) => date && setEventStart(date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                    {!eventAllDay && (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline">
                            <Clock className="mr-2 h-4 w-4" />
                            {eventStart ? format(eventStart, 'HH:mm') : '00:00'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-4">
                          <div className="flex flex-col gap-2">
                            <Select
                              value={format(eventStart, 'HH')}
                              onValueChange={(value) => {
                                const newDate = new Date(eventStart);
                                newDate.setHours(parseInt(value));
                                setEventStart(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Hour" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from({ length: 24 }, (_, i) => (
                                  <SelectItem
                                    key={i}
                                    value={i.toString().padStart(2, '0')}
                                  >
                                    {i.toString().padStart(2, '0')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>

                            <Select
                              value={format(eventStart, 'mm')}
                              onValueChange={(value) => {
                                const newDate = new Date(eventStart);
                                newDate.setMinutes(parseInt(value));
                                setEventStart(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Minute" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from(
                                  { length: 4 },
                                  (_, i) => i * 15
                                ).map((minute) => (
                                  <SelectItem
                                    key={minute}
                                    value={minute.toString().padStart(2, '0')}
                                  >
                                    {minute.toString().padStart(2, '0')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label>End</Label>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            'justify-start text-left font-normal',
                            !eventEnd && 'text-muted-foreground'
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {eventEnd ? format(eventEnd, 'PPP') : 'Pick a date'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={eventEnd}
                          onSelect={(date) => date && setEventEnd(date)}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                    {!eventAllDay && (
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline">
                            <Clock className="mr-2 h-4 w-4" />
                            {eventEnd ? format(eventEnd, 'HH:mm') : '00:00'}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-4">
                          <div className="flex flex-col gap-2">
                            <Select
                              value={format(eventEnd, 'HH')}
                              onValueChange={(value) => {
                                const newDate = new Date(eventEnd);
                                newDate.setHours(parseInt(value));
                                setEventEnd(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Hour" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from({ length: 24 }, (_, i) => (
                                  <SelectItem
                                    key={i}
                                    value={i.toString().padStart(2, '0')}
                                  >
                                    {i.toString().padStart(2, '0')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>

                            <Select
                              value={format(eventEnd, 'mm')}
                              onValueChange={(value) => {
                                const newDate = new Date(eventEnd);
                                newDate.setMinutes(parseInt(value));
                                setEventEnd(newDate);
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Minute" />
                              </SelectTrigger>
                              <SelectContent>
                                {Array.from(
                                  { length: 4 },
                                  (_, i) => i * 15
                                ).map((minute) => (
                                  <SelectItem
                                    key={minute}
                                    value={minute.toString().padStart(2, '0')}
                                  >
                                    {minute.toString().padStart(2, '0')}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </PopoverContent>
                      </Popover>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  id="all-day"
                  checked={eventAllDay}
                  onCheckedChange={setEventAllDay}
                />
                <Label htmlFor="all-day">All day</Label>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={eventLocation}
                  onChange={(e) => setEventLocation(e.target.value)}
                  placeholder="Event location"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="consultation-type">Consultation Type</Label>
                <Select
                  value={eventConsultationType}
                  onValueChange={(value) =>
                    setEventConsultationType(value as 'video' | 'document')
                  }
                >
                  <SelectTrigger id="consultation-type">
                    <SelectValue placeholder="Select consultation type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="video">Video Consultation</SelectItem>
                    <SelectItem value="document">Document Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="color">Color</Label>
                <Select
                  value={eventColor || 'sky'}
                  onValueChange={(value) =>
                    setEventColor(value as CalendarEvent['color'])
                  }
                >
                  <SelectTrigger id="color">
                    <SelectValue placeholder="Select color" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sky">Blue</SelectItem>
                    <SelectItem value="amber">Yellow</SelectItem>
                    <SelectItem value="violet">Purple</SelectItem>
                    <SelectItem value="rose">Red</SelectItem>
                    <SelectItem value="emerald">Green</SelectItem>
                    <SelectItem value="orange">Orange</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter className="flex justify-between">
              <div>
                {!isNewEvent && (
                  <Button
                    variant="shadow_red"
                    onClick={() => setIsDeleteDialogOpen(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsEventDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleSaveEvent} disabled={!eventTitle.trim()}>
                  Save
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete confirmation dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Event</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              Are you sure you want to delete this event? This action cannot be
              undone.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="shadow_red" onClick={handleDeleteEvent}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
