'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useBookingsRealtime, useLawyerMessages } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { LawyerConsultation } from '@/lib/types/database-modules';
import { format } from 'date-fns';
import {
  BarChart,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  MessageSquare,
  Users,
  Video,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

export function ConsultationDashboard() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  const { consultations, loading, cancelConsultation } = useBookingsRealtime();

  const {
    documentReviews,
    loading: reviewsLoading,
    unreadCount,
  } = useLawyerMessages('');

  const [activeTab, setActiveTab] = useState('overview');

  // Filter consultations by status
  const upcomingConsultations = consultations.filter(
    (consultation) =>
      consultation.status === 'scheduled' || consultation.status === 'confirmed'
  );

  const pastConsultations = consultations.filter(
    (consultation) =>
      consultation.status === 'completed' || consultation.status === 'cancelled'
  );

  const pendingReviews = documentReviews.filter(
    (review) => review.status === 'pending'
  );

  // Stats for the overview
  const stats = {
    upcoming: upcomingConsultations.length,
    completed: consultations.filter((c) => c.status === 'completed').length,
    cancelled: consultations.filter((c) => c.status === 'cancelled').length,
    pendingReviews: pendingReviews.length,
    unreadMessages: unreadCount,
  };

  // Handle consultation cancellation
  const handleCancelConsultation = async (consultationId: string) => {
    if (confirm('Are you sure you want to cancel this consultation?')) {
      toast.promise(cancelConsultation(consultationId), {
        loading: 'Cancelling consultation...',
        success: 'Consultation cancelled successfully',
        error: (err) =>
          `Failed to cancel consultation: ${err.message || 'Unknown error'}`,
      });
    }
  };

  // Render a consultation card
  const renderConsultationCard = (consultation: LawyerConsultation) => {
    // Handle null consultation_date safely
    const consultationDate = consultation.consultation_date
      ? new Date(consultation.consultation_date)
      : new Date();

    const isUpcoming =
      consultation.status === 'scheduled' ||
      consultation.status === 'confirmed';

    // Extract lawyer and client data from consultation
    // These are joined in the useBookingsRealtime hook
    const lawyer = (consultation as any).lawyer;
    const client = (consultation as any).client;
    const document = (consultation as any).document;

    return (
      <Card key={consultation.id} className="mb-4">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">
                  {format(consultationDate, 'MMMM d, yyyy')}
                </span>
                <span className="text-muted-foreground">at</span>
                <span className="font-medium">
                  {format(consultationDate, 'h:mm a')}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{consultation.duration_minutes || 60} minutes</span>
                <Badge variant={isUpcoming ? 'default' : 'outline'}>
                  {(consultation.status || 'scheduled')
                    .charAt(0)
                    .toUpperCase() +
                    (consultation.status || 'scheduled').slice(1)}
                </Badge>
              </div>

              {lawyer && (
                <div className="flex items-center gap-2 mt-2">
                  <UserAvatar
                    size="sm"
                    avatarUrl={lawyer.avatar_url}
                    fallbackText={lawyer.full_name}
                    className="h-6 w-6"
                  />
                  <span className="font-medium">{lawyer.full_name}</span>
                </div>
              )}

              {client && (
                <div className="flex items-center gap-2 mt-2">
                  <UserAvatar
                    size="sm"
                    avatarUrl={client.avatar_url}
                    fallbackText={client.full_name}
                    className="h-6 w-6"
                  />
                  <span className="font-medium">{client.full_name}</span>
                </div>
              )}

              {document && (
                <div className="flex items-center gap-2 mt-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span>{document.title}</span>
                </div>
              )}

              {consultation.notes && (
                <div className="mt-2 text-sm text-muted-foreground">
                  <p className="line-clamp-2">{consultation.notes}</p>
                </div>
              )}
            </div>

            <div className="flex flex-col gap-2">
              {isUpcoming && (
                <>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() =>
                      router.push(
                        `/${username}/lawyer/consultations/${consultation.id}`
                      )
                    }
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Join Meeting
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancelConsultation(consultation.id)}
                  >
                    Cancel
                  </Button>
                </>
              )}
              {!isUpcoming && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    router.push(
                      `/${username}/lawyer/consultations/${consultation.id}`
                    )
                  }
                >
                  View Details
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="upcoming">
            Upcoming ({stats.upcoming})
          </TabsTrigger>
          <TabsTrigger value="past">Past Consultations</TabsTrigger>
          <TabsTrigger value="reviews">
            Document Reviews ({stats.pendingReviews})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Upcoming Consultations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-2xl font-bold">{stats.upcoming}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveTab('upcoming')}
                  >
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Completed Consultations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {stats.completed}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveTab('past')}
                  >
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Reviews
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {stats.pendingReviews}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setActiveTab('reviews')}
                  >
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Unread Messages
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-2xl font-bold">
                      {stats.unreadMessages}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/${username}/messages`)}
                  >
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {upcomingConsultations.length > 0 && (
            <div className="mt-8">
              <h3 className="text-lg font-medium mb-4">Next Consultation</h3>
              {renderConsultationCard(upcomingConsultations[0])}
            </div>
          )}

          <div className="flex gap-4 mt-8">
            <Button
              onClick={() => {
                router.push(`/${username}/lawyer/find`);
              }}
            >
              <Users className="h-4 w-4 mr-2" />
              Find Lawyers
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                router.push(`/${username}/lawyer/consultations`);
              }}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Book Consultation
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4 mt-6">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : upcomingConsultations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Upcoming Consultations
                  </h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    You don't have any scheduled consultations. Book a
                    consultation with a lawyer to get legal advice.
                  </p>
                  <Button
                    onClick={() => {
                      router.push(`/${username}/lawyer/find`);
                    }}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Book Consultation
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            upcomingConsultations.map(renderConsultationCard)
          )}
        </TabsContent>

        <TabsContent value="past" className="space-y-4 mt-6">
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : pastConsultations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <BarChart className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Past Consultations
                  </h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    You haven't had any consultations yet. Book a consultation
                    with a lawyer to get legal advice.
                  </p>
                  <Button
                    onClick={() => {
                      router.push(`/${username}/lawyer/find`);
                    }}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Book Consultation
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            pastConsultations.map(renderConsultationCard)
          )}
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4 mt-6">
          {reviewsLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-[150px] w-full rounded-md" />
              <Skeleton className="h-[150px] w-full rounded-md" />
            </div>
          ) : documentReviews.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    No Document Reviews
                  </h3>
                  <p className="text-muted-foreground mb-4 max-w-md">
                    You haven't submitted any documents for review. Submit a
                    document to get legal advice.
                  </p>
                  <Button
                    onClick={() => {
                      router.push(`/${username}/lawyer/consultations`);
                    }}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Submit Document for Review
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            documentReviews.map((review) => (
              <Card key={review.id} className="mb-4">
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">
                          {review.document?.title || 'Document'}
                        </span>
                        <Badge
                          variant={
                            review.status === 'completed'
                              ? 'default'
                              : review.status === 'in_progress'
                                ? 'secondary'
                                : 'outline'
                          }
                        >
                          {review.status
                            .replace('_', ' ')
                            .charAt(0)
                            .toUpperCase() +
                            review.status.replace('_', ' ').slice(1)}
                        </Badge>
                      </div>

                      {review.lawyer && (
                        <div className="flex items-center gap-2 mt-2">
                          <UserAvatar
                            size="sm"
                            avatarUrl={review.lawyer.avatar_url}
                            fallbackText={review.lawyer.full_name}
                            className="h-6 w-6"
                          />
                          <span className="font-medium">
                            {review.lawyer.full_name}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center gap-2 mt-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          Submitted on{' '}
                          {format(new Date(review.created_at), 'MMMM d, yyyy')}
                        </span>
                      </div>

                      {review.review_notes && (
                        <div className="mt-2 text-sm text-muted-foreground">
                          <p className="line-clamp-2">{review.review_notes}</p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2">
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() =>
                          router.push(
                            `/${username}/lawyer/reviews/${review.id}`
                          )
                        }
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
