import { Button, buttonVariants } from '@/components/ui/button';
import { StrippedBg } from '@/components/ui/stripped-bg';
import { BlurInSection } from '@/components/ux/animations/blur-in';
import { Magnetic } from '@/components/ux/animations/magnetic';
import { TextEffect } from '@/components/ux/animations/text-effect';
import { LeftDashes } from '@/components/ux/lines/left-dashes';
import { RightDashes } from '@/components/ux/lines/right-dashes';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import { cn, constructMetadata } from '@/lib/utils';
import Logo from '@/public/FormsLogo.svg';
import Image from 'next/image';
import Link from 'next/link';

export const metadata = constructMetadata();

export default function Home() {
  return (
    <main className="p-4 h-dvh overflow-hidden">
      <section className="mx-auto flex max-w-6xl h-full relative  flex-col items-center justify-end">
        {/* stripped bg */}
        <StrippedBg />
        {/* Vertical Lines */}
        <div className="pointer-events-none inset-0 select-none">
          <LeftDashes />
          <RightDashes />
        </div>
        <BlurInSection className="flex flex-col items-center relative justify-center h-fit w-full max-w-sm mb-20 divide-accent-50/40  p-4">
          <div className="flex flex-col text-center items-center p-2 space-y-2">
            <Magnetic actionArea="global">
              <div className="w-24 cursor-pointer">
                <Image alt="logo" src={Logo} />
              </div>
            </Magnetic>
            <h1
              className={cn(
                FONT_BIRCOLAGE_GROTESQUE.className,
                'text-center text-4xl font-bold text-accent-300'
              )}
            >
              Forms <br />{' '}
              <span>
                Not<span className="text-accent-10">a</span>mess
              </span>
            </h1>
            <p>
              <TextEffect
                as="span"
                preset="blur"
                delay={0.6}
                className={cn('text-neutral-500')}
              >
                Effortlessly Create, Customize
              </TextEffect>{' '}
              <br />
              <TextEffect
                as="span"
                preset="blur"
                delay={1}
                className={cn('text-neutral-500')}
              >
                and Share Legal Documents.
              </TextEffect>
            </p>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <Button
              variant={'shadow_accent'}
              className={cn(
                FONT_JETBRAINS_MONO.className,
                'font-bold uppercase pb-0'
              )}
            >
              coming soon
            </Button>
          </div>
        </BlurInSection>
      </section>
    </main>
  );
}
