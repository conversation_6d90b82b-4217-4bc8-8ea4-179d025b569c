'use client';

import { CalendarEvent } from '@/lib/types/calendar-types';
import { format } from 'date-fns';

// Google Calendar API scopes
const SCOPES = [
  'https://www.googleapis.com/auth/calendar',
  'https://www.googleapis.com/auth/calendar.events',
];

// Google Calendar API client ID
const CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '';

// Google Calendar API client secret
const CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || '';

// Google Calendar API redirect URI
const REDIRECT_URI = typeof window !== 'undefined' 
  ? `${window.location.origin}/api/auth/google-calendar/callback` 
  : '';

// Google Calendar API endpoints
const GOOGLE_AUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth';
const GOOGLE_TOKEN_URL = 'https://oauth2.googleapis.com/token';
const GOOGLE_CALENDAR_API = 'https://www.googleapis.com/calendar/v3';

// Interface for Google Calendar event
interface GoogleCalendarEvent {
  id?: string;
  summary: string;
  description?: string;
  location?: string;
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  attendees?: {
    email: string;
    displayName?: string;
    responseStatus?: 'needsAction' | 'declined' | 'tentative' | 'accepted';
  }[];
  conferenceData?: {
    createRequest?: {
      requestId: string;
      conferenceSolutionKey: {
        type: string;
      };
    };
  };
  reminders?: {
    useDefault: boolean;
    overrides?: {
      method: 'email' | 'popup';
      minutes: number;
    }[];
  };
}

// Interface for Google Calendar integration
export interface GoogleCalendarIntegration {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: Date;
  calendarId: string;
  syncEnabled: boolean;
}

/**
 * Generate Google Calendar authorization URL
 */
export function getGoogleCalendarAuthUrl(state: string): string {
  const params = new URLSearchParams({
    client_id: CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    response_type: 'code',
    scope: SCOPES.join(' '),
    access_type: 'offline',
    prompt: 'consent',
    state,
  });

  return `${GOOGLE_AUTH_URL}?${params.toString()}`;
}

/**
 * Exchange authorization code for access token
 */
export async function exchangeCodeForTokens(code: string): Promise<{
  access_token: string;
  refresh_token: string;
  expires_in: number;
}> {
  const params = new URLSearchParams({
    client_id: CLIENT_ID,
    client_secret: CLIENT_SECRET,
    code,
    grant_type: 'authorization_code',
    redirect_uri: REDIRECT_URI,
  });

  const response = await fetch(GOOGLE_TOKEN_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params.toString(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to exchange code for tokens: ${error.error_description}`);
  }

  return response.json();
}

/**
 * Refresh access token
 */
export async function refreshAccessToken(refreshToken: string): Promise<{
  access_token: string;
  expires_in: number;
}> {
  const params = new URLSearchParams({
    client_id: CLIENT_ID,
    client_secret: CLIENT_SECRET,
    refresh_token: refreshToken,
    grant_type: 'refresh_token',
  });

  const response = await fetch(GOOGLE_TOKEN_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params.toString(),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to refresh access token: ${error.error_description}`);
  }

  return response.json();
}

/**
 * Get user's calendars
 */
export async function getUserCalendars(accessToken: string): Promise<{
  id: string;
  summary: string;
  description?: string;
  primary: boolean;
}[]> {
  const response = await fetch(`${GOOGLE_CALENDAR_API}/users/me/calendarList`, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to get user calendars: ${error.error?.message || 'Unknown error'}`);
  }

  const data = await response.json();
  return data.items;
}

/**
 * Convert CalendarEvent to GoogleCalendarEvent
 */
function calendarEventToGoogleEvent(event: CalendarEvent, timeZone: string = 'UTC'): GoogleCalendarEvent {
  const googleEvent: GoogleCalendarEvent = {
    summary: event.title,
    description: event.description,
    location: event.location,
    start: {
      dateTime: new Date(event.start).toISOString(),
      timeZone,
    },
    end: {
      dateTime: new Date(event.end).toISOString(),
      timeZone,
    },
    reminders: {
      useDefault: false,
      overrides: [
        { method: 'email', minutes: 24 * 60 }, // 1 day before
        { method: 'popup', minutes: 30 }, // 30 minutes before
      ],
    },
  };

  // Add attendees if client or lawyer information is available
  const attendees = [];
  
  if (event.client?.email) {
    attendees.push({
      email: event.client.email,
      displayName: event.client.full_name,
    });
  }
  
  if (event.lawyer?.email) {
    attendees.push({
      email: event.lawyer.email,
      displayName: event.lawyer.full_name,
    });
  }
  
  if (attendees.length > 0) {
    googleEvent.attendees = attendees;
  }

  // Add video conferencing for video consultations
  if (event.consultationType === 'video') {
    googleEvent.conferenceData = {
      createRequest: {
        requestId: `consultation-${event.id}`,
        conferenceSolutionKey: {
          type: 'hangoutsMeet',
        },
      },
    };
  }

  return googleEvent;
}

/**
 * Create a Google Calendar event
 */
export async function createGoogleCalendarEvent(
  accessToken: string,
  calendarId: string,
  event: CalendarEvent
): Promise<string> {
  const googleEvent = calendarEventToGoogleEvent(event);
  
  const response = await fetch(`${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(googleEvent),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to create Google Calendar event: ${error.error?.message || 'Unknown error'}`);
  }

  const data = await response.json();
  return data.id;
}

/**
 * Update a Google Calendar event
 */
export async function updateGoogleCalendarEvent(
  accessToken: string,
  calendarId: string,
  eventId: string,
  event: CalendarEvent
): Promise<void> {
  const googleEvent = calendarEventToGoogleEvent(event);
  
  const response = await fetch(`${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events/${eventId}`, {
    method: 'PUT',
    headers: {
      Authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(googleEvent),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to update Google Calendar event: ${error.error?.message || 'Unknown error'}`);
  }
}

/**
 * Delete a Google Calendar event
 */
export async function deleteGoogleCalendarEvent(
  accessToken: string,
  calendarId: string,
  eventId: string
): Promise<void> {
  const response = await fetch(`${GOOGLE_CALENDAR_API}/calendars/${calendarId}/events/${eventId}`, {
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  if (!response.ok && response.status !== 410) { // 410 Gone means the event was already deleted
    const error = await response.json();
    throw new Error(`Failed to delete Google Calendar event: ${error.error?.message || 'Unknown error'}`);
  }
}

/**
 * Send calendar invitation
 */
export async function sendCalendarInvitation(
  accessToken: string,
  calendarId: string,
  eventId: string,
  recipient: string
): Promise<void> {
  // Google Calendar automatically sends invitations when attendees are added
  // This function is a placeholder for any additional logic needed
  console.log(`Invitation sent to ${recipient} for event ${eventId}`);
}

/**
 * Format consultation details for Google Calendar
 */
export function formatConsultationDetails(event: CalendarEvent): string {
  const details = [];
  
  details.push(`Consultation Type: ${event.consultationType === 'video' ? 'Video Consultation' : 'Document Review'}`);
  
  if (event.client) {
    details.push(`Client: ${event.client.full_name}`);
  }
  
  if (event.lawyer) {
    details.push(`Lawyer: ${event.lawyer.full_name}`);
  }
  
  if (event.documentId) {
    details.push(`Document ID: ${event.documentId}`);
  }
  
  if (event.description) {
    details.push(`\nNotes:\n${event.description}`);
  }
  
  return details.join('\n');
}
