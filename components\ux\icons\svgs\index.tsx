import { cn } from 'lib/utils';

type svgProps = React.SVGProps<SVGSVGElement>;

const LogoRed: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="552"
      height="552"
      viewBox="0 0 552 552"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="552" height="552" rx="276" fill="#F65428" />
      <path
        d="M176.4 372V244.8V162H223.6L223.2 224.8H231.2C234.667 209.6 239.6 196.933 246 186.8C252.4 176.667 260.4 169.067 270 164C279.867 158.933 291.467 156.4 304.8 156.4C329.067 156.4 347.467 164.933 360 182C372.8 199.067 379.2 225.867 379.2 262.4V372H321.2V268.8C321.2 246.4 317.867 230.133 311.2 220C304.8 209.6 295.333 204.4 282.8 204.4C272.133 204.4 263.2 207.733 256 214.4C248.8 221.067 243.333 229.867 239.6 240.8C236.133 251.467 234.267 263.467 234 276.8V372H176.4Z"
        fill="white"
      />
    </svg>
  );
};
const TwitterSvg: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      data-testid="geist-icon"
      height="16"
      strokeLinecap="round"
      strokeLinejoin="round"
      viewBox="0 0 16 16"
      width="16"
      aria-label=""
      className={cn(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.5 0.5H5.75L9.48421 5.71053L14 0.5H16L10.3895 6.97368L16.5 15.5H11.25L7.51579 10.2895L3 15.5H1L6.61053 9.02632L0.5 0.5ZM12.0204 14L3.42043 2H4.97957L13.5796 14H12.0204Z"
        fill="currentColor"
      ></path>
    </svg>
  );
};

const Underline08: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="517"
      height="62"
      viewBox="0 0 517 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M238.104 8.99289C236.235 9.09919 234.367 9.21379 232.499 9.32009C207.738 10.3095 182.971 11.4951 158.179 12.9588C142.607 13.8746 131.764 14.5696 122.844 15.2074C82.5104 17.3579 42.2745 20.4651 2.31746 24.8479C1.58318 24.9297 1.21111 25.0767 1.1645 25.1013C0.108048 25.6001 0.0172869 26.4588 0.00175088 26.8758C-0.0186913 27.4645 0.127681 28.2412 1.09173 28.7563C1.20703 28.8135 1.4638 28.9526 1.90453 28.9853C2.08524 29.0016 4.27987 28.8464 8.43045 28.5275C8.20967 28.9854 8.19987 29.4269 8.28327 29.8275C8.36668 30.22 8.73221 31.3648 10.2711 31.4875C11.5172 31.5856 20.1504 30.3427 23.5029 30.0156C35.141 28.8872 46.7636 27.5871 58.4099 26.5404C76.6304 24.8969 94.864 23.4088 113.1 21.9615C125.532 20.9639 137.965 19.9908 150.404 19.0423C166.025 18.0611 181.652 17.1534 197.285 16.3357C242.2 15.1173 287.118 14.8393 331.78 15.0601C385.003 15.3299 438.504 14.8965 491.384 21.1845L488.751 21.2827L487.811 21.3155C464.801 19.2059 437.122 20.8412 417.629 20.6449C333.693 19.8109 251.091 22.5338 167.477 29.2224C134.659 31.8554 101.859 35.5269 69.2165 39.738C57.9267 41.2016 28.611 44.644 24.6198 45.9277C23.2567 46.3611 22.8896 47.3015 22.8103 47.6286C22.6909 48.1192 22.7424 48.5934 22.9755 49.0595C23.1267 49.3621 23.3835 49.7138 23.8855 49.9836C24.0769 50.0899 24.4236 50.2288 24.9657 50.3024C25.4613 50.3678 26.3427 50.4088 27.6657 50.4088C134.31 49.9755 240.549 37.3912 347.013 31.9945C386.303 29.9994 425.593 28.1105 464.891 26.2707C472.585 25.9109 480.288 25.6737 487.99 25.3957C489.609 25.5511 491.196 25.7227 492.758 25.919L475.063 26.8512C439.796 28.7074 404.627 31.3404 369.426 34.235C287.903 40.9318 206.752 52.0768 125.159 57.6616C124.034 57.7434 123.183 58.7164 123.26 59.8448C123.337 60.9732 124.314 61.8236 125.439 61.7418C207.05 56.157 288.222 45.0039 369.761 38.307C404.922 35.4206 440.05 32.7876 475.276 30.9315C487.802 30.2773 505.342 29.3532 508.686 29.1652C509.087 29.1406 509.316 29.1244 509.357 29.1162C510.321 29.0099 510.665 28.4374 510.714 28.372C511.213 27.7588 511.254 27.1292 511.098 26.5486C511 26.1888 510.747 25.4365 509.708 25.1258C509.43 25.0441 509.152 24.9623 508.866 24.8887C511.941 24.8396 514.508 24.7743 515.219 24.6108C516.724 24.2592 516.961 23.098 516.994 22.6728C517.026 22.223 516.937 21.7406 516.577 21.2664C516.43 21.0701 516.184 20.8084 515.743 20.5958C515.522 20.4977 515.023 20.3342 514.148 20.1952C486.003 15.7551 457.646 13.523 429.207 12.3619L446.845 12.5008C447.09 12.5008 447.204 12.4926 447.229 12.4926C448.267 12.4027 448.627 11.7648 448.709 11.6504C449.101 11.1025 449.175 10.5383 449.036 9.98226C449.028 9.94138 448.799 8.85381 447.703 8.51039L459.142 8.36326C461.465 8.33873 462.618 8.30615 462.822 8.28979C463.828 8.19167 464.188 7.60277 464.261 7.50465C464.744 6.89956 464.793 6.26993 464.613 5.65667C464.539 5.40319 464.286 4.48739 463.051 4.24209C462.986 4.22573 462.667 4.18499 462.111 4.17681C457.033 4.08686 420.384 4.16032 406.287 4.24209C383.646 4.37292 360.996 4.51204 338.354 4.96994C321.894 5.29701 305.45 5.81204 289.006 6.48254C242.729 5.37049 196.498 3.48169 150.818 0.00652763C149.693 -0.0834179 148.71 0.767016 148.624 1.88725C148.539 3.01565 149.383 3.99683 150.508 4.08677C179.529 6.28635 208.772 7.86449 238.104 8.99289ZM412.101 24.6842C330.177 24.0219 249.475 26.7612 167.803 33.3027C135.051 35.9193 102.317 39.5908 69.7398 43.7937C65.5434 44.3415 58.8139 45.151 51.8562 46.0178C150.306 44.0226 248.46 32.894 346.809 27.9061C368.567 26.8022 390.334 25.7309 412.101 24.6842Z"
        fill="currentColor"
      />
    </svg>
  );
};
const Lamp: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        d="M281.039 150.734C249.146 101.275 171.209 157.252 204.36 201.071"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M275.747 158.814C256.796 168.357 238.638 178.946 220.448 190.082C215.821 192.915 204.432 204.706 207.39 199.957C208.699 197.852 210.46 196.117 211.998 194.197"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M229.147 123.83C222.686 120.121 223.999 100.597 218.866 96.4894C215.126 93.4965 207.133 105.114 203.848 107.453C199.237 110.732 183.155 114.994 180.924 120.949C177.222 130.815 195.403 136.382 199.105 146.251"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M183.603 138.258C175.707 143.796 110.968 175.223 111.59 179.253C112.645 186.092 165.395 254.473 169.665 282.159"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M133.824 302.982C140.672 301.517 232.395 306.709 198.252 304.4"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M127.295 168.55C109.514 139.277 73.2003 176.133 99.88 196.481C118.607 210.767 140.488 188.891 128.081 169.371"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M213.893 246.753C213.224 244.626 214.585 242.186 214.931 239.9"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M256.679 234.19C253.824 231.901 252.911 229.13 251.383 226.195"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M285.277 209.064C288.455 211.92 289.842 213.565 291.632 214.775"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M295.867 177.088C300.666 177.532 306.709 179.551 309.635 180.514L295.867 177.088Z"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="16"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const CampHouse: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="200"
      height="200"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        d="M36.2984 191.443C34.753 219.45 35.5365 242.287 34.1367 270.28"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M28.8789 205.35C34.3474 210.782 36.1422 208.068 41.24 203.65"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M27.3359 214.467C33.4987 221.349 36.4249 217.838 42.9421 214.004"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M24.7031 226.673C31.8719 229.618 37.2013 228.132 44.0171 225.746"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.1562 241.044C31.2578 243.108 38.8197 241.36 47.7329 236.408"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 250.159C31.5931 259.457 37.3143 252.697 48.3491 249.387"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M342.191 108.07C343.047 157.419 344.353 206.794 344.353 256.249"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M312.73 231.085C350.122 207.643 337.314 204.111 373.535 234.464"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M318.383 206.989C346.485 185.718 339.015 180.935 365.616 204.21"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M327.531 174.058C342.048 161.261 344.259 163.312 357.475 176.528"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M330.77 149.329C341.177 139.652 344.238 142.878 354.231 150.872"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M328.922 131.877C337.785 122.674 343.486 121.981 351.458 130.951"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M336.48 115.639C341.867 109.673 343.436 108.342 348.52 115.331"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M235.441 111.607C208.791 161.703 178.363 209.957 149.301 258.394"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M128.636 129.318C102.051 169.711 79.1344 212.427 52.4258 252.49"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M132.125 124.219C160.571 119.39 192.919 115.032 231.951 111.07"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M238.66 110.803C264.434 160.458 296.794 206.872 319.969 257.858"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M270.864 206.335C242.326 206.342 213.483 203.92 184.992 203.92"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M228.195 127.979C249.11 171.355 282.512 210.423 302.796 254.37"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M198.141 180.842C218.453 181.701 238.297 186.307 258.788 184.599"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M211.559 161.521C222.724 161.961 233.339 164.205 244.565 164.205"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M186.332 218.145C213.981 220.108 241.644 220.56 269.52 220.56"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M183.382 219.752C182.97 231.94 182.447 244.147 180.43 256.247"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M268.984 222.168C269.291 232.916 268.984 243.601 268.984 254.37"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M225.244 222.168C225.073 234.289 224.761 246.38 223.902 258.395"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M226.048 130.393C226.199 153.901 226.707 177.537 225.242 200.968"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M203.776 169.57C204.074 177.841 202.703 186.063 202.703 194.258"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M247.783 171.986C247.72 181.046 247.005 190.065 246.441 199.089"
        stroke="currentColor"
        strokeOpacity="0.5"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M139.195 260.586C108.741 260.886 77.9932 261.676 47.6445 260.426"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M378.998 260.004C312.818 256.021 214.037 266.253 148.074 260.873"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M167.012 271.335C161.721 270.8 152.493 271.072 149.301 271.604"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M173.814 281.192C169.733 281.485 165.771 280.924 161.738 280.924"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M192.155 291.17C185.874 291.179 179.597 291.189 173.355 291.226"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M165.302 131.611C152.005 153.056 140.135 175.44 126.719 196.905"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M185.174 130.838C172.654 153.855 159.189 176.395 144.656 198.196"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M166.465 130.449C171.519 129.94 176.568 129.136 181.692 129.546"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M127.234 196.389C132.564 196.262 137.823 197.044 143.105 197.421"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M209.762 251.65C206.925 251.846 204.06 251.631 201.23 251.553"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M200.36 252.135C196.966 253.655 193.66 255.383 190.375 257.079"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M191.344 239.047C193.456 243.5 195.143 248.123 196.774 252.717"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M200.844 240.209C201.069 243.805 201.862 247.354 202.395 250.874"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M191.926 237.881C195.218 236.24 197.844 237.443 200.846 239.045"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M210.637 251.164C210.451 253.425 210.379 255.688 210.25 257.95"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M201.523 250.973C201.75 253.224 201.586 255.512 201.523 257.759"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M191.925 241.18C192.164 245.756 191.83 250.316 191.441 254.85"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const PalmTrees: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        d="M182.009 198.597C166.556 195.586 155.432 204.101 146.352 216.478"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M158.242 182.141C170.084 183.468 179.125 188.303 185.976 197.989"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M185.973 200.629C197.045 203.314 204.729 210.328 209.744 219.118"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M185.973 196.667C201.217 188.301 218.478 191.319 234.836 195.821"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M155.188 212.516C153.423 214.314 156.133 216.92 156.603 219.119"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M168.999 203.271C168.131 204.937 168.999 209.833 168.999 209.874"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M180.862 205.109C180.92 206.572 180.619 208.009 180.508 209.356"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M170.123 186.102C167.915 187.61 165.422 189.687 163.52 191.385"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M156.837 183.803C156.111 185.546 155.176 186.888 154.359 188.404"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M217.668 194.025C220.341 195.877 222.18 198.382 224.271 200.628"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M203.145 194.025C206.116 194.926 207.856 197.28 209.748 199.308"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M201.383 216.383C201.114 217.529 200.2 218.269 199.613 219.214"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M195.745 205.109C195.587 206.572 195.038 207.94 194.684 209.356"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M250.684 146.484C260.175 140.631 273.286 139.582 283.7 143.636"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M282.379 145.179C295.653 137.509 309.489 138.353 321.998 146.482"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M285.02 145.162C300.95 150.777 307.136 166.682 310.112 182.14"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M254.645 128.079C270.421 119.851 281.259 131.472 285.019 146.482"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M303.213 164.973C304.423 171.165 301.759 173.202 298.227 176.859"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M295.597 154.406C296.395 157.859 294.171 160.596 292.766 163.65"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M308.168 141.199C310.656 142.664 310.931 144.68 312.061 146.482"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M271.818 127.994C268.641 129.088 268.191 132.104 266.535 134.597"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M275.433 142.521C273.151 144.393 272.184 146.879 270.832 149.124"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M255.969 146.564C255.969 147.388 255.969 148.212 255.969 149.041"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M279.734 83.653C293.978 82.3155 312.6 82.1488 316.712 97.6188"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M278.419 82.3419C266.015 76.2878 251.124 79.0689 242.762 88.3753"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M234.836 58.0091C252.244 57.6545 269.696 67.6986 279.738 81.7715"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M282.379 79.1306C291.938 67.0006 305.296 59.6989 319.357 68.4479"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M311.161 91.0156C312.561 93.7702 309.693 95.4975 308.684 97.6188"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M296.754 84.4121C297.343 86.5266 296.878 88.986 296.931 91.0153"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M266.533 79.1309C263.147 81.4385 262.608 84.6928 261.25 88.3754"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M252.356 85.7344C251.881 87.4736 251.881 89.2731 251.648 91.017"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M255.966 64.6035C252.994 65.7695 251.381 67.9992 249.363 69.8861"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M242.418 61.5432C239.924 61.0255 239.285 63.952 237.816 65.0825"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M313.284 68.0352C312.931 68.3891 312.577 68.7431 312.223 69.097"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M121.258 116.108C137.478 103.314 162.396 97.5858 176.725 115.83"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M115.977 118.75C105.195 123.592 92.3122 139.807 99.6381 151.766"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M102.773 130.635C103.015 136.258 105.457 138.911 109.377 142.521"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M115.355 121.391C115.929 123.553 116.193 125.817 116.594 127.994"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M137.109 106.863C143.713 107.891 144.299 111.397 146.354 116.107"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M154.281 105.543C161.964 107.63 162.727 111.921 163.526 114.788"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M148.992 83.3672C130.242 80.5875 116.357 99.3721 118.923 116.107"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M130.504 88.375C134.433 88.5557 136.915 90.9885 139.748 93.6575"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M123.902 99.6481C125.495 98.2694 127.462 98.692 129.185 98.2324"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M115.978 109.506C109.288 92.5747 89.7659 91.9944 79 99.8548"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M97.4906 97.6191C93.1529 99.2558 90.8935 103.314 88.2461 106.864"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M108.056 100.262C104.935 100.546 104.35 104.505 102.773 106.865"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M121.258 116.107C200.383 144.023 243.946 255.147 239.851 336.654"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M279.734 91.0156C237.564 160.759 218.984 255.055 218.984 336.655"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M182.008 205.912C198.306 248.262 193.104 294.667 193.104 339.297"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M285.021 149.123C256.29 207.557 263.444 273.6 268.808 336.654"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M315.395 340.617C252.2 341.672 187.587 341.5 123.902 341.938"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M299.805 67.9395C301.437 68.6316 301.257 70.5966 301.928 71.8327"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const SummerHut: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        d="M112.988 157.966C141.017 101.338 226.552 102.735 263.252 150.835C264.409 152.357 266.953 155.892 265.564 158.688C263.425 163.006 148.286 163.395 110.477 163.395"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M190.574 119.809C194.762 173.486 189.282 234.1 194.108 284.724"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M151.703 217.1C178.463 218.564 205.169 215.471 231.805 214.143"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M185.865 117.453C168.416 122.297 155.849 150.761 155.238 152.792"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M195.289 123.344C210.739 127.158 229.67 143.539 230.628 159.861"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M65.7109 243.667C67.1185 168.886 109.296 210.568 109.296 250.564"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M61 284.724C69.7257 232.972 55.7538 251.393 91.4203 251.393C127.359 251.393 120.454 250.635 124.61 284.377"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M101.441 258.809C101.771 265.496 102.176 272.183 103.019 278.835"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M78.5524 251.74C76.9456 259.456 79.3015 282.924 79.344 283.545"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M294.238 227.706C311.526 170.669 349.137 193.208 334.52 242.318"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M337.914 280.011C337.538 272.288 341.844 252.099 335.53 246.222C328.725 239.895 274.479 241.818 266.622 247.964C258.765 254.11 264.372 270.298 264.372 280.011"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M280.098 252.92C280.812 261.126 281.78 269.46 280.985 277.658"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M319.962 249.385C318.27 259.632 318.237 279.491 317.988 285.902"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M168.195 285.902C209.607 256.556 180.716 266.209 215.314 282.964"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const SleepUnderTree: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        d="M138.051 150.752C169.028 162.01 146.05 212.823 114.831 200.196C86.9185 188.907 86.0826 121.558 150.948 144.407"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M194.279 254C162.948 250 156.403 206.848 158.3 207C189.781 217.222 220.665 213.416 223.062 213C227.126 212.295 305.302 194.893 331 171"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M303 217C302.097 215.22 300.732 213.987 298.881 212.555C298.175 212.007 297.39 211.489 296.634 210.974C294.648 209.625 259.825 188.014 255.71 185.791C253.724 184.707 251.692 184 249 184C248.963 184.027 248.925 184.055 248.888 184.078C248.908 184.055 248.925 184.027 248.945 184C248.914 184.039 221.549 208.314 221 210"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        opacity="0.503384"
        d="M26 152.9C26.3155 153.104 26.6261 153.317 26.9317 153.541C28.5995 157.446 120.091 218.784 206.978 216.96C291.383 215.188 364.921 156.553 374 147"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const SadFace: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="67"
      height="37"
      viewBox="0 0 67 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <g clipPath="url(#clip0_102_6)">
        <mask
          id="mask0_102_6"
          // style='mask-type:luminance'
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="67"
          height="37"
        >
          <path d="M66.7 0H0V36.92H66.7V0Z" fill="white" />
        </mask>
        <g mask="url(#mask0_102_6)">
          <path
            d="M18.3401 1.38002L18.1101 8.63002C18.1001 8.98002 18.1901 9.18002 18.5201 9.35002C18.8901 9.54002 19.4501 9.55002 19.8401 9.49002C20.3601 9.41002 20.9101 9.27001 21.3601 8.98001C21.6301 8.80001 22.1101 8.49002 22.1201 8.14002L22.3301 0.890017C22.3301 0.540017 22.2501 0.350017 21.9201 0.180017C21.5501 -0.00998299 21.0001 -0.019983 20.6101 0.040017C20.0901 0.120017 19.5501 0.260017 19.1001 0.550017C18.8301 0.720017 18.3601 1.03002 18.3501 1.39002L18.3401 1.38002Z"
            fill="currentColor"
          />
          <path
            d="M29.18 1.81001C29.04 4.20002 29.05 6.60001 29.21 8.98001C29.23 9.29001 29.74 9.46002 29.96 9.51002C30.41 9.61002 30.97 9.55002 31.4 9.43002C32.08 9.23002 33.13 8.80002 33.07 7.95002C32.98 6.69002 32.94 5.43001 32.94 4.16001V4.44002C32.94 3.40002 32.98 2.37002 33.04 1.33002C33.09 0.490017 32 0.410017 31.37 0.500017C30.87 0.570017 30.34 0.710017 29.91 1.00002C29.65 1.17002 29.2 1.46002 29.18 1.82002V1.81001Z"
            fill="currentColor"
          />
          <path
            d="M2.71024 34.22C17.8402 26.87 35.5902 22.45 51.7202 29.39C55.9002 31.19 59.4502 33.7 62.8802 36.65C63.8302 37.47 67.5802 36.27 66.5102 35.12C60.8202 29.01 52.1002 25.61 44.0102 24.3C34.7902 22.81 25.3302 23.93 16.4302 26.59C11.1802 28.16 6.10024 30.23 1.18024 32.62C0.660241 32.87 -0.38976 33.47 0.15024 34.17C0.64024 34.81 2.15024 34.48 2.71024 34.21V34.22Z"
            fill="currentColor"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_102_6">
          <rect width="67" height="37" fill="currentColor" />
        </clipPath>
      </defs>
    </svg>
  );
};
const Smile: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="100"
      height="100"
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M94.6785 31.4698C75.9099 66.3698 38.4175 80.5585 3.59528 60.9107C2.43603 60.2565 0.965988 60.6655 0.311743 61.8104C-0.342503 62.9758 0.0664445 64.4478 1.22569 65.1021C38.5504 86.181 78.8049 71.1539 98.9229 33.7597C99.5526 32.5943 99.1131 31.1221 97.9416 30.4883C96.7701 29.8749 95.3082 30.3045 94.6785 31.4698Z"
        fill="currentColor"
      />
      <path
        d="M32.8988 45.9428C36.7758 45.9428 39.9187 42.7999 39.9187 38.9229C39.9187 35.0459 36.7758 31.9031 32.8988 31.9031C29.0218 31.9031 25.8789 35.0459 25.8789 38.9229C25.8789 42.7999 29.0218 45.9428 32.8988 45.9428Z"
        fill="currentColor"
      />
      <path
        d="M61.9608 38.4754C65.762 38.4754 68.8436 35.394 68.8436 31.5927C68.8436 27.7915 65.762 24.7101 61.9608 24.7101C58.1596 24.7101 55.0781 27.7915 55.0781 31.5927C55.0781 35.394 58.1596 38.4754 61.9608 38.4754Z"
        fill="currentColor"
      />
    </svg>
  );
};
const Highlight05: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="68"
      height="74"
      viewBox="0 0 68 74"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.4259 68.5278C16.0259 66.7318 9.32534 65.8258 2.82534 64.9958C1.42534 64.8218 0.125535 65.7928 0.0255346 67.1608C-0.174465 68.5298 0.826121 69.7818 2.12612 69.9557C8.42612 70.7548 14.9255 71.6097 21.0255 73.3387C22.3255 73.7137 23.7261 72.9418 24.1261 71.6138C24.5261 70.2868 23.7259 68.9038 22.4259 68.5278Z"
        fill="black"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M41.8251 43.0648C31.5251 32.5538 19.9251 23.3958 9.8251 12.6028C8.9251 11.5948 7.3251 11.5408 6.3251 12.4818C5.3251 13.4238 5.22549 15.0078 6.22549 16.0158C16.3255 26.8398 27.9255 36.0278 38.2255 46.5698C39.2255 47.5538 40.8251 47.5678 41.8251 46.5998C42.7251 45.6328 42.8251 44.0488 41.8251 43.0648Z"
        fill="black"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M61.1264 2.63576C61.4264 8.65176 61.7259 14.6678 62.0259 20.6848C62.0259 22.0628 63.2264 23.1268 64.6264 23.0598C66.0264 22.9918 67.0259 21.8188 67.0259 20.4398C66.7259 14.4138 66.4264 8.38876 66.1264 2.36376C66.0264 0.985757 64.8262 -0.0712432 63.4262 0.00375683C62.1262 0.0787568 61.0264 1.25876 61.1264 2.63576Z"
        fill="black"
      />
    </svg>
  );
};
const Arrow09: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="130"
      height="130"
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <g clip-path="url(#clip0_304_197)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.8316 117.29C14.2448 102.664 19.1745 96.2087 25.1475 94.0207C31.1206 91.828 37.8521 93.9046 44.5836 95.986C49.2292 97.4502 53.9697 98.944 58.331 99.213C62.8819 99.4961 67.1485 98.4536 70.8461 94.7824C71.6994 93.9011 72.458 92.4712 72.9321 90.5652C73.6905 87.9791 74.1646 84.4596 74.3542 80.33C75.3023 64.6645 73.4065 40.0076 75.6819 24.9913C76.156 21.5393 76.9139 18.612 77.9568 16.4584C78.9049 14.5843 80.0424 13.3179 81.5594 12.9957C82.7919 12.7327 84.2143 13.1011 86.0156 14.1009C89.0496 15.808 92.6521 19.2896 97.203 24.9367C101.185 29.8802 104.504 35.4491 108.391 40.5063C114.459 48.4232 120.811 56.4467 127.922 62.7986C128.396 63.2002 129.059 63.16 129.438 62.7098C129.912 62.2585 129.818 61.5666 129.344 61.1651C122.328 54.9031 116.071 46.9839 110.098 39.1784C106.305 34.1081 102.892 28.525 98.9103 23.5685C94.1698 17.6252 90.2826 13.9967 87.0591 12.1996C84.7837 10.8646 82.7919 10.5021 81.0853 10.8539C79.3788 11.2164 77.8622 12.3406 76.7245 14.1436C75.2075 16.5294 74.1642 20.1828 73.5006 24.6678C71.2251 39.7375 73.1219 64.4832 72.1738 80.2044C71.9842 84.1445 71.5097 87.5053 70.8461 89.9729C70.4668 91.4359 69.9926 92.5636 69.2341 93.2401C66.1054 96.4125 62.4084 97.2725 58.4264 97.0285C54.2547 96.7691 49.7038 95.306 45.153 93.8963C38.0422 91.6657 30.7415 89.6175 24.3893 91.9666C18.037 94.3192 12.3484 101.07 8.74563 116.795C8.55601 117.384 8.93506 117.973 9.50392 118.109C10.0728 118.245 10.7368 117.879 10.8316 117.29Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.68015 116.438C7.58534 116.285 7.39535 115.87 7.30054 115.526C6.73168 113.856 6.16338 111.264 5.97376 110.503C3.88794 103.776 2.18137 96.2451 2.18137 89.039C2.18137 88.4348 1.70676 87.9456 1.1379 87.9468C0.474234 87.948 0 88.4384 0 89.0425C0 96.4571 1.80139 104.207 3.79239 111.13C4.07682 111.901 4.64568 114.532 5.21454 116.229C5.40416 116.839 5.59434 117.343 5.78395 117.661C5.97357 117.921 6.16282 118.1 6.35244 118.213C6.73168 118.504 7.3011 118.646 7.86996 118.627C8.53362 118.606 9.29192 118.387 10.0504 118.015C11.6622 117.288 13.5582 115.957 15.0751 114.905C15.9284 114.301 16.6871 113.786 17.1611 113.628C21.238 112.277 25.6936 113.717 29.1068 118.394C29.486 118.88 30.1503 118.985 30.6243 118.627C31.0984 118.27 31.193 117.584 30.9086 117.097C26.7369 111.488 21.4274 109.928 16.4973 111.55C15.2648 111.941 12.6104 114.1 10.2402 115.479C9.48172 115.873 8.8175 116.198 8.24864 116.357C8.05902 116.403 7.96421 116.435 7.77459 116.439C7.77459 116.442 7.68015 116.445 7.68015 116.438Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_304_197">
          <rect width="130" height="130" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

const Arrow11: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="130"
      height="130"
      viewBox="0 0 130 130"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <g clip-path="url(#clip0_910_13)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M60.3434 54.1264C60.4519 54.6896 60.6689 55.2516 60.8859 55.8126C61.8625 58.8411 62.9478 62.373 64.9009 64.9804C67.5051 68.3995 70.4345 69.7211 73.3642 69.6918C77.4875 69.6473 81.7189 66.6058 84.323 62.4381C86.9272 58.3083 88.0127 53.1369 86.3851 48.9713C85.3 46.1164 82.9129 43.6804 78.7896 42.3523C74.1238 40.8202 69.675 42.0734 65.7687 44.7742C64.2496 45.7876 62.9478 47.0073 61.6457 48.3506C61.3201 46.583 60.9942 44.7981 60.6687 42.9882C59.8006 37.0626 59.5834 30.8343 59.9089 24.861C60.2344 16.9595 61.8627 9.40082 64.9009 2.09829C65.1179 1.42337 64.7922 0.650805 64.1411 0.373026C63.4901 0.0963326 62.731 0.417541 62.4054 1.09246C59.2587 8.68253 57.5218 16.5384 57.1962 24.7503C56.9792 30.8972 57.0886 37.3056 58.0651 43.4037C58.4992 45.9298 58.933 48.4092 59.4755 50.8604C56.9799 54.0765 55.0265 57.6496 53.8329 60.6878C46.2374 81.0123 56.1112 108.381 65.8768 126.798C66.2023 127.443 66.9625 127.688 67.6136 127.347C68.2646 127.005 68.4816 126.206 68.1561 125.561C58.716 107.735 48.9499 81.2923 56.3284 61.621C57.1964 59.3076 58.6073 56.6525 60.3434 54.1264ZM62.4054 51.4962C62.6224 52.6572 62.9473 53.8107 63.3814 54.9608C64.3579 57.7365 65.2258 60.9982 66.9619 63.3864C68.915 65.9678 71.086 67.0713 73.2561 67.0496C76.7284 67.0138 79.9832 64.3988 82.0448 61.0275C84.2149 57.6193 85.3002 53.3723 83.8896 49.9337C83.0216 47.7202 81.1765 45.894 78.0298 44.8643C74.1236 43.6023 70.435 44.7221 67.2883 46.9476C65.4436 48.1856 63.816 49.7634 62.4054 51.4962Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M67.1772 126.999C66.6346 126.616 66.0921 126.043 65.5496 125.469C64.4645 124.366 63.4877 123.196 62.6196 122.58C57.4113 118.784 51.1181 114.297 44.9332 112.307C44.2821 112.083 43.5226 112.464 43.3056 113.158C43.0886 113.852 43.4139 114.597 44.1734 114.822C50.0328 116.731 56.0013 121.077 61.1012 124.72C61.9692 125.386 63.1622 126.796 64.2473 127.927C65.2238 128.886 66.2006 129.651 67.1772 129.929C67.7197 130.124 68.6963 129.907 69.3473 128.686C70.2154 126.954 70.9755 122.145 71.0841 121.679C72.1691 116.964 73.254 112.814 77.3773 109.837C77.9198 109.408 78.0281 108.58 77.7026 107.991C77.2685 107.402 76.4003 107.272 75.8577 107.701C71.1919 111.095 69.673 115.743 68.4795 121.118C68.4795 121.461 68.0454 124.335 67.3944 126.333C67.2859 126.549 67.2857 126.787 67.1772 126.999Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_910_13">
          <rect
            width="130"
            height="130"
            fill="currentColor"
            transform="translate(130 130) rotate(-180)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
const Bag: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="400"
      height="400"
      viewBox="0 0 400 400"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fill-rule="evenodd"
        clipRule="evenodd"
        d="M139.874 150.625C120.135 154.731 117.554 171.984 116.454 181.077C114.307 198.815 112.22 248.87 116.454 257.213C120.687 265.555 137.893 271.543 162.909 271.543C187.925 271.543 237.873 274.823 253.392 271.543C268.912 268.263 285.482 161.781 268.223 154.481C247.292 148.344 159.613 146.518 139.874 150.625Z"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M276.583 162.824C282.76 203.384 285.849 228.439 285.849 237.988C285.849 252.311 284.075 268.914 274.297 270.924C270.248 271.756 262.348 271.756 250.598 270.924"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M228.346 140.211C229.491 131.008 226.127 126.407 218.253 126.407C206.443 126.407 194.481 124.225 190.592 129.966C187.999 133.793 186.703 138.807 186.703 145.007"
        stroke="currentColor"
        strokeOpacity="0.9"
        strokeWidth="12"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
const Arrow001: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="78"
      height="93"
      viewBox="0 0 78 93"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M70.3849 49.2577C71.0433 48.9158 71.4391 48.1661 71.5201 47.4001C71.5701 46.9701 71.5101 46.59 71.3801 46.2C71.4872 44.9885 71.6283 43.7905 71.7698 42.5892C71.8 42.333 71.8302 42.0767 71.86 41.82C72.29 37.99 72.7201 34.1601 73.1701 30.3401C73.6101 26.6201 74.2501 22.94 74.9001 19.26C75.2701 17.18 75.62 15.09 75.94 13C76.21 11.21 76.4301 9.42004 76.6401 7.63004C76.7601 6.59004 76.8901 5.54003 77.0201 4.50003C77.0413 4.3322 77.0672 4.16118 77.0935 3.98831C77.2283 3.1003 77.3705 2.16359 76.9101 1.36002C76.4301 0.510019 75.4101 0.230029 74.5001 0.370029C74.0924 0.431792 73.6924 0.531703 73.2906 0.632064C73.0419 0.694196 72.7925 0.7565 72.5401 0.810031C72.1887 0.879151 71.8341 0.942465 71.4785 1.00594C70.5993 1.16288 69.7144 1.32084 68.86 1.57004C67.39 2.01004 65.9501 2.50004 64.5001 3.01004C64.546 2.99857 64.5886 2.9838 64.6298 2.96951C64.6604 2.9589 64.6902 2.94856 64.72 2.94004C64.0068 3.18665 63.3026 3.45993 62.5983 3.73322C62.246 3.86991 61.8935 4.00669 61.5401 4.14005C57.9601 5.50005 54.4 6.92005 50.86 8.39005C50.0301 8.73501 49.2027 9.08496 48.3753 9.43492C47.5479 9.78487 46.72 10.1351 45.8901 10.48C45.573 10.6116 45.2559 10.7477 44.9381 10.8841C44.3136 11.1521 43.6863 11.4214 43.0501 11.66L43.0473 11.6611C42.1482 12.0007 41.2491 12.3404 40.36 12.68C40.095 12.7645 39.8308 12.8507 39.5667 12.9368C38.9243 13.1463 38.2819 13.3558 37.6301 13.54C37.33 13.6251 37.0295 13.7089 36.729 13.7927C35.6177 14.1024 34.5062 14.4122 33.4201 14.79C32.3101 15.17 31.2101 15.58 30.1601 16.11C29.8905 16.2448 29.6184 16.3821 29.3463 16.5194L29.345 16.5201C29.0725 16.6576 28.8001 16.795 28.5301 16.93C27.7601 17.33 26.9701 17.8 26.3101 18.37H26.3001L26.2901 18.38C26.1301 18.47 25.98 18.57 25.84 18.7C25.38 19.12 25.2201 19.85 25.3801 20.43C25.5301 20.97 25.9201 21.38 26.4201 21.61C26.5779 21.6843 26.7528 21.7327 26.9211 21.7792L26.96 21.79C27.3757 21.9129 27.7928 22.0306 28.2097 22.1482C28.9461 22.3561 29.6822 22.5638 30.4101 22.8C31.0201 23.04 31.62 23.29 32.21 23.56C32.4099 23.65 32.6088 23.7444 32.8076 23.8388C33.2054 24.0277 33.6034 24.2167 34.0101 24.37C35.2701 24.84 36.52 25.27 37.82 25.61C38.18 25.705 38.5425 25.8 38.905 25.895C39.2675 25.99 39.63 26.085 39.99 26.18C40.0401 26.1967 40.0933 26.2103 40.1479 26.2243C40.1914 26.2354 40.2358 26.2467 40.2801 26.26C39.0901 27.11 37.9201 27.98 36.8101 28.93C34.2801 31.1 31.7901 33.33 29.4101 35.67C26.6601 38.37 23.92 41.08 21.34 43.94C21.1138 44.1906 20.8873 44.4411 20.6607 44.6917C18.2737 47.3322 15.8768 49.9836 13.6301 52.76C13.4385 52.9952 13.2461 53.2286 13.054 53.4616C12.5846 54.0311 12.1172 54.5981 11.6701 55.18C11.0001 56.07 10.3301 56.96 9.65008 57.85C9.28442 58.3282 8.9435 58.8162 8.60295 59.3038C8.45921 59.5095 8.31554 59.7152 8.1701 59.92C8.06299 60.0733 7.95543 60.2262 7.84799 60.3789C7.44388 60.9533 7.04139 61.5254 6.6701 62.11C6.50232 62.3707 6.33274 62.6287 6.16351 62.8861C5.76792 63.4879 5.37427 64.0867 5.01007 64.7101C4.48007 65.6201 3.9501 66.52 3.4201 67.42L3.22552 67.7502C2.78888 68.4906 2.35044 69.234 1.95001 70C1.70001 70.12 1.48011 70.29 1.30011 70.5201C0.840107 71.11 0.819999 71.93 1.19 72.54C1.24 72.85 1.37 73.1501 1.57 73.4001C1.65946 73.5126 1.76307 73.6151 1.87724 73.705C1.81479 74.277 1.93034 74.8778 2.03671 75.4309L2.05 75.5C2.13 75.91 2.21001 76.31 2.31001 76.71L2.3118 76.717C2.4012 77.0647 2.49058 77.4123 2.58991 77.75C2.77991 78.36 3.01991 78.86 3.58991 79.2C3.59492 79.205 3.59993 79.2075 3.60493 79.21C3.60994 79.2125 3.61495 79.215 3.61994 79.22C3.74994 79.37 3.9 79.51 4.05 79.65C4.46 80.03 4.86999 80.4 5.28999 80.77C5.73999 81.18 6.20992 81.55 6.71992 81.88C6.99981 82.0599 7.28859 82.231 7.57738 82.402C7.72194 82.4876 7.86651 82.5732 8.00996 82.66C8.16872 82.756 8.32613 82.8574 8.4842 82.9593C8.75421 83.1333 9.02618 83.3087 9.31001 83.46L9.31158 83.4609C9.71105 83.6706 10.1105 83.8803 10.52 84.08C10.789 84.2167 11.0471 84.3661 11.3044 84.5151C11.6496 84.7149 11.9932 84.9139 12.3599 85.08C12.6957 85.2283 13.0334 85.3708 13.3713 85.5135L13.3723 85.5139C13.809 85.6983 14.2461 85.8828 14.68 86.08C14.9036 86.1804 15.1272 86.284 15.3513 86.3878L15.3525 86.3884C15.9097 86.6466 16.4697 86.9062 17.04 87.12C17.1092 87.1452 17.1784 87.1706 17.2477 87.196C17.6186 87.3323 17.9908 87.4689 18.3699 87.57C18.4787 87.6011 18.5859 87.6291 18.6928 87.6571C18.8611 87.7011 19.0287 87.745 19.1999 87.8C19.5899 87.95 19.9699 88.1 20.3499 88.28C21.0325 88.6336 21.6861 89.0353 22.3392 89.4368C23.0161 89.8529 23.6925 90.2686 24.4 90.63L24.4789 90.6704C24.8837 90.8778 25.2975 91.0898 25.7199 91.24C26.1899 91.41 26.67 91.49 27.17 91.56C27.6697 91.63 28.1594 91.7199 28.6491 91.8098C28.9691 91.8699 29.2799 91.95 29.5899 92.04C29.7652 92.1066 29.9355 92.183 30.1056 92.2594C30.4209 92.4008 30.7357 92.5421 31.0799 92.62C31.3399 92.68 31.61 92.73 31.89 92.71C32.07 92.71 32.24 92.69 32.41 92.64C32.93 92.48 33.35 92.15 33.64 91.69C33.9872 91.1205 33.9389 90.4064 33.6024 89.8624C33.615 89.8513 33.6275 89.8404 33.64 89.83C33.9 89.58 34.0799 89.32 34.1899 88.96C34.2899 88.66 34.2899 88.35 34.2099 88.06C34.2199 87.9703 34.2398 87.8807 34.2597 87.7911L34.26 87.79C34.27 87.735 34.2825 87.6825 34.295 87.63C34.3074 87.5775 34.3199 87.525 34.3299 87.47V87.45L34.3626 87.29C34.5418 86.4123 34.7226 85.5267 34.93 84.65C35.1263 83.8089 35.3924 83.0027 35.6631 82.1824C35.682 82.125 35.701 82.0676 35.7199 82.01C36.0049 81.16 36.2649 80.305 36.5249 79.45C36.7849 78.595 37.0449 77.74 37.3299 76.89C37.5999 76.18 37.91 75.49 38.25 74.8C38.25 74.7938 38.2538 74.7876 38.2591 74.7791C38.2623 74.7738 38.2662 74.7676 38.27 74.76C38.28 74.73 38.2999 74.69 38.3199 74.65C38.316 74.6617 38.312 74.6739 38.3075 74.6855C38.2983 74.7037 38.2892 74.7218 38.28 74.74C38.2921 74.7218 38.3005 74.7037 38.3075 74.6855C38.403 74.4965 38.499 74.3075 38.5952 74.1182C39.0442 73.2345 39.495 72.3471 39.9 71.44C40.1131 70.9774 40.3181 70.5107 40.5232 70.0436C40.7703 69.4808 41.0177 68.9174 41.28 68.36C41.622 67.712 42.0072 67.0964 42.3924 66.4808C42.6492 66.0704 42.906 65.66 43.15 65.24C43.3865 64.8388 43.6016 64.4268 43.8164 64.0155C44.1102 63.4529 44.4034 62.8914 44.75 62.36C44.78 62.32 44.81 62.28 44.8499 62.24C45.0158 62.0638 45.1837 61.8902 45.3514 61.7168C45.8311 61.2209 46.3103 60.7256 46.74 60.17C47.26 59.51 47.72 58.8 48.18 58.09C48.53 57.59 48.9099 57.1 49.3199 56.63C49.7299 56.165 50.1499 55.7075 50.5699 55.25C50.9899 54.7925 51.4099 54.335 51.8199 53.87C51.83 53.8599 51.85 53.8399 51.86 53.82C51.87 53.82 51.87 53.81 51.87 53.81C51.87 53.8 51.88 53.8 51.88 53.8H51.87C51.88 53.8 51.89 53.79 51.89 53.79C52.145 53.485 52.385 53.1725 52.6249 52.86C52.8649 52.5475 53.1049 52.235 53.36 51.93C53.56 51.6966 53.7778 51.481 53.9956 51.2654C54.1044 51.1577 54.2133 51.05 54.3199 50.94C54.7299 50.51 55.13 50.07 55.51 49.62C55.726 49.3707 55.9236 49.1092 56.1214 48.8473C56.2808 48.6364 56.4404 48.4252 56.61 48.22C56.635 48.1949 56.6595 48.1698 56.6837 48.1449C56.7257 48.1019 56.7672 48.0594 56.8106 48.0181C57.6438 47.2849 58.4189 46.4848 59.0899 45.59C59.3699 45.22 59.6599 44.86 59.9599 44.5C59.9799 44.475 60.0024 44.45 60.0249 44.425C60.0474 44.4 60.0699 44.375 60.0899 44.35C60.2899 44.11 60.48 43.87 60.67 43.62L60.6705 43.6209C60.7403 43.7706 60.8102 43.9203 60.89 44.06C61.1 44.44 61.37 44.78 61.65 45.12C61.89 45.41 62.1 45.7 62.31 46C62.5174 46.3161 62.7139 46.6431 62.9109 46.9706C63.474 47.907 64.0402 48.8488 64.87 49.56C65.123 49.7768 65.3946 49.9643 65.6656 50.1515C65.9196 50.3267 66.173 50.5017 66.41 50.7C66.444 50.7318 66.478 50.7638 66.5122 50.7959C66.7788 51.0467 67.0508 51.3026 67.37 51.48C68.33 52.03 69.4799 51.81 70.0799 50.85C70.3399 50.42 70.37 49.91 70.38 49.42C70.3818 49.3658 70.3835 49.3117 70.3849 49.2577ZM56.8123 48.0165C56.8165 48.0127 56.8207 48.0089 56.825 48.005C56.8525 47.98 56.88 47.955 56.91 47.93C56.8758 47.958 56.8435 47.9869 56.8123 48.0165ZM56.74 48.08C56.7638 48.0602 56.786 48.0403 56.8079 48.0205C56.7853 48.0404 56.7626 48.0602 56.74 48.08ZM57.4824 41.9915C57.4516 42.0261 57.4208 42.063 57.39 42.1C57.421 42.0639 57.4518 42.0278 57.4824 41.9915ZM43.41 28.4318C43.2411 28.5393 43.072 28.6469 42.9001 28.75C42.4003 29.0999 41.9106 29.4497 41.4208 29.7995C41.0488 30.0613 40.6904 30.352 40.3338 30.6413C40.1727 30.7719 40.0118 30.9025 39.85 31.0301C39.1375 31.586 38.4556 32.1787 37.7756 32.7697C37.5872 32.9335 37.3988 33.0973 37.21 33.26C35.73 34.61 34.2501 35.9701 32.7901 37.3401C31.5301 38.5301 30.31 39.75 29.09 40.98C27.6022 42.4778 26.1445 44.0055 24.6866 45.5332L24.6801 45.54C23.5655 46.7098 22.4934 47.9306 21.4246 49.1476L21.1501 49.4601C20.8551 49.7901 20.5651 50.12 20.2751 50.45C19.9851 50.78 19.6951 51.11 19.4001 51.44C18.9646 51.9508 18.5262 52.4616 18.0864 52.9739L18.0857 52.9747C17.7078 53.415 17.3289 53.8564 16.95 54.3C16.368 54.9779 15.8141 55.6792 15.2595 56.3815C15.0042 56.7048 14.7486 57.0285 14.49 57.35C13.9478 58.0179 13.4362 58.7076 12.9234 59.399C12.6608 59.753 12.3976 60.1079 12.1301 60.4601C11.6215 61.1267 11.146 61.8217 10.6711 62.5158C10.455 62.8316 10.2389 63.1474 10.0201 63.4601C8.41008 65.9801 6.83006 68.5301 5.38006 71.1501C5.48506 71.2051 5.58756 71.2625 5.69006 71.32C5.79256 71.3775 5.89506 71.435 6.00006 71.49C6.65809 71.876 7.26807 72.338 7.87813 72.8001C8.23215 73.0683 8.58652 73.3367 8.95001 73.5901C9.30001 73.8101 9.65008 74.01 10.0201 74.2C10.8435 74.5965 11.7017 74.9174 12.5594 75.238C12.8264 75.3378 13.0942 75.438 13.36 75.54C14.7 76.06 15.98 76.74 17.21 77.48C18.46 78.24 19.72 79 20.98 79.75C21.6875 80.1724 22.3865 80.6059 23.0846 81.0387C23.7087 81.4257 24.3323 81.8124 24.96 82.19C25.712 82.646 26.4895 83.0508 27.267 83.4556C27.4615 83.5568 27.656 83.658 27.85 83.76C27.9686 83.2299 28.0726 82.6948 28.1756 82.1651C28.2203 81.9355 28.2648 81.7068 28.3101 81.48C28.6001 80.08 28.9501 78.68 29.4301 77.33C29.4151 77.3651 29.4026 77.4 29.3901 77.435C29.3776 77.47 29.365 77.505 29.35 77.54C30.6 73.93 32.0101 70.36 33.6801 66.92C33.8282 66.6203 33.9716 66.3183 34.1151 66.0159C34.3882 65.4405 34.662 64.8637 34.97 64.3C35.23 63.82 35.49 63.3375 35.7501 62.855C36.0101 62.3725 36.2701 61.89 36.5301 61.41C36.6307 61.2206 36.7453 61.0383 36.8594 60.8567C36.9381 60.7315 37.0165 60.6066 37.09 60.48C37.3476 60.0579 37.6 59.6359 37.8547 59.2102L37.8552 59.2092C37.9563 59.0402 38.0578 58.8705 38.1601 58.7C38.523 58.1048 38.9229 57.5307 39.3213 56.9585C39.4718 56.7424 39.6221 56.5266 39.7701 56.31C41.6181 53.6188 43.804 51.169 45.9742 48.7368C46.2232 48.4578 46.472 48.179 46.72 47.9001C48.0346 46.4062 49.4187 44.9619 50.7931 43.5278L50.8101 43.51C52.0801 42.18 53.34 40.84 54.58 39.48C54.8435 39.1927 55.1054 38.9031 55.3676 38.6132L55.3686 38.6121C56.4297 37.4388 57.4956 36.2602 58.69 35.2101C59.28 34.6901 59.9401 34.25 60.7501 34.23C61.0611 34.2218 61.3587 34.3007 61.6538 34.379C61.7193 34.3963 61.7847 34.4137 61.85 34.43C62.56 34.61 63.1401 35.11 63.6501 35.62C64.139 36.1089 64.6052 36.6261 65.0701 37.1417C65.2233 37.3116 65.3764 37.4815 65.5301 37.6501C66.1142 38.2813 66.6362 38.9746 67.1546 39.6632L67.2501 39.79C67.467 40.0755 67.6807 40.361 67.893 40.6446L67.8954 40.6478C68.0542 40.8599 68.2121 41.0709 68.3701 41.2801C68.3801 41.1801 68.3925 41.0826 68.405 40.9851C68.4175 40.8875 68.43 40.79 68.44 40.69C68.54 39.805 68.635 38.9225 68.73 38.04C68.8251 37.1575 68.9201 36.275 69.0201 35.39C69.2301 33.48 69.45 31.58 69.7 29.68C69.98 27.55 70.2901 25.42 70.6201 23.29C70.7745 22.2561 70.965 21.2267 71.1552 20.1988C71.2483 19.6959 71.3414 19.1926 71.4301 18.69L71.4688 18.4683C71.7859 16.653 72.1014 14.8461 72.3801 13.0301C72.6401 11.3201 72.87 9.61005 73.07 7.89005C73.1693 7.00647 73.2785 6.12289 73.3877 5.23931L73.3879 5.23767L73.3901 5.22004C73.4088 5.06561 73.4297 4.91119 73.4508 4.75575C73.4747 4.57912 73.4988 4.40081 73.5201 4.22004C73.4401 4.24004 73.3601 4.25753 73.2801 4.27503C73.2001 4.29252 73.1201 4.31002 73.0401 4.33002C72.6708 4.40168 72.3015 4.4703 71.9305 4.53923C71.6285 4.59534 71.3252 4.6517 71.0201 4.71003C70.1101 4.92003 69.1901 5.14005 68.2901 5.45005C68.3251 5.43505 68.3626 5.42254 68.4001 5.41004C68.4376 5.39753 68.4751 5.38503 68.5101 5.37003L65.8701 6.30002C62.1201 7.71002 58.3801 9.17001 54.6601 10.66C53.2929 11.2069 51.9394 11.7813 50.5883 12.3546L50.586 12.3556C50.3073 12.4739 50.0287 12.5921 49.7501 12.7101C49.2439 12.9219 48.7413 13.1372 48.24 13.3519C47.8898 13.502 47.5395 13.652 47.19 13.8C46.8992 13.922 46.6103 14.0459 46.3218 14.1697C45.688 14.4416 45.056 14.7127 44.4101 14.9601C43.9903 15.12 43.5706 15.2774 43.1508 15.4348L43.1494 15.4353C42.7296 15.5927 42.3098 15.7501 41.8901 15.91C41.3575 16.1125 40.8081 16.2813 40.2587 16.4501C40.0756 16.5063 39.8925 16.5625 39.71 16.62C38.9111 16.8762 38.1046 17.1079 37.2979 17.3396C36.2469 17.6416 35.1955 17.9436 34.1601 18.3C33.3701 18.59 32.59 18.91 31.82 19.26C31.7807 19.2797 31.7403 19.3004 31.6993 19.3215C31.6149 19.3648 31.5274 19.4097 31.44 19.45C31.64 19.52 31.8301 19.59 32.0201 19.66C32.6741 19.9249 33.3075 20.2104 33.9487 20.4994C34.082 20.5595 34.2158 20.6198 34.35 20.68C35.13 21.03 35.91 21.34 36.71 21.63C38.74 22.24 40.78 22.81 42.85 23.26C43.34 23.35 43.8201 23.43 44.3101 23.47C44.4505 23.4767 44.5953 23.4744 44.7386 23.4722C44.8096 23.4711 44.8804 23.47 44.95 23.47L45.22 23.44L46.09 22.99C46.68 22.67 47.3501 22.41 48.0201 22.74C48.7201 23.09 49.22 23.8 49.08 24.61C48.94 25.46 48.3201 25.91 47.6301 26.32C47.2401 26.55 46.8101 26.71 46.3801 26.82C46.2598 26.8877 46.1338 26.9497 46.0107 27.0104L45.9271 27.0516C45.931 27.0506 45.9352 27.05 45.94 27.05C45.36 27.32 44.8 27.62 44.24 27.93C43.9559 28.0845 43.6831 28.258 43.41 28.4318Z"
        fill="black"
      />
    </svg>
  );
};
const Arrow002: React.FC<svgProps> = ({ className }) => {
  return (
    <svg
      width="98"
      height="187"
      viewBox="0 0 98 187"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M44.4076 0.056348C38.7595 0.877517 32.6896 3.7868 26.9712 8.43227C23.0574 11.5996 17.9484 17.1132 13.9409 22.4156C1.21519 39.3317 -3.12046 59.4386 2.2698 76.4016C3.27754 79.6159 5.90239 84.9183 7.8007 87.593C9.6287 90.1972 12.3941 93.0831 14.6205 94.7489C17.011 96.532 20.6904 98.3385 23.1981 98.9485C24.2996 99.2301 25.2136 99.4647 25.237 99.4882C25.2604 99.5116 24.8151 100.99 24.2292 102.796C22.2372 108.99 20.2451 117.835 19.3077 124.733C18.7452 128.816 18.7453 135.127 19.2843 137.168C20.5264 141.814 23.3621 144.629 27.0415 144.864C27.9555 144.911 29.1039 144.864 29.5961 144.723C30.3929 144.512 30.4632 144.512 30.3694 144.864C30.1351 145.661 29.3383 149.298 29.0336 151.011C28.5649 153.545 28.6352 160.419 29.1508 163.187C31.096 173.886 38.1502 182.027 48.2276 185.265C51.8133 186.438 54.2272 186.743 58.2348 186.602C63.7891 186.391 67.5388 185.406 73.0931 182.684C79.1865 179.705 83.944 175.904 86.8734 171.728L88.0452 170.015L88.1859 173.956C88.3499 179.634 89.0764 181.722 91.5138 183.74C93.2012 185.101 93.6699 184.843 93.248 182.731C92.9668 181.417 93.0137 181.159 93.9277 177.382C94.4667 175.2 95.076 172.408 95.2635 171.188C95.4744 169.968 96.1306 166.801 96.7165 164.149C98.4039 156.571 98.3336 154.295 96.3885 153.075C95.076 152.278 93.9042 152.395 91.7715 153.568C90.6466 154.201 89.3108 154.718 88.2796 154.929C85.9126 155.422 84.4361 155.938 79.702 157.979C72.8822 160.912 72.3432 161.24 71.9213 162.789C71.7338 163.539 71.7573 163.868 72.1088 164.548C73.4212 167.176 75.4836 166.965 84.3892 163.328C86.7328 162.366 88.7014 161.615 88.7483 161.662C88.9592 161.874 86.4282 166.542 85.2798 168.044C83.5221 170.39 80.7567 173.135 78.8115 174.496C76.7492 175.927 72.8354 177.875 69.7887 179.024C59.9222 182.708 50.6884 181.769 43.353 176.326C35.3613 170.461 32.3147 159.926 34.916 147.304C35.7597 143.268 37.658 138.482 39.8376 134.939C41.2203 132.687 41.5484 131.655 41.2203 130.669C40.939 129.754 40.4235 129.426 39.322 129.426C37.8455 129.426 36.9315 130.552 34.3535 135.502C33.3927 137.309 32.7131 138.341 32.1037 138.81C29.3383 140.992 26.4791 140.969 25.1198 138.764C23.1278 135.526 23.5965 127.15 26.4791 114.41C27.6978 108.99 30.3226 100.333 30.9788 99.4882C31.096 99.3239 31.6584 99.1362 32.2209 99.0424C33.6739 98.8312 36.8378 97.7285 38.6658 96.8135C45.0638 93.5757 51.7196 86.1852 54.2975 79.4516C56.1958 74.5012 54.4616 71.1696 50.0322 71.193C46.4231 71.2165 40.236 74.6654 36.5566 78.7009C33.8614 81.6102 30.6741 86.8656 28.2368 92.3557C27.6275 93.7165 27.065 94.8896 26.9947 94.9835C26.9009 95.0773 26.151 95.0069 25.3073 94.8427C18.7453 93.4584 12.7222 88.0857 8.71467 80.0147C5.55082 73.68 4.26187 67.181 4.51967 59.0397C4.75403 51.9073 6.20701 45.6664 9.27711 38.2759C14.058 26.8499 24.3933 13.7347 33.5099 7.54071C35.7129 6.03915 39.861 3.9745 42.1109 3.24718C45.9543 1.98023 48.5323 2.07408 52.3055 3.59911C53.4538 4.04488 54.4381 4.37335 54.5084 4.30297C55.024 3.7868 51.9305 1.34675 49.7978 0.619434C48.462 0.173656 45.6966 -0.131348 44.4076 0.056348ZM51.6259 75.3223C51.9071 75.7212 51.6727 77.2932 51.1102 78.7713C50.829 79.522 49.8447 81.2582 48.9307 82.6425C46.1419 86.8891 41.8062 90.8776 37.7987 92.8954C36.5097 93.5523 33.0411 94.7958 32.9005 94.655C32.7833 94.5142 34.6582 90.4318 35.9472 88.0153C39.1814 81.9386 42.5327 78.3489 47.2902 75.9558C49.5401 74.8062 51.1103 74.595 51.6259 75.3223Z"
        fill="currentColor"
      />
    </svg>
  );
};

export {
  CampHouse,
  PalmTrees,
  SummerHut,
  SleepUnderTree,
  Underline08,
  Arrow11,
  Arrow09,
  Highlight05,
  Smile,
  SadFace,
  Lamp,
  Bag,
  TwitterSvg,
  Arrow001,
  Arrow002,
};
