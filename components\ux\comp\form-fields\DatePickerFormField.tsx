'use client';

import { useState } from 'react';

// RHF
import { useFormContext } from 'react-hook-form';

// ShadCn
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

// Utils
import { cn } from '@/lib/utils';

// Icons
import { CalendarIcon } from 'lucide-react';

// Types
import { NameType } from '@/types';
import { DATE_OPTIONS } from '@/lib/constants';

type DatePickerFormFieldProps = {
  name: NameType;
  label?: string;
};

const DatePickerFormField = ({ name, label }: DatePickerFormFieldProps) => {
  const { control } = useFormContext();

  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  return (
    <>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <div className='flex flex-col items-start justify-between gap-1 text-sm'>
              <div>
                <FormLabel className='ml-2 text-sm text-neutral-400'>
                  {label}
                </FormLabel>
              </div>
              <div>
                <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={'outline'}
                        className={cn(
                          'w-[300px] rounded-xl md:w-[13rem]',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className='mr-2 h-4 w-4' />
                        {field.value ? (
                          new Date(field.value).toLocaleDateString(
                            'en-US',
                            DATE_OPTIONS
                          )
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className='w-auto p-0'>
                    <p>Calendar</p>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </div>
            </div>
          </FormItem>
        )}
      />
    </>
  );
};

export default DatePickerFormField;
