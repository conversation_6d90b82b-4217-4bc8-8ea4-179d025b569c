'use client';
import { userStore } from '@/lib/store/user';
import { useEffect } from 'react';
import { supabaseClient } from '../client';
import { supabaseServer } from './../server-client';

// Server-side function to get session
export async function getSession() {
  try {
    const supabase = await supabaseServer();
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session;
  } catch (error) {
    console.error('Error getting session:', error);
    return null;
  }
}

export function useGetSession() {
  const supabase = supabaseClient;

  // Move userStore inside the hook to ensure it's only called during component render
  const {
    updateUser,
    updateProfile,
    removeProfile,
    removeUser,
    profile,
    user,
  } = userStore();

  useEffect(() => {
    supabase.auth.getUser().then(({ data: { user } }) => {
      updateUser(user);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        removeUser(null);
      }
      updateUser(session?.user!);
    });
  }, [updateUser, removeUser]);

  useEffect(() => {
    if (!user) {
      removeProfile(null);
      removeUser(null);
    } else {
      // First, fetch the profile data
      supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id!)
        .single()
        .then(async ({ data, error }) => {
          if (error) {
            console.error('Error fetching profile:', error);
            return null;
          }

          let profileData = data;
          console.log('Initial profile data:', profileData);

          // Now check if the user is a lawyer by querying the lawyers table
          const { data: lawyerData, error: lawyerError } = await supabase
            .from('lawyers')
            .select('*')
            .eq('user_id', user.id!)
            .single();

          if (!lawyerError && lawyerData) {
            // User is a lawyer, update the role
            console.log('User is a lawyer:', lawyerData);
            profileData = {
              ...profileData,
              user_role: 'lawyer',
            };
          } else {
            // User is not a lawyer, ensure role is 'user'
            console.log('User is not a lawyer');
            profileData = {
              ...profileData,
              user_role: 'user',
            };
          }

          console.log('Final profile data with correct role:', profileData);
          updateProfile(profileData);
        });
    }
  }, [user, supabase, updateProfile, removeProfile, removeUser]);
  // !session?.user.email ? console.log("Not Logged In") : console.log(profile);

  return { profile, user };
}
