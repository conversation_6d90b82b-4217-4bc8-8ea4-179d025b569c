'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers, useRecurringConsultations } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import {
  RecurringConsultationFormData,
  RecurringFrequency,
} from '@/lib/types/database-modules';
import { cn } from '@/lib/utils';
import { addMonths, format, parse } from 'date-fns';
import {
  Calendar as CalendarIcon,
  Check,
  FileText,
  Loader2,
  Repeat,
  Video,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface RecurringConsultationFormProps {
  recurringConsultationId?: string;
  lawyerId?: string;
  clientId?: string;
  documentId?: string;
  onSuccess?: (consultation: any) => void;
  onCancel?: () => void;
}

export function RecurringConsultationForm({
  recurringConsultationId,
  lawyerId,
  clientId,
  documentId,
  onSuccess,
  onCancel,
}: RecurringConsultationFormProps) {
  const { profile } = userStore();
  const isLawyer = profile?.role === 'lawyer';

  const {
    getRecurringConsultationById,
    createRecurringConsultation,
    updateRecurringConsultation,
  } = useRecurringConsultations();

  const {
    loading: loadingLawyers,
    lawyers,
    fetchAllLawyers,
    getLawyerById,
  } = useLawyers();

  // Form state
  const [title, setTitle] = useState('Regular Consultation');
  const [description, setDescription] = useState('');
  const [dayOfWeek, setDayOfWeek] = useState<number>(1); // Monday
  const [startTime, setStartTime] = useState('09:00:00');
  const [durationMinutes, setDurationMinutes] = useState(60);
  const [frequency, setFrequency] = useState<RecurringFrequency>('weekly');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date | undefined>(
    addMonths(new Date(), 3)
  );
  const [consultationType, setConsultationType] = useState<
    'video' | 'document'
  >('video');
  const [location, setLocation] = useState('');
  const [selectedLawyer, setSelectedLawyer] = useState<any | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);

  // Load recurring consultation if editing
  useEffect(() => {
    const loadRecurringConsultation = async () => {
      if (recurringConsultationId) {
        setIsLoading(true);
        setIsEditing(true);

        // Create a promise for loading the consultation
        const loadConsultationPromise = async () => {
          try {
            const recurringConsultation = await getRecurringConsultationById(
              recurringConsultationId
            );

            if (recurringConsultation) {
              setTitle(recurringConsultation.title);
              setDescription(recurringConsultation.description || '');
              setDayOfWeek(recurringConsultation.day_of_week);
              setStartTime(recurringConsultation.start_time);
              setDurationMinutes(recurringConsultation.duration_minutes);
              setFrequency(
                recurringConsultation.frequency as RecurringFrequency
              );
              setStartDate(new Date(recurringConsultation.start_date));
              setEndDate(
                recurringConsultation.end_date
                  ? new Date(recurringConsultation.end_date)
                  : undefined
              );
              setConsultationType(
                recurringConsultation.consultation_type as 'video' | 'document'
              );
              setLocation(recurringConsultation.location || '');

              // Load lawyer
              if (recurringConsultation.lawyer_id) {
                const lawyer = await getLawyerById(
                  recurringConsultation.lawyer_id
                );
                setSelectedLawyer(lawyer);
              }

              return recurringConsultation;
            } else {
              throw new Error('Failed to load recurring consultation');
            }
          } catch (error) {
            console.error('Error loading recurring consultation:', error);
            onCancel?.();
            throw error;
          } finally {
            setIsLoading(false);
          }
        };

        // Use toast.promise for better user feedback
        toast.promise(loadConsultationPromise(), {
          loading: 'Loading recurring consultation...',
          success: 'Consultation loaded successfully',
          error: 'Failed to load recurring consultation',
        });
      } else {
        setIsEditing(false);
        setIsLoading(false);
      }
    };

    loadRecurringConsultation();
  }, [
    recurringConsultationId,
    getRecurringConsultationById,
    getLawyerById,
    onCancel,
  ]);

  // Load lawyer if lawyerId is provided
  useEffect(() => {
    const loadLawyer = async () => {
      if (lawyerId && !selectedLawyer) {
        // Create a promise for loading the lawyer
        const loadLawyerPromise = async () => {
          try {
            const lawyer = await getLawyerById(lawyerId);
            if (lawyer) {
              setSelectedLawyer(lawyer);
              return lawyer;
            } else {
              throw new Error('Lawyer not found');
            }
          } catch (error) {
            console.error('Error loading lawyer:', error);
            throw error;
          }
        };

        // Use toast.promise for better user feedback
        toast.promise(loadLawyerPromise(), {
          loading: 'Loading lawyer profile...',
          success: 'Lawyer profile loaded successfully',
          error: 'Failed to load lawyer profile',
        });
      } else if (!lawyerId && !selectedLawyer && isLawyer) {
        // If user is a lawyer, load lawyers to select from
        const loadLawyersPromise = fetchAllLawyers();

        // Use toast.promise for better user feedback
        toast.promise(loadLawyersPromise, {
          loading: 'Loading lawyers...',
          success: 'Lawyers loaded successfully',
          error: 'Failed to load lawyers',
        });
      }
    };

    loadLawyer();
  }, [lawyerId, selectedLawyer, getLawyerById, fetchAllLawyers, isLawyer]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedLawyer) {
      toast.error('Please select a lawyer');
      return;
    }

    setIsSubmitting(true);

    // Create the form data
    const formData: RecurringConsultationFormData = {
      title,
      description,
      day_of_week: dayOfWeek,
      start_time: startTime,
      duration_minutes: durationMinutes,
      frequency,
      start_date: startDate,
      end_date: endDate,
      document_id: documentId,
      consultation_type: consultationType,
      location,
      lawyer_id: selectedLawyer.id,
      client_id: clientId,
    };

    // Create a promise for the submission
    const submitPromise = async () => {
      try {
        let result;

        if (isEditing && recurringConsultationId) {
          result = await updateRecurringConsultation(
            recurringConsultationId,
            formData
          );
        } else {
          result = await createRecurringConsultation(formData);
        }

        if (result) {
          onSuccess?.(result);
          return result;
        } else {
          throw new Error('Failed to save recurring consultation');
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        throw error;
      } finally {
        setIsSubmitting(false);
      }
    };

    // Use toast.promise for better user feedback
    toast.promise(submitPromise(), {
      loading: isEditing
        ? 'Updating recurring consultation...'
        : 'Creating recurring consultation...',
      success: isEditing
        ? 'Recurring consultation updated successfully'
        : 'Recurring consultation created successfully',
      error: (err) =>
        `Failed to save recurring consultation: ${err instanceof Error ? err.message : 'Please try again'}`,
    });
  };

  // Format day of week
  const formatDayOfWeek = (day: number) => {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    return days[day];
  };

  // Format time
  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(':');
      return format(
        new Date().setHours(parseInt(hours), parseInt(minutes)),
        'h:mm a'
      );
    } catch (error) {
      return time;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-3/4" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </CardContent>
        <CardFooter>
          <Skeleton className="h-10 w-24 mr-2" />
          <Skeleton className="h-10 w-24" />
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {isEditing
            ? 'Edit Recurring Consultation'
            : 'Create Recurring Consultation'}
        </CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Lawyer Selection */}
          {!lawyerId && !isEditing && (
            <div className="space-y-2">
              <Label htmlFor="lawyer">Lawyer</Label>
              {loadingLawyers ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select
                  value={selectedLawyer?.id}
                  onValueChange={(value) => {
                    const lawyer = lawyers.find((l) => l.id === value);
                    setSelectedLawyer(lawyer);
                  }}
                >
                  <SelectTrigger id="lawyer">
                    <SelectValue placeholder="Select a lawyer" />
                  </SelectTrigger>
                  <SelectContent>
                    {lawyers.map((lawyer) => (
                      <SelectItem key={lawyer.id} value={lawyer.id}>
                        {lawyer.full_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          )}

          {/* Selected Lawyer Display */}
          {selectedLawyer && (
            <div className="flex items-center gap-4 p-4 border rounded-md">
              <UserAvatar
                avatarUrl={selectedLawyer.avatar_url}
                fallbackText={selectedLawyer.full_name}
                size="lg"
                className="h-12 w-12"
              />
              <div>
                <h3 className="font-medium">{selectedLawyer.full_name}</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedLawyer.specialization}
                </p>
              </div>
            </div>
          )}

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Consultation title"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Consultation description"
              className="min-h-[100px]"
            />
          </div>

          {/* Frequency */}
          <div className="space-y-2">
            <Label htmlFor="frequency">Frequency</Label>
            <Select
              value={frequency}
              onValueChange={(value) =>
                setFrequency(value as RecurringFrequency)
              }
            >
              <SelectTrigger id="frequency">
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="biweekly">Bi-weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Day of Week */}
          <div className="space-y-2">
            <Label htmlFor="day-of-week">Day of Week</Label>
            <Select
              value={dayOfWeek.toString()}
              onValueChange={(value) => setDayOfWeek(parseInt(value))}
            >
              <SelectTrigger id="day-of-week">
                <SelectValue placeholder="Select day of week" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">Sunday</SelectItem>
                <SelectItem value="1">Monday</SelectItem>
                <SelectItem value="2">Tuesday</SelectItem>
                <SelectItem value="3">Wednesday</SelectItem>
                <SelectItem value="4">Thursday</SelectItem>
                <SelectItem value="5">Friday</SelectItem>
                <SelectItem value="6">Saturday</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Start Time */}
          <div className="space-y-2">
            <Label htmlFor="start-time">Start Time</Label>
            <Select
              value={startTime.substring(0, 5)}
              onValueChange={(value) => setStartTime(`${value}:00`)}
            >
              <SelectTrigger id="start-time">
                <SelectValue placeholder="Select start time" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 24 }, (_, hour) =>
                  Array.from({ length: 4 }, (_, quarterHour) => {
                    const h = hour.toString().padStart(2, '0');
                    const m = (quarterHour * 15).toString().padStart(2, '0');
                    const time = `${h}:${m}`;
                    return (
                      <SelectItem key={time} value={time}>
                        {format(parse(time, 'HH:mm', new Date()), 'h:mm a')}
                      </SelectItem>
                    );
                  })
                ).flat()}
              </SelectContent>
            </Select>
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Duration</Label>
            <Select
              value={durationMinutes.toString()}
              onValueChange={(value) => setDurationMinutes(parseInt(value))}
            >
              <SelectTrigger id="duration">
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="45">45 minutes</SelectItem>
                <SelectItem value="60">60 minutes</SelectItem>
                <SelectItem value="90">90 minutes</SelectItem>
                <SelectItem value="120">2 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start-date">Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="start-date"
                  variant="outline"
                  className={cn(
                    'w-full justify-start text-left font-normal',
                    !startDate && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, 'PPP') : 'Select start date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => date && setStartDate(date)}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="end-date">End Date</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() =>
                  setEndDate(endDate ? undefined : addMonths(new Date(), 3))
                }
              >
                {endDate ? 'No End Date' : 'Set End Date'}
              </Button>
            </div>
            {endDate && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="end-date"
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(endDate, 'PPP')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    disabled={(date) => date < startDate}
                  />
                </PopoverContent>
              </Popover>
            )}
          </div>

          {/* Consultation Type */}
          <div className="space-y-2">
            <Label className="text-base font-medium block">
              Consultation Type
            </Label>
            <RadioGroup
              value={consultationType}
              onValueChange={(value) =>
                setConsultationType(value as 'video' | 'document')
              }
              className="flex flex-col space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="video" id="video" />
                <Label htmlFor="video" className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  Video Consultation
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value="document"
                  id="document"
                  disabled={!documentId}
                />
                <Label
                  htmlFor="document"
                  className={`flex items-center gap-2 ${!documentId ? 'text-muted-foreground' : ''}`}
                >
                  <FileText className="h-4 w-4" />
                  Document Review
                  {!documentId && (
                    <span className="text-xs">(No document selected)</span>
                  )}
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Location (Optional)</Label>
            <Input
              id="location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="Meeting location or URL"
            />
          </div>

          {/* Summary */}
          <div className="bg-muted p-4 rounded-md space-y-2 mt-4">
            <h3 className="font-medium flex items-center gap-2">
              <Repeat className="h-4 w-4" />
              Recurring Schedule Summary
            </h3>
            <p className="text-sm">
              This consultation will occur <strong>{frequency}</strong> on{' '}
              <strong>{formatDayOfWeek(dayOfWeek)}</strong> at{' '}
              <strong>{formatTime(startTime)}</strong> for{' '}
              <strong>{durationMinutes} minutes</strong>.
            </p>
            <p className="text-sm">
              Starting from <strong>{format(startDate, 'MMMM d, yyyy')}</strong>
              {endDate
                ? ` until ${format(endDate, 'MMMM d, yyyy')}`
                : ' with no end date'}
              .
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting || !selectedLawyer}>
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                {isEditing ? 'Update' : 'Create'}
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
