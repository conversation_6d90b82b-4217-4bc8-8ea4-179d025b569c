import { userStore } from '@/lib/store/user';
import { useEffect } from 'react';
import { supabaseClient } from '../client';

export function useGetUser() {
  const { updateUser, user, removeUser } = userStore((state) => state);

  useEffect(() => {
    supabaseClient.auth.getSession().then(({ data: { session } }) => {
      updateUser(session?.user!);
    });

    supabaseClient.auth.onAuthStateChange((_event, session) => {
      if (!session) {
        removeUser(null);
      }
      updateUser(session?.user!);
    });
  }, []);

  return { user };
}
