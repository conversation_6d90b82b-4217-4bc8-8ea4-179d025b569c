-- Migration to fix the realtime.broadcast_changes function

-- First, ensure the realtime schema exists
CREATE SCHEMA IF NOT EXISTS realtime;

-- Create the realtime.broadcast_changes function
CREATE OR REPLACE FUNCTION realtime.broadcast_changes(
  topic text,
  event text,
  operation text,
  table_name name,
  schema_name name,
  record json,
  old_record json DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  payload json;
BEGIN
  -- Build the payload
  payload := json_build_object(
    'topic', topic,
    'event', event,
    'operation', operation,
    'table', table_name,
    'schema', schema_name,
    'record', record,
    'old_record', old_record
  );

  -- Send the payload to the realtime system
  PERFORM pg_notify('realtime', payload::text);
END;
$$;

-- Create a simpler version of the function that doesn't require all parameters
CREATE OR REPLACE FUNCTION realtime.broadcast_changes(
  topic text,
  event text,
  operation text,
  record json
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Call the full function with default values for missing parameters
  PERFORM realtime.broadcast_changes(
    topic,
    event,
    operation,
    'notifications'::name,
    'public'::name,
    record,
    NULL
  );
END;
$$;

-- Create a function to send direct messages to a channel
CREATE OR REPLACE FUNCTION realtime.send(
  payload json,
  event text DEFAULT 'broadcast',
  topic text DEFAULT NULL,
  private boolean DEFAULT false
)
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  channel text;
  final_payload json;
BEGIN
  -- Set the channel name
  channel := CASE
    WHEN topic IS NULL THEN 'realtime'
    ELSE 'realtime:' || topic
  END;

  -- Build the final payload
  final_payload := json_build_object(
    'event', event,
    'payload', payload,
    'private', private
  );

  -- Send the notification
  PERFORM pg_notify(channel, final_payload::text);
END;
$$;

-- Create a helper function to send direct messages to a channel
CREATE OR REPLACE FUNCTION public.send_realtime_message(
  topic TEXT,
  event TEXT,
  payload JSONB
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  -- Use the realtime.send function to send a message to a channel
  PERFORM realtime.send(payload, event, topic, false);
END;
$$;

-- Recreate the notification changes trigger
DROP TRIGGER IF EXISTS on_notification_changes ON public.notifications;
CREATE TRIGGER on_notification_changes
  AFTER INSERT OR UPDATE OR DELETE
  ON public.notifications
  FOR EACH ROW
  EXECUTE FUNCTION public.broadcast_notification_changes();
