'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useOrganizations } from '@/lib/hooks';
import { Organization } from '@/lib/types/database-modules';
import { Building2, ChevronRight, Plus, Search } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function OrganizationsPage() {
  const { username } = useParams();
  const { organizations, loading, error, fetchOrganizations } =
    useOrganizations();

  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOrganizations, setFilteredOrganizations] = useState<
    Organization[]
  >([]);

  // Filter organizations based on search query
  useEffect(() => {
    if (!organizations) {
      setFilteredOrganizations([]);
      return;
    }

    if (!searchQuery) {
      setFilteredOrganizations(organizations);
      return;
    }

    const filtered = organizations.filter(
      (org) =>
        org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (org.description &&
          org.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    setFilteredOrganizations(filtered);
  }, [organizations, searchQuery]);

  // Get subscription badge color
  const getSubscriptionBadgeColor = (level: string) => {
    switch (level) {
      case 'professional':
        return 'bg-blue-100 text-blue-800';
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-neutral-500" />
            <h1 className="text-2xl font-bold">Organizations</h1>
          </div>
          <p className="text-neutral-500 mt-1">
            Manage your organizations and teams
          </p>
        </div>
        <Link href={`/${username}/organizations/new`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Organization
          </Button>
        </Link>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative w-full md:max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
          <Input
            className="pl-10"
            placeholder="Search organizations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2 ml-auto">
          <Tabs defaultValue="all">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="owned">Owned</TabsTrigger>
              <TabsTrigger value="member">Member</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="h-5 w-24 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 w-full bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="h-4 w-20 bg-gray-200 rounded"></div>
                    <div className="flex -space-x-2">
                      {[1, 2, 3].map((j) => (
                        <div
                          key={j}
                          className="h-8 w-8 rounded-full bg-gray-200"
                        ></div>
                      ))}
                    </div>
                  </div>
                  <div className="h-4 w-full bg-gray-200 rounded"></div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="h-9 w-full bg-gray-200 rounded"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Building2 className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Error Loading Organizations
            </h3>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              {error}
            </p>
            <Button onClick={() => fetchOrganizations()}>Try Again</Button>
          </CardContent>
        </Card>
      ) : filteredOrganizations.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Organizations Found</h3>
            <p className="text-sm text-muted-foreground max-w-md mb-4">
              {searchQuery
                ? 'No organizations match your search criteria.'
                : "You don't have any organizations yet. Create your first organization to get started."}
            </p>
            <Link href={`/${username}/organizations/new`}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Organization
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOrganizations.map((org) => (
            <Card key={org.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{org.name}</CardTitle>
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${getSubscriptionBadgeColor(org.subscription_level)}`}
                  >
                    {org.subscription_level.charAt(0).toUpperCase() +
                      org.subscription_level.slice(1)}
                  </div>
                </div>
                <CardDescription>
                  {org.description || 'No description provided'}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm">
                      <span className="text-muted-foreground">Created: </span>
                      <span className="font-medium">
                        {new Date(org.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Link
                  href={`/${username}/organizations/${org.id}`}
                  className="w-full"
                >
                  <Button variant="outline" className="w-full justify-between">
                    <span>View Organization</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
