import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON>itle,
} from '@/components/ui/card';
import { ConsultingRequest, RequestPriority, RequestStatus } from '@/types';
import {
  Calendar<PERSON><PERSON>,
  ChevronRight,
  Clock,
  MessageSquare,
  User,
} from 'lucide-react';
import Link from 'next/link';

interface ConsultingRequestCardProps {
  request: ConsultingRequest;
  showActions?: boolean;
}

export function ConsultingRequestCard({
  request,
  showActions = true,
}: ConsultingRequestCardProps) {
  const getStatusBadge = (status: RequestStatus) => {
    const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
    switch (status) {
      case 'pending':
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>
            Pending
          </span>
        );
      case 'accepted':
        return (
          <span className={`${baseClasses} bg-blue-100 text-blue-800`}>
            Accepted
          </span>
        );
      case 'in_progress':
        return (
          <span className={`${baseClasses} bg-purple-100 text-purple-800`}>
            In Progress
          </span>
        );
      case 'completed':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800`}>
            Completed
          </span>
        );
      case 'cancelled':
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  const getPriorityBadge = (priority: RequestPriority) => {
    const baseClasses =
      'inline-flex items-center px-2 py-1 text-xs font-medium rounded-full';
    switch (priority) {
      case 'urgent':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800`}>
            <span className="mr-1 h-1.5 w-1.5 rounded-full bg-red-700"></span>
            Urgent
          </span>
        );
      case 'high':
        return (
          <span className={`${baseClasses} bg-orange-100 text-orange-800`}>
            <span className="mr-1 h-1.5 w-1.5 rounded-full bg-orange-700"></span>
            High
          </span>
        );
      case 'medium':
        return (
          <span className={`${baseClasses} bg-blue-100 text-blue-800`}>
            <span className="mr-1 h-1.5 w-1.5 rounded-full bg-blue-700"></span>
            Medium
          </span>
        );
      case 'low':
        return (
          <span className={`${baseClasses} bg-gray-100 text-gray-800`}>
            <span className="mr-1 h-1.5 w-1.5 rounded-full bg-gray-700"></span>
            Low
          </span>
        );
      default:
        return null;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between mb-1">
          {getStatusBadge(request.status)}
          {getPriorityBadge(request.priority)}
        </div>
        <CardTitle>{request.title}</CardTitle>
        <CardDescription className="line-clamp-2">
          {request.description}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-start gap-2">
              <User className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Requester</span>
                <span className="text-sm">
                  {request.requesterId.replace('user_', '')}
                </span>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <CalendarClock className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Created</span>
                <span className="text-sm">{formatDate(request.createdAt)}</span>
              </div>
            </div>
          </div>

          {request.estimatedHours && (
            <div className="flex items-start gap-2">
              <Clock className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">
                  Estimated Time
                </span>
                <span className="text-sm">{request.estimatedHours} hours</span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1 text-sm">
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
              <span>{request.communications.length} messages</span>
            </div>

            {request.dueDate && (
              <div className="text-sm">
                <span className="text-muted-foreground">Due: </span>
                <span
                  className={
                    new Date(request.dueDate) < new Date()
                      ? 'text-red-600 font-medium'
                      : 'font-medium'
                  }
                >
                  {formatDate(request.dueDate)}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="pt-2">
          <div className="grid w-full grid-cols-2 gap-2">
            <Link
              href={`/organizations/${request.organizationId}/consulting/requests/${request.id}`}
              passHref
            >
              <Button className="w-full justify-between" size="sm">
                <span>View Details</span>
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
            <Link
              href={`/organizations/${request.organizationId}/consulting/requests/${request.id}/messages`}
              passHref
            >
              <Button
                variant="outline"
                className="w-full justify-between"
                size="sm"
              >
                <span>Messages</span>
                <MessageSquare className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
