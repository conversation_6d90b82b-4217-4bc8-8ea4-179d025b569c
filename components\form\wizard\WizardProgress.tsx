'use client';

import BaseButton from '@/components/ux/comp/BaseButton';
import { cn } from '@/lib/utils';
import { InvoiceType, WizardStepType } from '@/types';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { useWizard, WizardValues } from 'react-use-wizard';

type WizardProgressProps = {
  wizard: WizardValues;
};

const WizardProgress = ({ wizard }: WizardProgressProps) => {
  const { activeStep, stepCount } = wizard;

  const {
    formState: { errors },
  } = useFormContext<InvoiceType>();

  const step1Valid = !errors.sender && !errors.receiver;
  const step2Valid =
    !errors.details?.invoiceNumber &&
    !errors.details?.dueDate &&
    !errors.details?.invoiceDate &&
    !errors.details?.currency;

  const step3Valid = !errors.details?.items;
  const step4Valid = !errors.details?.paymentInformation;
  const step5Valid =
    !errors.details?.paymentTerms &&
    !errors.details?.subTotal &&
    !errors.details?.totalAmount &&
    !errors.details?.discountDetails?.amount &&
    !errors.details?.taxDetails?.amount &&
    !errors.details?.shippingDetails?.cost;

  /**
   * Determines the button variant based on the given WizardStepType.
   *
   * @param {WizardStepType} step - The wizard step object
   * @returns The button variant ("destructive", "default", or "outline") based on the step's validity and active status.
   */
  const returnButtonVariant = (step: WizardStepType) => {
    if (!step.isValid) {
      return 'shadow_red';
    }
    if (step.id === activeStep) {
      return 'shadow';
    } else {
      return 'ghost';
    }
  };

  /**
   * Checks whether the given WizardStepType has been passed or not.
   *
   * @param {WizardStepType} currentStep - The WizardStepType object
   * @returns `true` if the step has been passed, `false` if it hasn't, or `undefined` if the step is not valid.
   */
  const stepPassed = (currentStep: WizardStepType) => {
    if (currentStep.isValid) {
      return activeStep > currentStep.id ? true : false;
    }
  };

  const steps: WizardStepType[] = [
    {
      id: 0,
      label: 'From And To',
      isValid: step1Valid,
    },
    {
      id: 1,
      label: 'Invoice Details',
      isValid: step2Valid,
    },
    {
      id: 2,
      label: 'Items',
      isValid: step3Valid,
    },
    {
      id: 3,
      label: 'Payment Info',
      isValid: step4Valid,
    },
    {
      id: 4,
      label: 'Summary',
      isValid: step5Valid,
    },
  ];

  const { isFirstStep, isLastStep, handleStep, previousStep, nextStep } =
    useWizard();

  return (
    <div
      className={cn(
        '',
        'z-[400] w-fit select-none rounded-3xl border border-neutral-200 bg-white/60 backdrop-blur-lg backdrop-filter'
      )}
    >
      <div className="flex items-center justify-center space-x-2 px-1 py-1">
        {!isFirstStep && (
          <BaseButton
            size={'icon'}
            variant={'shadow_dark'}
            tooltipLabel="Previous step"
            onClick={previousStep}
            className="ml-1 size-8"
          >
            <ArrowLeft className="size-4" />
          </BaseButton>
        )}
        <div>
          {steps.map((step, idx) => {
            if (step.id === activeStep) {
              return (
                <div key={step.id} className="flex items-center">
                  <BaseButton
                    variant={returnButtonVariant(step)}
                    className="w-auto"
                    onClick={() => {
                      wizard.goToStep(step.id);
                    }}
                  >
                    {step.id + 1}. {step.label}
                  </BaseButton>
                </div>
              );
            }
          })}
        </div>
        {!isLastStep && (
          <div className="pr-1">
            <BaseButton
              size={'icon'}
              variant={'shadow_dark'}
              tooltipLabel="next step"
              disabled={isLastStep}
              onClick={nextStep}
              className="size-8"
            >
              <ArrowRight className="size-4" />
            </BaseButton>
          </div>
        )}
      </div>
    </div>
  );
};

export default WizardProgress;
