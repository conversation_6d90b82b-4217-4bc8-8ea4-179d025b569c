import {
  DocumentExportService,
  ExportOptions,
} from '@/lib/services/document-export-service';
// Using the auth session function from the auth module
import { getSession } from '@/lib/supabase/auth/session';
// Import doesn't exist yet - commented out
// import { addDocumentActivity } from '@/lib/supabase/db/document-activities';
import { getDocument, incrementExportCount } from '@/lib/supabase/db/documents';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get user session
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get document ID from params
    const { id: documentId } = await params;

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      );
    }

    // Get optional parameters from query
    const searchParams = request.nextUrl.searchParams;
    const includeWatermark = searchParams.get('watermark') === 'true';
    const watermarkText = searchParams.get('watermarkText') || 'CONFIDENTIAL';
    const watermarkOpacity = parseInt(searchParams.get('opacity') || '30');
    const includeMetadata = searchParams.get('metadata') === 'true';
    const includeSignaturePage = searchParams.get('signature') === 'true';
    const fontFamily =
      searchParams.get('font') || '"Times New Roman", Times, serif';

    // Get legal formatting options
    const addSectionNumbers = searchParams.get('sectionNumbers') === 'true';
    const addTableOfContents = searchParams.get('toc') === 'true';
    const addLineNumbers = searchParams.get('lineNumbers') === 'true';
    const addBookmarks = searchParams.get('bookmarks') === 'true';
    const documentType =
      (searchParams.get('docType') as
        | 'contract'
        | 'agreement'
        | 'lease'
        | 'general') || undefined;

    // Get document from database
    const document = await getDocument(documentId);
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Check if user has access to the document
    if (document.owner_id !== session.user.id) {
      // Additional check for shared documents could be added here
      return NextResponse.json(
        { error: 'You do not have access to this document' },
        { status: 403 }
      );
    }

    // Export options
    const options: ExportOptions = {
      format: 'pdf',
      includeWatermark,
      watermarkText: includeWatermark ? watermarkText : undefined,
      watermarkOpacity: includeWatermark ? watermarkOpacity : undefined,
      includeMetadata,
      includeSignaturePage,
      styling: {
        fontFamily,
        fontSize: 12,
        lineHeight: 1.5,
        margins: {
          top: 25,
          right: 25,
          bottom: 25,
          left: 25,
        },
        textAlign: 'justify',
        headingStyle: {
          fontWeight: 'bold',
          fontSize: 16,
          marginBottom: 16,
          textAlign: 'center',
        },
        sectionStyle: {
          marginBottom: 24,
        },
      },
      // Add legal formatting options if requested
      legalFormatting: {
        addSectionNumbers,
        addTableOfContents,
        addLineNumbers,
        addBookmarks,
        documentType,
      },
    };

    // Add jurisdiction and disclaimer information if available
    if (document.jurisdiction) {
      options.watermarkText =
        options.watermarkText || `CONFIDENTIAL - ${document.jurisdiction}`;

      if (document.legal_metadata?.legal_disclaimer) {
        options.legalFormatting = {
          ...options.legalFormatting,
          disclaimer: document.legal_metadata.legal_disclaimer,
        };
      }
    }

    // Export document
    const exportService = new DocumentExportService();
    const pdfBlob = await exportService.exportDocument(document, options);

    // Update export count for analytics
    try {
      await incrementExportCount(documentId);
    } catch (error) {
      console.error('Error updating document export count:', error);
      // Continue even if tracking fails
    }

    // Log document activity
    try {
      // Temporarily commented out as module doesn't exist
      /* await addDocumentActivity({
        document_id: documentId,
        user_id: session.user.id,
        activity_type: 'download',
        activity_data: {
          format: 'pdf',
          includeWatermark,
          includeMetadata,
          includeSignaturePage,
        },
      }); */
      // TODO: Create the document-activities module
    } catch (error) {
      console.error('Error logging document activity:', error);
      // Continue even if logging fails
    }

    // Convert blob to ArrayBuffer for response
    const arrayBuffer = await (pdfBlob as Blob).arrayBuffer();

    // Add jurisdiction to filename if available
    const jurisdictionPrefix = document.jurisdiction
      ? `${document.jurisdiction}_`
      : '';
    const filename = `${jurisdictionPrefix}${document.title.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;

    // Return PDF as response
    return new NextResponse(arrayBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error exporting PDF:', error);
    return NextResponse.json(
      { error: 'Failed to export document' },
      { status: 500 }
    );
  }
}
