'use client';

import { EventCalendar } from '@/components/calendar/EventCalendar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useConsultationCalendar } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { CalendarEvent, CalendarView } from '@/lib/types/database-modules';
import { ArrowLeft, Calendar as CalendarIcon, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function LawyerCalendarPage() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;
  const isLawyer = profile?.role === 'lawyer';

  const [activeTab, setActiveTab] = useState<'calendar' | 'settings'>(
    'calendar'
  );
  const [calendarView, setCalendarView] = useState<CalendarView>('month');

  const {
    events,
    loading,
    error,
    addConsultation,
    updateConsultation,
    deleteConsultation,
  } = useConsultationCalendar();

  // Handle event add
  const handleEventAdd = async (event: CalendarEvent) => {
    await addConsultation(event);
  };

  // Handle event update
  const handleEventUpdate = async (event: CalendarEvent) => {
    await updateConsultation(event);
  };

  // Handle event delete
  const handleEventDelete = async (eventId: string) => {
    await deleteConsultation(eventId);
  };

  // Handle date select
  const handleDateSelect = (date: Date) => {
    router.push(
      `/${username}/lawyer/consultations/book?date=${date.toISOString()}`
    );
  };

  // Handle view change
  const handleViewChange = (view: CalendarView) => {
    setCalendarView(view);
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Consultation Calendar</h1>
          <p className="text-muted-foreground">
            Manage your consultation schedule
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/${username}/lawyer/dashboard`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <Button onClick={() => router.push(`/${username}/lawyer/book`)}>
            <Plus className="h-4 w-4 mr-2" />
            Book Consultation
          </Button>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) =>
          setActiveTab(value as 'calendar' | 'settings')
        }
      >
        <TabsList className="mb-6">
          <TabsTrigger value="calendar">Calendar</TabsTrigger>
          {isLawyer && (
            <TabsTrigger value="settings">Availability Settings</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="calendar">
          {loading ? (
            <Card>
              <CardContent className="p-6 flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              </CardContent>
            </Card>
          ) : error ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-destructive">
                  Error loading calendar. Please try again.
                </p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Refresh
                </Button>
              </CardContent>
            </Card>
          ) : (
            <EventCalendar
              events={events}
              onEventAdd={handleEventAdd}
              onEventUpdate={handleEventUpdate}
              onEventDelete={handleEventDelete}
              initialView={calendarView}
              isReadOnly={!isLawyer}
              onDateSelect={handleDateSelect}
              onViewChange={handleViewChange}
              className="bg-background rounded-lg border p-4"
            />
          )}
        </TabsContent>

        {isLawyer && (
          <TabsContent value="settings">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4">
                  Availability Settings
                </h2>
                <p className="text-muted-foreground mb-4">
                  Configure your availability for consultations.
                </p>
                <Button
                  onClick={() =>
                    router.push(`/${username}/lawyer/availability`)
                  }
                >
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Manage Availability
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
