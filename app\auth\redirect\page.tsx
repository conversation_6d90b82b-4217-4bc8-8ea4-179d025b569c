'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function AuthRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Get the stored redirect path from localStorage
    const redirectPath = localStorage.getItem('redirectPath') || '/';
    
    // Clear the stored path
    localStorage.removeItem('redirectPath');
    
    // Redirect to the stored path or home page
    console.log('Redirecting to:', redirectPath);
    router.push(redirectPath);
  }, [router]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <Loader2 className="h-10 w-10 animate-spin text-primary" />
      <p className="mt-4 text-center text-sm text-neutral-600">
        Completing authentication...
      </p>
    </div>
  );
}
