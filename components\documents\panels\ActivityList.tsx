import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { DocumentActivity as BaseDocumentActivity } from '@/lib/types/database-modules';
import { formatDistanceToNow } from 'date-fns';
import {
  Activity,
  Download,
  Eye,
  FileText,
  MessageSquare,
  Pencil,
  Share2,
  Upload,
} from 'lucide-react';

interface DocumentActivity extends BaseDocumentActivity {
  user_full_name?: string;
  user_email?: string;
  user_avatar_url?: string;
}

interface ActivityListProps {
  activities: DocumentActivity[];
  isLoading: boolean;
}

export function ActivityList({ activities, isLoading }: ActivityListProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin mr-2">
          <Activity size={16} />
        </div>
        <span>Loading activities...</span>
      </div>
    );
  }

  if (!activities || activities.length === 0) {
    return (
      <div className="text-center p-4 text-muted-foreground">
        No activities yet
      </div>
    );
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'view':
        return <Eye size={16} className="text-blue-500" />;
      case 'edit':
        return <Pencil size={16} className="text-amber-500" />;
      case 'comment':
        return <MessageSquare size={16} className="text-green-500" />;
      case 'share':
        return <Share2 size={16} className="text-purple-500" />;
      case 'upload':
        return <Upload size={16} className="text-indigo-500" />;
      case 'download':
        return <Download size={16} className="text-pink-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getActivityText = (activity: DocumentActivity) => {
    // Ensure we have valid data with fallbacks
    const userName =
      (activity as DocumentActivity).user_full_name ||
      (activity as DocumentActivity).user_email?.split('@')[0] ||
      'A user';

    try {
      switch (activity.activity_type) {
        case 'view':
          return `${userName} viewed the document`;
        case 'edit':
          return `${userName} edited the document`;
        case 'comment':
          let commentContent = '';
          if (
            activity.activity_data &&
            typeof activity.activity_data === 'object' &&
            !Array.isArray(activity.activity_data) &&
            'content' in activity.activity_data &&
            typeof (activity.activity_data as any).content === 'string'
          ) {
            commentContent = (activity.activity_data as any).content;
          }
          const shortComment =
            commentContent.length > 30
              ? `${commentContent.substring(0, 30)}...`
              : commentContent;
          return `${userName} commented: "${shortComment}"`;
        case 'share':
          let sharedWith = 'someone';
          if (
            activity.activity_data &&
            typeof activity.activity_data === 'object' &&
            !Array.isArray(activity.activity_data)
          ) {
            sharedWith =
              (activity.activity_data as any).email ||
              (activity.activity_data as any).shared_with ||
              'someone';
          }
          return `${userName} shared the document with ${sharedWith}`;
        case 'upload':
          return `${userName} uploaded a new version`;
        case 'download':
          return `${userName} downloaded the document`;
        default:
          return `${userName} interacted with the document`;
      }
    } catch (error) {
      console.error('Error formatting activity text:', error);
      return `${userName} interacted with the document`;
    }
  };

  return (
    <ScrollArea className="h-[300px] pr-4">
      <div className="space-y-4 p-1">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-3 text-sm">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={
                  (activity as DocumentActivity).user_avatar_url ||
                  `https://ui-avatars.com/api/?name=${encodeURIComponent(
                    (activity as DocumentActivity).user_full_name ||
                      (activity as DocumentActivity).user_email?.split(
                        '@'
                      )[0] ||
                      'User'
                  )}`
                }
                alt={
                  (activity as DocumentActivity).user_full_name ||
                  (activity as DocumentActivity).user_email?.split('@')[0] ||
                  'User'
                }
              />
              <AvatarFallback>
                {(
                  (activity as DocumentActivity).user_full_name ||
                  (activity as DocumentActivity).user_email?.split('@')[0] ||
                  'U'
                ).charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getActivityIcon(activity.activity_type)}
                  <span className="font-medium">
                    {getActivityText(activity)}
                  </span>
                </div>
                <span className="text-xs text-muted-foreground">
                  {activity.created_at
                    ? formatDistanceToNow(new Date(activity.created_at), {
                        addSuffix: true,
                      })
                    : 'Unknown time'}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}
