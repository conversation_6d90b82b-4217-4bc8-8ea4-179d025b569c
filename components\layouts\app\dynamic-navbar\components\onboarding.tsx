import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

const OnboardingAction = () => {
  const pathname = usePathname();
  return (
    <div>
      <Link
        href={`#`}
        className={cn(buttonVariants({ variant: 'shadow', size: 'sm' }), '')}
      >
        <span>Onboarding Action</span>
      </Link>
    </div>
  );
};

export default OnboardingAction;
