import { supabaseClient } from '@/lib/supabase/client';
import { waitlistStore, WaitlistEntry, WaitlistStats } from '@/lib/store/waitlist';
import { useCallback } from 'react';
import { toast } from 'sonner';

export function useWaitlist() {
  const {
    isOnWaitlist,
    userWaitlistEntry,
    stats,
    isLoading,
    error,
    setIsOnWaitlist,
    setUserWaitlistEntry,
    setStats,
    setLoading,
    setError,
    reset,
  } = waitlistStore();

  const joinWaitlist = useCallback(
    async (data: { full_name: string; email: string; role: 'user' | 'lawyer' }) => {
      setLoading(true);
      setError(null);

      try {
        const { data: result, error } = await supabaseClient
          .from('waitlist')
          .insert([data])
          .select()
          .single();

        if (error) {
          if (error.code === '23505') {
            // Unique constraint violation (email already exists)
            setError('This email is already on our waitlist!');
            toast.error('This email is already on our waitlist!');
            return false;
          }
          throw error;
        }

        setIsOnWaitlist(true);
        setUserWaitlistEntry(result);
        toast.success('Successfully joined the waitlist!');
        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to join waitlist';
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setIsOnWaitlist, setUserWaitlistEntry]
  );

  const checkWaitlistStatus = useCallback(
    async (email: string) => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await supabaseClient
          .from('waitlist')
          .select('*')
          .eq('email', email)
          .single();

        if (error && error.code !== 'PGRST116') {
          // PGRST116 is "not found" error, which is expected if user is not on waitlist
          throw error;
        }

        if (data) {
          setIsOnWaitlist(true);
          setUserWaitlistEntry(data);
        } else {
          setIsOnWaitlist(false);
          setUserWaitlistEntry(null);
        }

        return !!data;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to check waitlist status';
        setError(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setIsOnWaitlist, setUserWaitlistEntry]
  );

  const fetchWaitlistStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/waitlist');
      if (!response.ok) {
        throw new Error('Failed to fetch waitlist stats');
      }

      const { stats: fetchedStats } = await response.json();
      setStats(fetchedStats);
      return fetchedStats;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch waitlist stats';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError, setStats]);

  const removeFromWaitlist = useCallback(
    async (email: string) => {
      setLoading(true);
      setError(null);

      try {
        const { error } = await supabaseClient
          .from('waitlist')
          .delete()
          .eq('email', email);

        if (error) {
          throw error;
        }

        setIsOnWaitlist(false);
        setUserWaitlistEntry(null);
        toast.success('Successfully removed from waitlist');
        return true;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to remove from waitlist';
        setError(errorMessage);
        toast.error(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [setLoading, setError, setIsOnWaitlist, setUserWaitlistEntry]
  );

  return {
    // State
    isOnWaitlist,
    userWaitlistEntry,
    stats,
    isLoading,
    error,

    // Actions
    joinWaitlist,
    checkWaitlistStatus,
    fetchWaitlistStats,
    removeFromWaitlist,
    reset,
  };
}
