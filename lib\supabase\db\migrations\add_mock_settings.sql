-- Add mock user settings and wallet connections

-- Insert mock data for user_settings
INSERT INTO public.user_settings (user_id, theme, language, notifications_enabled, email_notifications, display_preferences, export_preferences)
VALUES
  (auth.uid(), 'light', 'en', true, 
   '{"comment_added": true, "task_assigned": true, "contract_signed": true, "document_shared": true, "document_updated": true}'::jsonb,
   '{"document_view": "card", "sidebar_collapsed": false, "show_document_previews": true}'::jsonb,
   '{"default_format": "pdf", "include_metadata": true, "include_signatures": true}'::jsonb)
ON CONFLICT (user_id) DO UPDATE SET
  theme = EXCLUDED.theme,
  language = EXCLUDED.language,
  notifications_enabled = EXCLUDED.notifications_enabled,
  email_notifications = EXCLUDED.email_notifications,
  display_preferences = EXCLUDED.display_preferences,
  export_preferences = EXCLUDED.export_preferences;

-- Insert mock data for wallet_connections
INSERT INTO public.wallet_connections (user_id, wallet_address, wallet_type, chain_id, is_primary)
VALUES
  (auth.uid(), '******************************************', 'MetaMask', 1, true),
  (auth.uid(), '******************************************', 'WalletConnect', 137, false)
ON CONFLICT DO NOTHING;
