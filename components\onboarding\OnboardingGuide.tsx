import { Button } from '@/components/ui/button';
import { LogoWithNotamess, NotamessLogo } from '@/components/ux/icons';
import { FONT_BIRCOLAGE_GROTESQUE } from '@/lib/constants';
import { useUsers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import { ArrowRight, X } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Drawer } from 'vaul';
import { FormsLogo } from '../ux/icons/logo';

export function OnboardingGuide() {
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState(1);
  const totalSteps = 5;
  const router = useRouter();
  const { user, profile } = userStore();
  const { getProfile, updateProfile } = useUsers();

  useEffect(() => {
    // Check if the user is onboarded by checking the database
    const checkOnboardingStatus = async () => {
      if (!user) {
        console.log('No user available for onboarding check');
        return;
      }

      try {
        // Use the getProfile hook to fetch the user's profile
        const profileData = await getProfile();

        // If is_onboarded is false, show the onboarding guide
        if (profileData && profileData.is_onboarded === false) {
          setIsOpen(true);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        toast.error('Failed to check onboarding status');
      }
    };

    // Only run if we have a user
    if (user && user.id) {
      checkOnboardingStatus();
    }
  }, [user, getProfile]);

  const handleComplete = async () => {
    if (!user) return;

    try {
      // Create a promise for updating the profile
      const updatePromise = updateProfile.mutate({ is_onboarded: true });

      // Use toast.promise for better user feedback
      toast.promise(updatePromise, {
        loading: 'Completing onboarding...',
        success: 'Onboarding completed successfully',
        error: 'Failed to complete onboarding',
      });

      // Wait for the update to complete
      await updatePromise;

      // Close the onboarding guide
      setIsOpen(false);
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  };

  const handleSkip = async () => {
    if (!user) return;

    try {
      // Create a promise for updating the profile
      const updatePromise = updateProfile.mutate({ is_onboarded: true });

      // Use toast.promise for better user feedback
      toast.promise(updatePromise, {
        loading: 'Skipping onboarding...',
        success: 'Onboarding skipped',
        error: 'Failed to skip onboarding',
      });

      // Wait for the update to complete
      await updatePromise;

      // Close the onboarding guide
      setIsOpen(false);
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  };

  const handleNext = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={setIsOpen} direction="right">
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 " />
        <Drawer.Content
          className="right-2 md:right-4 top-2 md:top-4 bottom-2 md:bottom-4  left-2 md:left-auto fixed z-50 outline-none  flex  flex-col  md:max-w-[380px]"
          // The gap between the edge of the screen and the drawer is 8px in this case.
          style={
            { '--initial-transform': 'calc(100% + 8px)' } as React.CSSProperties
          }
        >
          <Drawer.Title className="sr-only">Onboarding Guide</Drawer.Title>
          <div className="flex h-full flex-col relative bg-white rounded-[10px]   shadow-xs transition-[color,box-shadow] border border-neutral-200">
            <div className="flex flex-col justify-between">
              <div className="flex items-center justify-between w-full px-2 h-10 border-b border-dashed border-neutral-200">
                <div className="px-2">
                  <p
                    className={cn(
                      FONT_BIRCOLAGE_GROTESQUE.className,
                      'text-sm text-neutral-500 font-medium '
                    )}
                  >
                    Onboarding
                  </p>
                </div>
                <Drawer.Close asChild>
                  <Button
                    variant="shadow_dark"
                    size="icon"
                    className="h-6 w-6 rounded-md"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </Drawer.Close>
              </div>
              <div className=" flex flex-col border-b border-dashed border-neutral-200">
                <div>
                  <FormsLogo className="size-16 px-2" />
                  <p className="px-4 text-base">
                    <LogoWithNotamess />{' '}
                    <span className="text-neutral-500">by </span>
                    <Link
                      href={'https://notamess.com'}
                      className="relative group"
                    >
                      <NotamessLogo />
                      <span className="absolute bottom-0 left-0 h-px w-0 transition-all duration-200 ease-in-out group-hover:w-full rounded-full bg-notamess-100" />
                    </Link>
                  </p>
                </div>
                <div
                  className={cn(
                    FONT_BIRCOLAGE_GROTESQUE.className,
                    'px-4 py-2 space-y-1'
                  )}
                >
                  <Drawer.Title className="font-semibold">
                    Welcome {profile?.full_name}!
                  </Drawer.Title>
                  <Drawer.Description className="text-sm text-neutral-500">
                    Let&apos;s get you started with a quick tour of the platform
                    and the features we offer.
                  </Drawer.Description>
                </div>
              </div>
            </div>

            <div className="mt-8 flex-1 overflow-y-auto">
              {step === 1 && (
                <div className="space-y-6 p-4">
                  <div>
                    <h3 className="font-medium">Create Documents</h3>
                    <p className="text-sm text-muted-foreground">
                      Create legal documents from templates or from scratch.
                    </p>
                  </div>
                </div>
              )}

              {step === 2 && (
                <div className="space-y-6 p-4">
                  <div>
                    <h3 className="font-medium">Manage Templates</h3>
                    <p className="text-sm text-muted-foreground">
                      Use our library of templates or create your own.
                    </p>
                  </div>
                </div>
              )}

              {step === 3 && (
                <div className="space-y-6 p-4">
                  <div>
                    <h3 className="font-medium">Share and Collaborate</h3>
                    <p className="text-sm text-muted-foreground">
                      Share documents with clients and colleagues.
                    </p>
                  </div>
                </div>
              )}

              {step === 4 && (
                <div className="space-y-6 p-4">
                  <div>
                    <h3 className="font-medium">Get Expert Help</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect with legal professionals when needed.
                    </p>
                  </div>
                </div>
              )}

              {step === 5 && (
                <div className="space-y-6 p-4">
                  <h3 className="font-medium text-center">
                    You&apos;re all set!
                  </h3>
                  <p className="text-center">
                    Start by creating your first document or exploring
                    templates.
                  </p>
                  <div className="flex justify-center">
                    <Button
                      onClick={() => {
                        handleComplete();
                        router.push('/documents/new');
                      }}
                      className="mx-auto"
                    >
                      Create Your First Document
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div className="mt-6 flex items-center justify-between border-t p-2">
              <div>
                {step > 1 && (
                  <Button
                    variant="shadow_dark"
                    onClick={handlePrevious}
                    className="rounded-md"
                  >
                    Previous
                  </Button>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-neutral-500">
                  {step} of {totalSteps}
                </span>
                {step < totalSteps ? (
                  <>
                    <Button
                      variant="shadow_accent"
                      className="rounded-md"
                      onClick={handleSkip}
                    >
                      Skip
                    </Button>
                    <Button
                      variant="shadow_dark"
                      className="rounded-md"
                      onClick={handleNext}
                    >
                      Next
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="shadow_accent"
                    className="rounded-md"
                    onClick={handleComplete}
                  >
                    Get Started
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
