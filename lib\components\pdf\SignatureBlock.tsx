import { SignatureMetadata } from '@/lib/services/SignatureService';
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import type { Style } from '@react-pdf/types';

interface SignatureBlockProps {
  metadata: SignatureMetadata;
  style?: Style;
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
    padding: 10,
    borderTop: '1pt solid #999',
  },
  title: {
    fontSize: 10,
    marginBottom: 5,
    color: '#666',
  },
  info: {
    fontSize: 9,
    color: '#333',
    marginBottom: 3,
  },
  certificate: {
    fontSize: 8,
    color: '#666',
    marginTop: 5,
  },
});

export function SignatureBlock({ metadata, style }: SignatureBlockProps) {
  const certificateInfo = metadata.certificateInfo
    ? JSON.parse(metadata.certificateInfo)
    : null;

  return (
    <View style={{ ...styles.container, ...style }}>
      <Text style={styles.title}>
        {metadata.signatureType === 'digital'
          ? 'Digital Signature'
          : 'Electronic Signature'}
      </Text>
      <Text style={styles.info}>Signed by: {metadata.signedBy}</Text>
      <Text style={styles.info}>
        Date: {new Date(metadata.signedAt).toLocaleString()}
      </Text>
      {certificateInfo?.location && (
        <Text style={styles.info}>Location: {certificateInfo.location}</Text>
      )}
      {certificateInfo?.reason && (
        <Text style={styles.info}>Reason: {certificateInfo.reason}</Text>
      )}
      <Text style={styles.certificate}>
        Certificate Valid Until:{' '}
        {new Date(metadata.validUntil!).toLocaleString()}
      </Text>
    </View>
  );
}
