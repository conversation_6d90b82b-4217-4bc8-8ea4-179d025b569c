'use client';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { userStore } from '@/lib/store/user';
import { cn } from '@/lib/utils';
import {
  Activity,
  Calendar,
  ChevronLeft,
  ClipboardCheck,
  MessagesSquare,
  Network,
  Plus,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

interface CollaborationContentProps {
  direction?: number;
}

export default function CollaborationContent({
  direction = 0,
}: CollaborationContentProps) {
  const { profile } = userStore();
  const [collaborationHovered, setCollaborationHovered] = useState(false);
  const pathname = usePathname();

  // Projects group
  const projects = [
    {
      name: 'Collaboration Hub',
      url: `/${profile?.username}/collaboration/hub`,
      icon: Network,
      active:
        pathname === `/${profile?.username}/collaboration/hub` ||
        pathname === `/${profile?.username}/collaboration`,
    },
    {
      name: 'All Projects',
      url: `/${profile?.username}/collaboration/projects`,
      icon: Activity,
      active: pathname.includes(`/${profile?.username}/collaboration/projects`),
    },
    {
      name: 'Project Calendar',
      url: `/${profile?.username}/collaboration/calendar`,
      icon: Calendar,
      active: pathname.includes(`/${profile?.username}/collaboration/calendar`),
    },
  ];

  // Collaboration tools group
  const collaborationTools = [
    {
      name: 'Team Chat',
      url: `/${profile?.username}/collaboration/messages`,
      icon: MessagesSquare,
      active: pathname.includes(`/${profile?.username}/collaboration/messages`),
    },
    {
      name: 'Task Tracking',
      url: `/${profile?.username}/collaboration/tasks`,
      icon: ClipboardCheck,
      active: pathname.includes(`/${profile?.username}/collaboration/tasks`),
    },
    {
      name: 'Collaborators',
      url: `/${profile?.username}/collaboration/collaborators`,
      icon: Users,
      active: pathname.includes(
        `/${profile?.username}/collaboration/collaborators`
      ),
    },
  ];

  // Create Project
  const createProject = [
    {
      name: 'Create New Project',
      url: `/${profile?.username}/collaboration/projects/new`,
      icon: Plus,
      active: pathname.includes(
        `/${profile?.username}/collaboration/projects/new`
      ),
    },
  ];

  return (
    <motion.div
      key="collaboration"
      initial={{
        opacity: 0,
        filter: 'blur(4px)',
        x: direction * 20,
      }}
      animate={{
        opacity: 1,
        filter: 'blur(0px)',
        x: 0,
      }}
      exit={{
        opacity: 0,
        filter: 'blur(4px)',
        x: 20,
      }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
      className="w-full overflow-hidden"
    >
      <div className="py-2 px-4 flex items-center gap-2 border-b h-10 border-dashed border-neutral-300/60">
        <Link
          href={`/${profile?.username}`}
          className={cn(
            'flex items-center gap-1 transition-all duration-200 ease-out',
            collaborationHovered ? 'text-neutral-600' : 'text-neutral-400'
          )}
          onMouseEnter={() => setCollaborationHovered(true)}
          onMouseLeave={() => setCollaborationHovered(false)}
        >
          <motion.span
            initial={{ x: 0 }}
            animate={{ x: collaborationHovered ? -3 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronLeft className="size-4" />
          </motion.span>
          <span className="text-sm font-medium uppercase">
            Collaboration Hub
          </span>
        </Link>
      </div>
      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Projects
        </SidebarGroupLabel>
        <SidebarMenu>
          {projects.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Tools
        </SidebarGroupLabel>
        <SidebarMenu>
          {collaborationTools.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      <SidebarGroup className="group-data-[collapsible=icon]:hidden mt-4">
        <SidebarGroupLabel className="text-neutral-700 font-medium uppercase text-xs">
          Actions
        </SidebarGroupLabel>
        <SidebarMenu>
          {createProject.map((item) => (
            <SidebarMenuItem
              key={item.name}
              className={cn(
                'w-full hover:bg-accent-10/10 hover:text-accent-100 text-neutral-500 hover:border-accent-10/20 rounded-lg border border-transparent transition-all duration-200 ease-out',
                item.active
                  ? 'bg-accent-10/20 text-accent-300 border-accent-10/30'
                  : ''
              )}
            >
              <SidebarMenuButton asChild>
                <Link href={item.url}>
                  <item.icon className="size-4" />
                  <span>{item.name}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>
    </motion.div>
  );
}
