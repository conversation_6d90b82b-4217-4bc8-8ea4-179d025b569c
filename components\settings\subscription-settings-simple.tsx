'use client';

import { useState } from 'react';
import { useSettings } from '@/lib/hooks';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { CreditCard, Loader2 } from 'lucide-react';

interface SubscriptionSettingsProps {
  subscription?: any;
  availablePlans?: any[];
}

export function SubscriptionSettings({
  subscription,
  availablePlans,
}: SubscriptionSettingsProps) {
  const { loading } = useSettings();
  const [isUpgrading, setIsUpgrading] = useState(false);

  const handleUpgrade = async () => {
    setIsUpgrading(true);

    const upgradePromise = new Promise((resolve) => {
      setTimeout(resolve, 2000); // Simulate API call
    });

    toast.promise(upgradePromise, {
      loading: 'Upgrading subscription...',
      success: 'Subscription upgraded successfully!',
      error: 'Failed to upgrade subscription',
    });

    try {
      await upgradePromise;
    } catch (error) {
      console.error('Error upgrading subscription:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-24 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Current Plan</CardTitle>
          <CardDescription>
            Your current subscription plan and billing details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-neutral-50 border rounded-lg p-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold capitalize">
                  {subscription?.plan || 'Free'} Plan
                </h3>
                <p className="text-sm text-neutral-500">
                  Basic features for personal use
                </p>
              </div>
              <div className="mt-2 sm:mt-0">
                <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                  Active
                </span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mb-4">
              <div className="flex-1">
                <p className="text-sm font-medium mb-1">Started On</p>
                <p className="text-sm">
                  {subscription?.start_date
                    ? new Date(subscription.start_date).toLocaleDateString()
                    : 'N/A'}
                </p>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium mb-1">Billing</p>
                <p className="text-sm">Free</p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-neutral-500" />
                <span className="text-sm">No payment method on file</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Upgrade Plan</CardTitle>
          <CardDescription>
            Get access to more features and higher limits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">Standard Plan</h4>
              <p className="text-sm text-blue-700 mb-3">
                Advanced features for professionals
              </p>
              <div className="text-2xl font-bold text-blue-800 mb-4">
                $19.99/month
              </div>
              <Button
                onClick={handleUpgrade}
                disabled={isUpgrading}
                className="w-full"
              >
                {isUpgrading && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Upgrade to Standard
              </Button>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="font-medium text-purple-800 mb-2">
                Enterprise Plan
              </h4>
              <p className="text-sm text-purple-700 mb-3">
                Complete solution for businesses
              </p>
              <div className="text-2xl font-bold text-purple-800 mb-4">
                Contact Sales
              </div>
              <Button
                variant="outline"
                className="w-full"
                onClick={() =>
                  toast.info(
                    'Please contact our sales team for enterprise pricing'
                  )
                }
              >
                Contact Sales
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
