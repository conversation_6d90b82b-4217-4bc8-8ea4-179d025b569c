import { COUNTRIES } from '@/lib/constants';

// Zod
import z from 'zod';

import { InvoiceSchema, ItemSchema } from '@/lib/constants/schemas';
import { FieldPath, UseFormReturn } from 'react-hook-form';

// Prop Types

export type pageProps = {
  params: Promise<{ username: string }>;
  searchParams: Promise<{ [key: string]: string }>;
};

// Zod schemas

export type addressType = {
  street: string;
  city: string;
  country: typeof COUNTRIES;
};

// Form types
export type InvoiceType = z.infer<typeof InvoiceSchema>;
export type ItemType = z.infer<typeof ItemSchema>;
export type FormType = UseFormReturn<InvoiceType>;
export type NameType = FieldPath<InvoiceType>;
export type CurrencyType = {
  [currencyCode: string]: string;
};

// Signature types
export type SignatureColor = {
  name: string;
  label: string;
  color: string;
};

export type SignatureFont = {
  name: string;
  variable: string;
};

export enum SignatureTabs {
  DRAW = 'draw',
  TYPE = 'type',
  UPLOAD = 'upload',
}

// Wizard types
export type WizardStepType = {
  id: number;
  label: string;
  isValid?: boolean;
};

// Export types
export enum ExportTypes {
  JSON = 'JSON',
  CSV = 'CSV',
  XML = 'XML',
  XLSX = 'XLSX',
  DOCX = 'DOCX',
}

// Export enterprise types
export * from './enterprise';
