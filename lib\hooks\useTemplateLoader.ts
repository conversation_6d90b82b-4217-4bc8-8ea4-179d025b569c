'use client';

import { useCallback, useState } from 'react';
import { TemplateContext } from '../services/templateService';

export interface UseTemplateLoaderResult {
  loading: boolean;
  error: string | null;
  template: TemplateContext | null;
  loadTemplate: (
    jurisdiction: string,
    formType: string,
    category: string,
    version?: string
  ) => Promise<TemplateContext | null>;
}

/**
 * Hook for loading form templates from the API
 * This can be imported and used in client components
 */
export function useTemplateLoader(): UseTemplateLoaderResult {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [template, setTemplate] = useState<TemplateContext | null>(null);

  const loadTemplate = useCallback(
    async (
      jurisdiction: string,
      formType: string,
      category: string,
      version?: string
    ): Promise<TemplateContext | null> => {
      setLoading(true);
      setError(null);

      try {
        // Build URL with query parameters
        const url = new URL('/api/templates/load', window.location.origin);
        url.searchParams.set('jurisdiction', jurisdiction);
        url.searchParams.set('formType', formType);
        url.searchParams.set('category', category);
        if (version) {
          url.searchParams.set('version', version);
        }

        const response = await fetch(url.toString());

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to load template');
        }

        const data = await response.json();
        setTemplate(data);
        return data;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error loading template';
        setError(errorMessage);
        console.error('Template loading error:', err);
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return { loading, error, template, loadTemplate };
}

/**
 * Server-side function to load templates
 * This can be imported and used in server components or API routes
 */
export async function loadTemplateFromServer(
  jurisdiction: string,
  formType: string,
  category: string,
  version?: string
): Promise<TemplateContext> {
  const url = new URL(
    `/api/templates/load`,
    process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  );

  url.searchParams.set('jurisdiction', jurisdiction);
  url.searchParams.set('formType', formType);
  url.searchParams.set('category', category);
  if (version) {
    url.searchParams.set('version', version);
  }

  const response = await fetch(url.toString(), { next: { revalidate: 3600 } });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error ||
        `Template not found for ${jurisdiction}/${category}/${formType}${
          version ? ` version ${version}` : ''
        }`
    );
  }

  return response.json();
}
