'use client';

import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Field } from '@/lib/constants/schemas/template';

interface FormFieldProps {
  field: Field;
  value: unknown;
  onChange: (value: unknown) => void;
}

export function FormField({ field, value, onChange }: FormFieldProps) {
  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            value={(value as string) || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );
      case 'textarea':
        return (
          <Textarea
            value={(value as string) || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );
      case 'number':
        return (
          <Input
            type="number"
            value={(value as number) || ''}
            onChange={(e) => onChange(Number(e.target.value))}
            required={field.required}
            min={field.validation?.min}
            max={field.validation?.max}
            step="any"
          />
        );
      case 'date':
        return (
          <Input
            type="date"
            value={(value as string) || ''}
            onChange={(e) => onChange(e.target.value)}
            required={field.required}
          />
        );
      case 'boolean':
        return (
          <Checkbox
            checked={(value as boolean) || false}
            onCheckedChange={onChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <FormItem>
      <div className="flex items-center gap-2">
        {field.type === 'boolean' ? (
          <>
            <FormControl>{renderField()}</FormControl>
            <FormLabel>{field.label}</FormLabel>
          </>
        ) : (
          <>
            <FormLabel>{field.label}</FormLabel>
            <FormControl>{renderField()}</FormControl>
          </>
        )}
      </div>
      {field.description && (
        <FormDescription>{field.description}</FormDescription>
      )}
      <FormMessage />
    </FormItem>
  );
}
