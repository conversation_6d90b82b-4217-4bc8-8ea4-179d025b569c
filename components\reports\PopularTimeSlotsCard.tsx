'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { TimeSlotData } from '@/lib/types/database-modules';
import { AlertCircle, Clock } from 'lucide-react';

interface PopularTimeSlotsCardProps {
  data: TimeSlotData[] | null;
  loading?: boolean;
}

export function PopularTimeSlotsCard({
  data,
  loading = false,
}: PopularTimeSlotsCardProps) {
  // Format day of week
  const formatDayOfWeek = (day: number) => {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    return days[day];
  };

  // Format hour
  const formatHour = (hour: number) => {
    if (hour === 0) return '12 AM';
    if (hour === 12) return '12 PM';
    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between py-2 border-b"
            >
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Popular Time Slots</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No time slot data available</p>
        </CardContent>
      </Card>
    );
  }

  // Calculate the maximum count for the progress bars
  const maxCount = Math.max(...data.map((slot) => slot.consultation_count));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Popular Time Slots</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((slot, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">
                  {formatDayOfWeek(slot.day_of_week)}, {formatHour(slot.hour)}
                </span>
              </div>
              <span className="text-sm font-medium">
                {slot.consultation_count} consultations
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
              <div
                className="bg-primary h-full rounded-full"
                style={{
                  width: `${(slot.consultation_count / maxCount) * 100}%`,
                }}
              ></div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
