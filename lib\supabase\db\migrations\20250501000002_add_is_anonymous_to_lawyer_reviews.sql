-- Migration to add is_anonymous column to lawyer_reviews table
-- This migration is idempotent and can be run multiple times

-- Add is_anonymous column if it doesn't exist
ALTER TABLE public.lawyer_reviews
ADD COLUMN IF NOT EXISTS is_anonymous BOOLEAN DEFAULT false;

-- Add comment to explain the column
COMMENT ON COLUMN public.lawyer_reviews.is_anonymous IS 'Whether the review is anonymous or not';
