import '@/components/ui/sonner';
import { toast } from 'sonner';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationOptions {
  type?: NotificationType;
  duration?: number;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  position?:
    | 'top-right'
    | 'top-left'
    | 'bottom-right'
    | 'bottom-left'
    | 'top-center'
    | 'bottom-center';
  closeButton?: boolean;
}

export class NotificationService {
  private static instance: NotificationService;
  private retryCallback?: () => Promise<void>;

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  setRetryCallback(callback: () => Promise<void>): void {
    this.retryCallback = callback;
  }

  notify(message: string, options: NotificationOptions = {}): void {
    const { type = 'info', duration = 3000, action } = options;

    const toastOptions = {
      duration,
      ...(action && {
        action: {
          label: action.label,
          onClick: action.onClick,
        },
      }),
    };

    switch (type) {
      case 'success':
        toast.success(message, toastOptions);
        break;
      case 'error':
        toast.error(message, toastOptions);
        break;
      case 'warning':
        toast.warning(message, toastOptions);
        break;
      case 'info':
      default:
        toast(message, toastOptions);
    }
  }

  // Form state specific notifications
  formStateSaved(): void {
    this.notify('Form progress saved', { type: 'success', duration: 2000 });
  }

  formStateRestored(): void {
    this.notify('Form progress restored', { type: 'info' });
  }

  formStateSaveError(error: Error): void {
    this.notify('Failed to save form progress', {
      type: 'error',
      duration: 5000,
      action: {
        label: 'Retry',
        onClick: async () => {
          if (this.retryCallback) {
            try {
              await this.retryCallback();
              this.formStateSaved();
            } catch (retryError) {
              console.error('Retry failed:', retryError);
              this.notify('Retry failed', {
                type: 'error',
                duration: 5000,
              });
            }
          }
        },
      },
    });
  }

  formStateLoadError(): void {
    this.notify('Failed to load form progress', {
      type: 'error',
      duration: 5000,
    });
  }

  formStateConflict(): void {
    toast.warning('Server changes detected. Loading latest version.');
  }

  offlineMode(): void {
    toast.warning('You are offline. Changes will be synced when back online.');
  }

  onlineSync(): void {
    toast.success('Back online. Syncing changes...');
  }

  syncComplete(): void {
    toast.success('All changes synced successfully');
  }

  syncError(): void {
    toast.error('Some changes failed to sync', {
      action: {
        label: 'Retry',
        onClick: () => this.retryCallback?.(),
      },
    });
  }

  saveSuccess(): void {
    toast.success('Form state saved successfully');
  }

  saveError(): void {
    toast.error('Failed to save form state');
  }

  loadError(): void {
    toast.error('Failed to load form state');
  }

  clearSuccess(): void {
    toast.success('Form state cleared successfully');
  }

  clearError(): void {
    toast.error('Failed to clear form state');
  }

  offlineSave(): void {
    toast.info('Changes saved locally. Will sync when online.');
  }

  /**
   * Show a success notification
   */
  public showSuccess(message: string, options: NotificationOptions = {}): void {
    toast.success(message, {
      duration: options.duration || 5000,
      description: options.description,
      action: options.action,
      position: options.position || 'bottom-right',
      closeButton: options.closeButton || true,
    });
  }

  /**
   * Show an error notification
   */
  public showError(message: string, options: NotificationOptions = {}): void {
    toast.error(message, {
      duration: options.duration || 7000,
      description: options.description,
      action: options.action,
      position: options.position || 'bottom-right',
      closeButton: options.closeButton || true,
    });
  }

  /**
   * Show a warning notification
   */
  public showWarning(message: string, options: NotificationOptions = {}): void {
    toast.warning(message, {
      duration: options.duration || 6000,
      description: options.description,
      action: options.action,
      position: options.position || 'bottom-right',
      closeButton: options.closeButton || true,
    });
  }

  /**
   * Show an info notification
   */
  public showInfo(message: string, options: NotificationOptions = {}): void {
    toast.info(message, {
      duration: options.duration || 5000,
      description: options.description,
      action: options.action,
      position: options.position || 'bottom-right',
      closeButton: options.closeButton || true,
    });
  }

  /**
   * Show a loading notification that can be updated
   */
  public showLoading(message: string): {
    update: (message: string) => void;
    success: (message: string) => void;
    error: (message: string) => void;
  } {
    const toastId = toast.loading(message, {
      position: 'bottom-right',
    });

    return {
      update: (message: string) => {
        toast.loading(message, { id: toastId });
      },
      success: (message: string) => {
        toast.success(message, { id: toastId });
      },
      error: (message: string) => {
        toast.error(message, { id: toastId });
      },
    };
  }

  /**
   * Show a custom notification
   */
  public showCustom(
    message: string,
    options: NotificationOptions & { icon?: React.ReactNode } = {}
  ): void {
    toast(message, {
      duration: options.duration || 5000,
      description: options.description,
      action: options.action,
      position: options.position || 'bottom-right',
      closeButton: options.closeButton || true,
      icon: options.icon,
    });
  }
}
