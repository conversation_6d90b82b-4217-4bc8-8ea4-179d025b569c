'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { UserAvatar } from '@/components/ui/user-avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ConsultationCalendarSync } from '@/components/lawyer/ConsultationCalendarSync';
import { ConsultationTimeSlots } from '@/components/lawyer/ConsultationTimeSlots';
import { RecurringConsultationForm } from '@/components/lawyer/RecurringConsultationForm';
import { MobileConsultationBooking } from '@/components/lawyer/MobileConsultationBooking';
import { useLawyers } from '@/lib/hooks';
import { userStore } from '@/lib/store/user';
import { Lawyer } from '@/lib/types/database-modules';
import { format } from 'date-fns';
import {
  Calendar,
  Clock,
  FileText,
  Video,
  Briefcase,
  CheckCircle,
  Loader2,
  Star,
  Repeat,
} from 'lucide-react';
import { toast } from 'sonner';

interface EnhancedConsultationBookingProps {
  lawyerId?: string;
  clientId?: string;
  documentId?: string;
}

export function EnhancedConsultationBooking({
  lawyerId,
  clientId,
  documentId,
}: EnhancedConsultationBookingProps) {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;

  const { loading, lawyers, fetchAllLawyers, getById, createConsultation } =
    useLawyers();

  const [selectedLawyer, setSelectedLawyer] = useState<Lawyer | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
  const [selectedDuration, setSelectedDuration] = useState<number>(60);
  const [consultationType, setConsultationType] = useState<
    'video' | 'document'
  >('video');
  const [notes, setNotes] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('select-lawyer');
  const [result, setResult] = useState<any>(null);
  const [showRecurringForm, setShowRecurringForm] = useState<boolean>(false);
  const [isMobile, setIsMobile] = useState<boolean>(false);

  // Check if the device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIsMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIsMobile);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, []);

  // Fetch lawyers on component mount
  useEffect(() => {
    if (lawyerId) {
      getById(lawyerId).then((lawyer) => {
        if (lawyer) {
          setSelectedLawyer(lawyer);
          setActiveTab('select-time');
        }
      });
    } else {
      fetchAllLawyers();
    }
  }, [fetchAllLawyers, getById, lawyerId]);

  // Calculate consultation fee based on duration and lawyer's hourly rate
  const calculateFee = () => {
    if (!selectedLawyer) return 0;

    const hourlyRate = selectedLawyer.consultation_fee || 100;
    return (hourlyRate / 60) * selectedDuration;
  };

  // Handle lawyer selection
  const handleSelectLawyer = (lawyer: Lawyer) => {
    setSelectedLawyer(lawyer);
    setActiveTab('select-time');

    // Set default duration to the first available option
    if (
      lawyer.consultation_duration_options &&
      lawyer.consultation_duration_options.length > 0
    ) {
      setSelectedDuration(lawyer.consultation_duration_options[0]);
    }
  };

  // Handle duration selection
  const handleSelectDuration = (duration: number) => {
    setSelectedDuration(duration);
    setSelectedTimeSlot(null); // Reset time slot when duration changes
  };

  // Handle consultation booking
  const handleBookConsultation = async () => {
    if (!selectedLawyer || !selectedTimeSlot) {
      toast.error('Please select a lawyer and time slot');
      return;
    }

    setIsSubmitting(true);
    try {
      const consultationDate = new Date(selectedTimeSlot);

      // Create a booking promise function
      const bookingPromise = async () => {
        return await createConsultation(
          selectedLawyer.id,
          consultationType === 'document' ? documentId || null : null,
          consultationDate.toISOString(),
          selectedDuration,
          notes || null,
          consultationType === 'video' ? 'video' : 'message' // Use 'message' instead of 'document'
        );
      };

      // Use toast.promise for better user feedback
      toast.promise(bookingPromise(), {
        loading: 'Scheduling consultation...',
        success: 'Consultation scheduled successfully',
        error: 'Failed to schedule consultation',
      });

      // Execute the promise and get the result
      const result = await bookingPromise();

      if (result) {
        setResult(result);
        setIsSuccess(true);

        // Reset form after a delay
        setTimeout(() => {
          setSelectedLawyer(null);
          setSelectedDate(new Date());
          setSelectedTimeSlot(null);
          setNotes('');
          setIsSuccess(false);
          setActiveTab('select-lawyer');
          setResult(null);

          // Redirect to consultations page
          router.push(`/${username}/lawyer/consultations`);
        }, 3000);
      }
    } catch (error) {
      console.error('Error scheduling consultation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success view
  if (isSuccess) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="flex flex-col items-center justify-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
            <h2 className="text-2xl font-bold mb-2">Consultation Scheduled</h2>
            <p className="text-muted-foreground mb-6 max-w-md">
              Your consultation has been scheduled successfully. You will
              receive a confirmation email shortly.
            </p>
            <div className="bg-muted p-4 rounded-lg mb-6 w-full max-w-md">
              <div className="flex items-center gap-4 mb-4">
                <UserAvatar
                  avatarUrl={selectedLawyer?.avatar_url || null}
                  fallbackText={selectedLawyer?.full_name || 'Lawyer'}
                  className="h-12 w-12"
                />
                <div>
                  <h3 className="font-medium">{selectedLawyer?.full_name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedLawyer?.specialization}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>
                  {format(new Date(selectedTimeSlot!), 'EEEE, MMMM d, yyyy')}
                </span>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>
                  {format(new Date(selectedTimeSlot!), 'h:mm a')} (
                  {selectedDuration} minutes)
                </span>
              </div>
              <div className="flex items-center gap-2">
                {consultationType === 'video' ? (
                  <Video className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <FileText className="h-4 w-4 text-muted-foreground" />
                )}
                <span>
                  {consultationType === 'video'
                    ? 'Video Consultation'
                    : 'Document Review'}
                </span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <ConsultationCalendarSync
                consultationId={result.id}
                isNewConsultation={true}
              />
              <Button
                onClick={() => router.push(`/${username}/lawyer/consultations`)}
              >
                View My Consultations
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If mobile, render the mobile version
  if (isMobile) {
    return (
      <MobileConsultationBooking
        lawyerId={lawyerId}
        documentId={documentId}
        clientId={clientId}
        onSuccess={(result) => {
          setResult(result);
          setIsSuccess(true);
        }}
      />
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Schedule a Consultation</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="select-lawyer" disabled={!!lawyerId}>
                Select Lawyer
              </TabsTrigger>
              <TabsTrigger value="select-time" disabled={!selectedLawyer}>
                Select Time
              </TabsTrigger>
              <TabsTrigger
                value="consultation-details"
                disabled={!selectedTimeSlot}
              >
                Details
              </TabsTrigger>
              <TabsTrigger value="review-booking" disabled={!notes}>
                Review & Book
              </TabsTrigger>
            </TabsList>

            {/* Step 1: Select Lawyer */}
            <TabsContent value="select-lawyer">
              {loading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          <Skeleton className="h-16 w-16 rounded-full" />
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-40" />
                            <Skeleton className="h-3 w-32" />
                            <Skeleton className="h-3 w-24" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : lawyers.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Briefcase className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">
                      No Lawyers Available
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      There are no lawyers available for consultation at the
                      moment.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {lawyers.map((lawyer) => (
                    <Card
                      key={lawyer.id}
                      className="overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => handleSelectLawyer(lawyer)}
                    >
                      <CardContent className="p-6">
                        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                          <div className="flex items-center gap-4">
                            <UserAvatar
                              avatarUrl={lawyer.avatar_url || null}
                              fallbackText={lawyer.full_name}
                              className="h-16 w-16"
                            />
                            <div>
                              <h3 className="font-medium">
                                {lawyer.full_name}
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                {lawyer.specialization}
                              </p>
                              <div className="flex items-center gap-4 mt-1">
                                <span className="text-xs flex items-center gap-1">
                                  <Clock className="h-3 w-3" />$
                                  {lawyer.consultation_fee}/hour
                                </span>
                                <span className="text-xs flex items-center gap-1">
                                  <Star className="h-3 w-3" />
                                  {lawyer.average_rating
                                    ? lawyer.average_rating.toFixed(1)
                                    : 'N/A'}{' '}
                                  ({lawyer.consultation_count || 0}{' '}
                                  consultations)
                                </span>
                              </div>
                            </div>
                          </div>
                          <Button>Select</Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            {/* Step 2: Select Time */}
            <TabsContent value="select-time">
              {selectedLawyer && (
                <div className="space-y-6">
                  <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-6">
                    <div className="flex items-center gap-4">
                      <UserAvatar
                        avatarUrl={selectedLawyer.avatar_url || null}
                        fallbackText={selectedLawyer.full_name}
                        className="h-12 w-12"
                      />
                      <div>
                        <h3 className="font-medium">
                          {selectedLawyer.full_name}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {selectedLawyer.specialization}
                        </p>
                      </div>
                    </div>
                    {!lawyerId && (
                      <Button
                        variant="outline"
                        onClick={() => setActiveTab('select-lawyer')}
                      >
                        Change Lawyer
                      </Button>
                    )}
                  </div>

                  <div className="mb-6">
                    <Label className="text-base font-medium mb-2 block">
                      Select Duration
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {(
                        selectedLawyer.consultation_duration_options || [30, 60]
                      ).map((duration) => (
                        <Button
                          key={duration}
                          type="button"
                          variant={
                            selectedDuration === duration
                              ? 'default'
                              : 'outline'
                          }
                          onClick={() => handleSelectDuration(duration)}
                          className="h-10"
                        >
                          {duration} min ($
                          {(
                            ((selectedLawyer.consultation_fee || 100) / 60) *
                            duration
                          ).toFixed(2)}
                          )
                        </Button>
                      ))}
                    </div>
                  </div>

                  <ConsultationTimeSlots
                    lawyerId={selectedLawyer.id}
                    selectedDate={selectedDate}
                    onDateChange={setSelectedDate}
                    selectedTimeSlot={selectedTimeSlot}
                    onTimeSlotChange={setSelectedTimeSlot}
                    durationMinutes={selectedDuration}
                  />

                  <div className="flex justify-end mt-6">
                    <Button
                      onClick={() => setActiveTab('consultation-details')}
                      disabled={!selectedTimeSlot}
                    >
                      Next: Consultation Details
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Step 3: Consultation Details */}
            <TabsContent value="consultation-details">
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-6">
                  <div className="flex items-center gap-4">
                    <UserAvatar
                      avatarUrl={selectedLawyer?.avatar_url || null}
                      fallbackText={selectedLawyer?.full_name || 'Lawyer'}
                      className="h-12 w-12"
                    />
                    <div>
                      <h3 className="font-medium">
                        {selectedLawyer?.full_name}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {selectedLawyer?.specialization}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {selectedTimeSlot &&
                        format(
                          new Date(selectedTimeSlot),
                          'EEEE, MMMM d, yyyy'
                        )}
                    </span>
                    <span className="text-muted-foreground">at</span>
                    <span className="font-medium">
                      {selectedTimeSlot &&
                        format(new Date(selectedTimeSlot), 'h:mm a')}
                    </span>
                    <span className="text-muted-foreground">
                      ({selectedDuration} min)
                    </span>
                  </div>
                </div>

                <div className="mb-6">
                  <Label className="text-base font-medium mb-2 block">
                    Consultation Type
                  </Label>
                  <RadioGroup
                    value={consultationType}
                    onValueChange={(value) =>
                      setConsultationType(value as 'video' | 'document')
                    }
                    className="flex flex-col space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="video" id="video" />
                      <Label
                        htmlFor="video"
                        className="flex items-center gap-2"
                      >
                        <Video className="h-4 w-4" />
                        Video Consultation
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem
                        value="document"
                        id="document"
                        disabled={!documentId}
                      />
                      <Label
                        htmlFor="document"
                        className={`flex items-center gap-2 ${!documentId ? 'text-muted-foreground' : ''}`}
                      >
                        <FileText className="h-4 w-4" />
                        Document Review
                        {!documentId && (
                          <span className="text-xs">
                            (No document selected)
                          </span>
                        )}
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="mb-6">
                  <Label
                    htmlFor="notes"
                    className="text-base font-medium mb-2 block"
                  >
                    Consultation Notes
                  </Label>
                  <Textarea
                    id="notes"
                    placeholder="Describe what you'd like to discuss in this consultation..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="min-h-[120px]"
                  />
                </div>

                <div className="flex justify-between mt-6">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('select-time')}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={() => setActiveTab('review-booking')}
                    disabled={!notes}
                  >
                    Next: Review & Book
                  </Button>
                </div>
              </div>
            </TabsContent>

            {/* Step 4: Review & Book */}
            <TabsContent value="review-booking">
              <div className="space-y-6">
                <div className="bg-muted p-6 rounded-lg">
                  <h3 className="text-lg font-medium mb-4">
                    Consultation Summary
                  </h3>

                  <div className="flex items-center gap-4 mb-4">
                    <UserAvatar
                      avatarUrl={selectedLawyer?.avatar_url || null}
                      fallbackText={selectedLawyer?.full_name || 'Lawyer'}
                      className="h-12 w-12"
                    />
                    <div>
                      <h4 className="font-medium">
                        {selectedLawyer?.full_name}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {selectedLawyer?.specialization}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {selectedTimeSlot &&
                          format(
                            new Date(selectedTimeSlot),
                            'EEEE, MMMM d, yyyy'
                          )}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {selectedTimeSlot &&
                          format(new Date(selectedTimeSlot), 'h:mm a')}{' '}
                        ({selectedDuration} minutes)
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      {consultationType === 'video' ? (
                        <Video className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <FileText className="h-4 w-4 text-muted-foreground" />
                      )}
                      <span>
                        {consultationType === 'video'
                          ? 'Video Consultation'
                          : 'Document Review'}
                      </span>
                    </div>
                  </div>

                  <div className="border-t pt-4 mb-4">
                    <h4 className="font-medium mb-2">Consultation Notes</h4>
                    <p className="text-sm whitespace-pre-wrap">{notes}</p>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center">
                      <span className="font-medium">Consultation Fee</span>
                      <span className="font-bold">
                        ${calculateFee().toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-center mt-4 mb-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowRecurringForm(true)}
                    className="w-full"
                  >
                    <Repeat className="h-4 w-4 mr-2" />
                    Make This a Recurring Consultation
                  </Button>
                </div>

                <div className="flex justify-between mt-6">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('consultation-details')}
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleBookConsultation}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Booking...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Confirm Booking
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Recurring Consultation Form Dialog */}
      <Dialog open={showRecurringForm} onOpenChange={setShowRecurringForm}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create Recurring Consultation</DialogTitle>
          </DialogHeader>

          {selectedLawyer && (
            <RecurringConsultationForm
              lawyerId={selectedLawyer.id}
              clientId={clientId}
              documentId={
                consultationType === 'document' ? documentId : undefined
              }
              onSuccess={() => {
                setShowRecurringForm(false);
                toast.success('Recurring consultation created successfully');
                router.push(`/${username}/lawyer/recurring-consultations`);
              }}
              onCancel={() => setShowRecurringForm(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
