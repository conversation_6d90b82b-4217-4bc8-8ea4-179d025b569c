'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useDocuments, useLawyerAvailability, useLawyers } from '@/lib/hooks';
import { cn } from '@/lib/utils';

// Define a local TimeSlot interface that matches what the hook returns
interface TimeSlot {
  start: string;
  end: string;
}
import { zodResolver } from '@hookform/resolvers/zod';
import { format, startOfDay } from 'date-fns';
import { CalendarIcon, Clock, FileText, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Drawer } from 'vaul';
import * as z from 'zod';

const formSchema = z.object({
  lawyerId: z.string({
    required_error: 'Please select a lawyer',
  }),
  documentId: z.string().optional(),
  consultationDate: z.date({
    required_error: 'Please select a date for the consultation',
  }),
  timeSlot: z.string({
    required_error: 'Please select a time slot',
  }),
  durationType: z.enum(['30', '60', '90'], {
    required_error: 'Please select a duration',
  }),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface CalendarBookingProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  defaultLawyerId?: string;
  defaultDocumentId?: string;
}

export function CalendarBooking({
  isOpen,
  onClose,
  onSuccess,
  defaultLawyerId,
  defaultDocumentId,
}: CalendarBookingProps) {
  const {
    lawyers,
    loading: lawyersLoading,
    fetchAllLawyers,
    scheduleLawyerConsultation,
  } = useLawyers();
  const {
    documents,
    loading: documentsLoading,
    fetchDocuments,
  } = useDocuments();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<TimeSlot[]>([]);
  const [isLoadingTimeSlots, setIsLoadingTimeSlots] = useState(false);

  const { getAvailableTimeSlots } = useLawyerAvailability(undefined);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      lawyerId: defaultLawyerId || '',
      documentId: defaultDocumentId || '',
      durationType: '60',
      notes: '',
    },
  });

  const watchLawyerId = form.watch('lawyerId');
  const watchConsultationDate = form.watch('consultationDate');
  const watchDurationType = form.watch('durationType');

  useEffect(() => {
    if (isOpen) {
      // Fetch lawyers and documents when the dialog opens
      const fetchData = async () => {
        try {
          // Create promises for fetching data
          const lawyersPromise = fetchAllLawyers();
          const documentsPromise = fetchDocuments();

          // Execute both promises in parallel
          await Promise.all([lawyersPromise, documentsPromise]);

          // Set default values if provided
          if (defaultLawyerId) {
            form.setValue('lawyerId', defaultLawyerId);
          }
          if (defaultDocumentId) {
            form.setValue('documentId', defaultDocumentId);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
          toast.error('Failed to load required data');
        }
      };

      fetchData();
    }
  }, [
    isOpen,
    fetchAllLawyers,
    fetchDocuments,
    form,
    defaultLawyerId,
    defaultDocumentId,
  ]);

  // Fetch available time slots when lawyer or date changes
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!watchLawyerId || !watchConsultationDate) return;

      setIsLoadingTimeSlots(true);
      try {
        const durationMinutes = parseInt(watchDurationType, 10);

        // Convert string date to Date object
        const dateObj = new Date(watchConsultationDate);

        // Fetch time slots without toast.promise to avoid type issues
        const slots = await getAvailableTimeSlots(
          dateObj,
          durationMinutes,
          watchLawyerId
        );

        // Show success toast after setting state
        toast.success('Time slots loaded');

        setAvailableTimeSlots(slots);

        // Clear the selected time slot if it's no longer available
        const currentTimeSlot = form.getValues('timeSlot');
        if (
          currentTimeSlot &&
          !slots.some(
            (slot: { start: string; end: string }) =>
              format(new Date(slot.start), 'HH:mm') === currentTimeSlot
          )
        ) {
          form.setValue('timeSlot', '');
        }
      } catch (error) {
        console.error('Error fetching time slots:', error);
        // Error is already handled by toast.promise
      } finally {
        setIsLoadingTimeSlots(false);
      }
    };

    fetchTimeSlots();
  }, [
    watchLawyerId,
    watchConsultationDate,
    watchDurationType,
    getAvailableTimeSlots,
    form,
  ]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      // Combine date and time
      const [hours, minutes] = data.timeSlot.split(':').map(Number);
      const consultationDateTime = new Date(data.consultationDate);
      consultationDateTime.setHours(hours, minutes, 0, 0);

      // Get the duration in minutes
      const durationMinutes = parseInt(data.durationType, 10);

      // Create a promise for the consultation scheduling
      const consultationPromise = scheduleLawyerConsultation(
        data.lawyerId,
        data.documentId === 'none' ? null : data.documentId || null,
        consultationDateTime.toISOString(),
        durationMinutes,
        data.notes
      );

      // Use toast.promise with the consultation promise
      toast.promise(consultationPromise, {
        loading: 'Scheduling consultation...',
        success: `Your consultation has been scheduled for ${format(consultationDateTime, 'MMMM d, yyyy')} at ${format(consultationDateTime, 'h:mm a')}`,
        error: 'Failed to schedule consultation',
      });

      // Await the result separately
      const result = await consultationPromise;

      if (result) {
        // Reset form
        form.reset();

        // Close dialog
        onClose();

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('Error scheduling consultation:', error);
      // Error is already handled by toast.promise
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to format time slots for display
  const formatTimeSlot = (isoString: string) => {
    return format(new Date(isoString), 'h:mm a');
  };

  // Disable past dates
  const disabledDays = {
    before: startOfDay(new Date()),
  };

  return (
    <Drawer.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Drawer.Content className="sm:max-w-[550px] overflow-y-auto">
        <Drawer.Title className="sr-only">Schedule a Consultation</Drawer.Title>
        <div className="p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold">Schedule a Consultation</h2>
            <p className="text-sm text-muted-foreground">
              Book a time with a lawyer to discuss your legal needs.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="lawyerId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Lawyer</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Clear the time slot when lawyer changes
                        form.setValue('timeSlot', '');
                      }}
                      defaultValue={field.value}
                      disabled={lawyersLoading || isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a lawyer" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {lawyersLoading ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading lawyers...</span>
                          </div>
                        ) : lawyers.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No lawyers available
                          </div>
                        ) : (
                          lawyers.map((lawyer) => (
                            <SelectItem key={lawyer.id} value={lawyer.id}>
                              {lawyer.full_name || lawyer.email}
                              {lawyer.specialization &&
                                ` (${lawyer.specialization})`}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="consultationDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                'w-full pl-3 text-left font-normal',
                                !field.value && 'text-muted-foreground'
                              )}
                              disabled={!watchLawyerId || isSubmitting}
                            >
                              {field.value ? (
                                format(field.value, 'MMMM d, yyyy')
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              field.onChange(date);
                              // Clear the time slot when date changes
                              form.setValue('timeSlot', '');
                            }}
                            disabled={disabledDays}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        Select a date for your consultation
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="durationType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          // Clear the time slot when duration changes
                          form.setValue('timeSlot', '');
                        }}
                        defaultValue={field.value}
                        disabled={isSubmitting}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select duration" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="30">30 minutes</SelectItem>
                          <SelectItem value="60">1 hour</SelectItem>
                          <SelectItem value="90">1.5 hours</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>How long do you need?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="timeSlot"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time Slot</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={
                        !watchLawyerId ||
                        !watchConsultationDate ||
                        isLoadingTimeSlots ||
                        isSubmitting
                      }
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a time slot" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {isLoadingTimeSlots ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading time slots...</span>
                          </div>
                        ) : availableTimeSlots.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No available time slots for this date
                          </div>
                        ) : (
                          availableTimeSlots.map((slot, index) => (
                            <SelectItem
                              key={index}
                              value={format(new Date(slot.start), 'HH:mm')}
                            >
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4" />
                                <span>
                                  {formatTimeSlot(slot.start)} -{' '}
                                  {formatTimeSlot(slot.end)}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select an available time slot
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="documentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document (Optional)</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={documentsLoading || isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a document (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No document</SelectItem>
                        {documentsLoading ? (
                          <div className="flex items-center justify-center py-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading documents...</span>
                          </div>
                        ) : documents.length === 0 ? (
                          <div className="p-2 text-center text-sm text-muted-foreground">
                            No documents available
                          </div>
                        ) : (
                          documents.map((document) => (
                            <SelectItem key={document.id} value={document.id}>
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4" />
                                <span>{document.title}</span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optionally select a document to discuss during the
                      consultation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any specific questions or topics you'd like to discuss..."
                        className="resize-none"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide any additional information for the lawyer
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Schedule Consultation
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </Drawer.Content>
    </Drawer.Root>
  );
}
