'use client';

import { userStore } from '@/lib/store/user';
import { useEffect, useState } from 'react';
import RealtimeService from '@/lib/supabase/realtime/realtime-service';
import SafeRealtimeClient from '@/lib/supabase/realtime/safe-realtime-client';

/**
 * RealtimeProvider
 *
 * This component sets up realtime subscriptions for the application.
 * It should be placed high in the component tree, typically in the root layout.
 *
 * Implements Supabase Realtime Broadcast from Database functionality
 * as described in https://supabase.com/blog/realtime-broadcast-from-database
 */
export function RealtimeProvider({ children }: { children: React.ReactNode }) {
  const { user } = userStore();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Set up authentication for realtime
  useEffect(() => {
    if (!user) {
      setIsAuthenticated(false);
      return;
    }

    // Initialize Realtime authentication using our enhanced service
    const initAuth = async () => {
      try {
        const success = await RealtimeService.initializeRealtimeAuth();
        setIsAuthenticated(success);
      } catch (error) {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Error setting up realtime authentication:', error);
        }
        setIsAuthenticated(false);
      }
    };

    initAuth();
  }, [user]);

  // Clean up all subscriptions when the component unmounts or user changes
  useEffect(() => {
    // Clean up function that will be called on unmount or when dependencies change
    return () => {
      try {
        RealtimeService.unsubscribeAll();
      } catch (error) {
        // Only log in development
        if (process.env.NODE_ENV === 'development') {
          console.error('Error cleaning up realtime subscriptions:', error);
        }
      }
    };
  }, [user]); // Re-run when user changes

  return <>{children}</>;
}

export default RealtimeProvider;
