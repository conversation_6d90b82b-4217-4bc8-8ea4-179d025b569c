import {
  createGoogleCalendarEvent,
  deleteGoogleCalendarEvent,
  refreshAccessToken,
  updateGoogleCalendarEvent,
} from '@/lib/services/google-calendar';
import { consultationToCalendarEvent } from '@/lib/utils/calendar-utils';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll: async () => Array.from((await cookies()).getAll()),
          setAll: async (cookiesArray) => {
            const cookieStore = await cookies();
            for (const { name, value, options } of cookiesArray) {
              cookieStore.set(name, value, options);
            }
          },
        },
      }
    );

    // Get the current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse the request body
    const { consultationId, action } = await request.json();

    if (!consultationId || !action) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Get the consultation
    const { data: consultation, error: consultationError } = await supabase
      .from('lawyer_consultations')
      .select(
        `
        *,
        lawyer:lawyer_id (
          id,
          full_name,
          email,
          avatar_url,
          specialization
        ),
        client:user_id (
          id,
          full_name,
          email,
          avatar_url
        ),
        document:document_id (
          id,
          title
        )
      `
      )
      .eq('id', consultationId)
      .single();

    if (consultationError || !consultation) {
      return NextResponse.json(
        { error: 'Consultation not found' },
        { status: 404 }
      );
    }

    // Check if the user is authorized to access this consultation
    if (
      consultation.user_id !== user.id &&
      consultation.lawyer?.id !== user.id
    ) {
      return NextResponse.json(
        { error: 'Unauthorized to access this consultation' },
        { status: 403 }
      );
    }

    // Get the user's Google Calendar integration
    const { data: integration, error: integrationError } = await supabase
      .from('calendar_integrations')
      .select('*')
      .eq('user_id', user.id)
      .eq('provider', 'google')
      .single();

    if (integrationError || !integration) {
      return NextResponse.json(
        { error: 'Google Calendar integration not found' },
        { status: 404 }
      );
    }

    // Check if the access token is expired and refresh if needed
    const now = new Date();
    const tokenExpiresAt = new Date(integration.token_expires_at);

    let accessToken = integration.access_token;

    if (now >= tokenExpiresAt) {
      try {
        const { access_token, expires_in } = await refreshAccessToken(
          integration.refresh_token
        );

        // Update the access token and expiration date
        accessToken = access_token;
        const newExpiresAt = new Date();
        newExpiresAt.setSeconds(newExpiresAt.getSeconds() + expires_in);

        // Update the database
        await supabase
          .from('calendar_integrations')
          .update({
            access_token,
            token_expires_at: newExpiresAt.toISOString(),
            updated_at: now.toISOString(),
          })
          .eq('id', integration.id);
      } catch (error) {
        console.error('Error refreshing access token:', error);
        return NextResponse.json(
          { error: 'Failed to refresh access token' },
          { status: 500 }
        );
      }
    }

    // Convert the consultation to a calendar event
    const calendarEvent = consultationToCalendarEvent(consultation);

    // Perform the requested action
    switch (action) {
      case 'create': {
        try {
          // Create the event in Google Calendar
          const externalEventId = await createGoogleCalendarEvent(
            accessToken,
            integration.calendar_id || 'primary',
            calendarEvent
          );

          // Store the external event ID in the database
          await supabase.from('consultation_calendar_events').upsert(
            {
              consultation_id: consultationId,
              user_id: user.id,
              external_event_id: externalEventId,
              provider: 'google',
              last_synced_at: now.toISOString(),
              updated_at: now.toISOString(),
            },
            {
              onConflict: 'consultation_id,user_id,provider',
            }
          );

          return NextResponse.json({ success: true, externalEventId });
        } catch (error) {
          console.error('Error creating Google Calendar event:', error);
          return NextResponse.json(
            { error: 'Failed to create Google Calendar event' },
            { status: 500 }
          );
        }
      }

      case 'update': {
        try {
          // Get the external event ID
          const { data: calendarEvent, error: calendarEventError } =
            await supabase
              .from('consultation_calendar_events')
              .select('external_event_id')
              .eq('consultation_id', consultationId)
              .eq('user_id', user.id)
              .eq('provider', 'google')
              .single();

          if (calendarEventError || !calendarEvent) {
            return NextResponse.json(
              { error: 'Calendar event not found' },
              { status: 404 }
            );
          }

          // Update the event in Google Calendar
          await updateGoogleCalendarEvent(
            accessToken,
            integration.calendar_id || 'primary',
            calendarEvent.external_event_id,
            calendarEvent
          );

          // Update the last synced timestamp
          await supabase
            .from('consultation_calendar_events')
            .update({
              last_synced_at: now.toISOString(),
              updated_at: now.toISOString(),
            })
            .eq('consultation_id', consultationId)
            .eq('user_id', user.id)
            .eq('provider', 'google');

          return NextResponse.json({ success: true });
        } catch (error) {
          console.error('Error updating Google Calendar event:', error);
          return NextResponse.json(
            { error: 'Failed to update Google Calendar event' },
            { status: 500 }
          );
        }
      }

      case 'delete': {
        try {
          // Get the external event ID
          const { data: calendarEvent, error: calendarEventError } =
            await supabase
              .from('consultation_calendar_events')
              .select('external_event_id')
              .eq('consultation_id', consultationId)
              .eq('user_id', user.id)
              .eq('provider', 'google')
              .single();

          if (calendarEventError || !calendarEvent) {
            return NextResponse.json(
              { error: 'Calendar event not found' },
              { status: 404 }
            );
          }

          // Delete the event from Google Calendar
          await deleteGoogleCalendarEvent(
            accessToken,
            integration.calendar_id || 'primary',
            calendarEvent.external_event_id
          );

          // Delete the record from the database
          await supabase
            .from('consultation_calendar_events')
            .delete()
            .eq('consultation_id', consultationId)
            .eq('user_id', user.id)
            .eq('provider', 'google');

          return NextResponse.json({ success: true });
        } catch (error) {
          console.error('Error deleting Google Calendar event:', error);
          return NextResponse.json(
            { error: 'Failed to delete Google Calendar event' },
            { status: 500 }
          );
        }
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in calendar sync route:', error);
    return NextResponse.json(
      { error: 'Failed to sync calendar event' },
      { status: 500 }
    );
  }
}
