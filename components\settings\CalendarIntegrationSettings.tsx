'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { useCalendarIntegration } from '@/lib/hooks';
import { getUserCalendars } from '@/lib/services/google-calendar';
import { Calendar, Loader2, RefreshCw, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

interface CalendarIntegrationSettingsProps {
  redirectUrl?: string;
}

export function CalendarIntegrationSettings({
  redirectUrl,
}: CalendarIntegrationSettingsProps) {
  const {
    integrations,
    loading,
    error,
    connectGoogleCalendar,
    disconnectGoogleCalendar,
    updateCalendarId,
    toggleSyncEnabled,
    isProviderConnected,
    getIntegration,
  } = useCalendarIntegration();

  const [calendars, setCalendars] = useState<{ id: string; summary: string }[]>(
    []
  );
  const [loadingCalendars, setLoadingCalendars] = useState(false);
  const [selectedCalendarId, setSelectedCalendarId] = useState<string>('');
  const [syncEnabled, setSyncEnabled] = useState(true);

  // Load user's Google Calendars
  const loadGoogleCalendars = async () => {
    const googleIntegration = getIntegration('google');
    if (!googleIntegration) return;

    setLoadingCalendars(true);
    try {
      const userCalendars = await getUserCalendars(
        googleIntegration.access_token
      );
      setCalendars(userCalendars);

      // Set the selected calendar ID
      if (googleIntegration.calendar_id) {
        setSelectedCalendarId(googleIntegration.calendar_id);
      } else if (userCalendars.length > 0) {
        // Find the primary calendar or use the first one
        const primaryCalendar = userCalendars.find(
          (calendar) => calendar.primary
        );
        setSelectedCalendarId(primaryCalendar?.id || userCalendars[0].id);
      }

      // Set sync enabled
      setSyncEnabled(googleIntegration.sync_enabled);
    } catch (err) {
      console.error('Error loading Google Calendars:', err);
      toast.error('Failed to load Google Calendars');
    } finally {
      setLoadingCalendars(false);
    }
  };

  // Handle Google Calendar connection
  const handleConnectGoogle = () => {
    connectGoogleCalendar(redirectUrl);
  };

  // Handle Google Calendar disconnection
  const handleDisconnectGoogle = async () => {
    if (confirm('Are you sure you want to disconnect from Google Calendar?')) {
      await disconnectGoogleCalendar();
    }
  };

  // Handle calendar selection
  const handleCalendarChange = async (calendarId: string) => {
    setSelectedCalendarId(calendarId);
    await updateCalendarId('google', calendarId);
  };

  // Handle sync toggle
  const handleSyncToggle = async (enabled: boolean) => {
    setSyncEnabled(enabled);
    await toggleSyncEnabled('google', enabled);
  };

  // Load Google Calendars when connected
  useEffect(() => {
    if (isProviderConnected('google')) {
      loadGoogleCalendars();
    }
  }, [isProviderConnected]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Calendar Integration</CardTitle>
        <CardDescription>
          Connect your calendar to automatically sync consultations
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <div className="text-destructive">
            Error loading calendar integrations. Please try again.
          </div>
        ) : (
          <>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <div>
                    <h3 className="font-medium">Google Calendar</h3>
                    <p className="text-sm text-muted-foreground">
                      Sync your consultations with Google Calendar
                    </p>
                  </div>
                </div>

                {isProviderConnected('google') ? (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDisconnectGoogle}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Disconnect
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleConnectGoogle}
                  >
                    Connect
                  </Button>
                )}
              </div>

              {isProviderConnected('google') && (
                <div className="space-y-4 border rounded-md p-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="sync-enabled">Enable Calendar Sync</Label>
                    <Switch
                      id="sync-enabled"
                      checked={syncEnabled}
                      onCheckedChange={handleSyncToggle}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="calendar-select">Select Calendar</Label>
                    <div className="flex gap-2">
                      <Select
                        value={selectedCalendarId}
                        onValueChange={handleCalendarChange}
                        disabled={loadingCalendars}
                      >
                        <SelectTrigger id="calendar-select" className="flex-1">
                          <SelectValue placeholder="Select a calendar" />
                        </SelectTrigger>
                        <SelectContent>
                          {calendars.map((calendar) => (
                            <SelectItem key={calendar.id} value={calendar.id}>
                              {calendar.summary}
                              {calendar.primary && ' (Primary)'}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Button
                        variant="outline"
                        size="icon"
                        onClick={loadGoogleCalendars}
                        disabled={loadingCalendars}
                      >
                        {loadingCalendars ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    <p>When enabled:</p>
                    <ul className="list-disc list-inside ml-2 mt-1">
                      <li>New consultations will be added to your calendar</li>
                      <li>Updated consultations will be synced</li>
                      <li>Cancelled consultations will be removed</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>

      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Last updated: {new Date().toLocaleDateString()}
        </div>
      </CardFooter>
    </Card>
  );
}
