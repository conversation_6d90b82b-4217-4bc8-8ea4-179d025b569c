'use client';

import { But<PERSON> } from '@/components/ui/button';
import { FONT_BIRCOLAGE_GROTESQUE, FONT_JETBRAINS_MONO } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { ArrowUp } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { PlanSheet, PlanType } from './plan-sheet';

export function UserPlanDetails() {
  const [currentPlan, setCurrentPlan] = useState<PlanType>('free');
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const handleUpgrade = (plan: PlanType) => {
    // In a real app, this would call an API to upgrade the plan
    setCurrentPlan(plan);
    toast.success(`Your plan has been upgraded to ${plan}`);
  };

  const getPlanDetails = (plan: PlanType) => {
    switch (plan) {
      case 'free':
        return {
          name: 'Free Plan',
          limit: '5 docs',
          color: 'bg-neutral-100 text-neutral-800',
        };
      case 'pro':
        return {
          name: 'Pro Plan',
          limit: 'Unlimited docs',
          color: 'bg-accent-100 text-accent-800',
        };
      case 'business':
        return {
          name: 'Business Plan',
          limit: 'Team features',
          color: 'bg-indigo-100 text-indigo-800',
        };
    }
  };

  const planDetails = getPlanDetails(currentPlan);

  return (
    <div
      className={cn(
        FONT_BIRCOLAGE_GROTESQUE.className,
        'flex items-center w-full justify-center pb-2'
      )}
    >
      <Button
        variant={'shadow_accent'}
        size={'sm'}
        onClick={() => setIsSheetOpen(true)}
        className={cn(' hover:bg-accent-50/20 rounded-md h-6 px-2')}
      >
        <div className="flex items-center">
          <span
            className={cn(FONT_JETBRAINS_MONO.className, 'uppercase text-xs')}
          >
            {planDetails.name} {planDetails.limit}{' '}
            {currentPlan !== 'business' && (
              <span className="text-xs text-muted-foreground">| Upgrade</span>
            )}
          </span>
        </div>
      </Button>

      <PlanSheet
        open={isSheetOpen}
        onOpenChange={setIsSheetOpen}
        currentPlan={currentPlan}
        onUpgrade={handleUpgrade}
      />
    </div>
  );
}
