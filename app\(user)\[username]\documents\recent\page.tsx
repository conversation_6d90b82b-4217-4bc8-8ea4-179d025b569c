import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { pageProps } from '@/types';
import {
  Clock,
  FileText,
  History,
  MoreHorizontal,
  Star,
  User,
} from 'lucide-react';

export default async function RecentDocumentsPage({ params }: pageProps) {
  const username = (await params).username;

  // Mock recent documents data
  const recentDocuments = [
    {
      id: 1,
      title: 'Contract Agreement',
      lastOpened: '2 hours ago',
      lastEdited: '3 hours ago',
      editedBy: 'You',
      type: 'Contract',
      size: '245 KB',
    },
    {
      id: 2,
      title: 'NDA Template',
      lastOpened: 'Yesterday',
      lastEdited: '2 days ago',
      editedBy: '<PERSON>',
      type: 'Template',
      size: '180 KB',
    },
    {
      id: 3,
      title: 'Partnership Agreement',
      lastOpened: '3 days ago',
      lastEdited: '5 days ago',
      editedBy: '<PERSON>',
      type: 'Agreement',
      size: '320 KB',
    },
    {
      id: 4,
      title: 'Service Contract',
      lastOpened: '1 week ago',
      lastEdited: '1 week ago',
      editedBy: 'You',
      type: 'Contract',
      size: '210 KB',
    },
  ];

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center gap-2">
          <History className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Recent Documents</h1>
        </div>
        <p className="text-neutral-500 mt-1">
          Documents you&apos;ve recently opened or edited
        </p>
      </section>

      <section>
        <div className="bg-white rounded-lg border border-neutral-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <h2 className="font-medium">Recently Accessed</h2>
            <Button variant="ghost" size="sm">
              Clear History
            </Button>
          </div>

          <div className="divide-y divide-neutral-200">
            {recentDocuments.map((doc) => (
              <div key={doc.id} className="px-6 py-4 flex items-center">
                <div className="bg-neutral-100 size-10 rounded flex items-center justify-center mr-4">
                  <FileText className="size-5 text-neutral-500" />
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="font-medium truncate">{doc.title}</h3>
                  <div className="flex gap-4 text-sm text-neutral-500 mt-1">
                    <div className="flex items-center gap-1">
                      <Clock className="size-3.5" />
                      <span>Opened {doc.lastOpened}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="size-3.5" />
                      <span>
                        Last edited by{' '}
                        <span className="font-medium">{doc.editedBy}</span>
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 ml-4">
                  <div className="text-right text-sm">
                    <div className="font-medium">{doc.type}</div>
                    <div className="text-neutral-500">{doc.size}</div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <Star className="size-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>Open</DropdownMenuItem>
                        <DropdownMenuItem>Make a Copy</DropdownMenuItem>
                        <DropdownMenuItem>Share</DropdownMenuItem>
                        <Separator />
                        <DropdownMenuItem>Remove from Recent</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {recentDocuments.length === 0 && (
            <div className="p-10 text-center">
              <History className="size-10 text-neutral-300 mx-auto mb-2" />
              <h3 className="font-medium text-neutral-600">
                No recent documents
              </h3>
              <p className="text-neutral-500 text-sm mt-1">
                Documents you open will appear here
              </p>
            </div>
          )}
        </div>
      </section>
    </main>
  );
}
