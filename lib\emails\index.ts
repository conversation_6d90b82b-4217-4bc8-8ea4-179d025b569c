import { Resend } from 'resend';

// Initialize Resend with API key
const resendApiKey =
  process.env.NEXT_PUBLIC_RESEND_API_KEY || process.env.RESEND_API_KEY;

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Create a conditional initialization to avoid errors in browser
export const resend = isBrowser
  ? new Resend('placeholder_for_browser') // This won't be used in browser
  : new Resend(resendApiKey);

// Email sender configuration
export const emailConfig = {
  from: 'NotAMess Forms <<EMAIL>>',
  replyTo: '<EMAIL>',
};

// Email template types
export type EmailTemplateProps =
  | VerificationEmailProps
  | ResetPasswordEmailProps
  | DocumentNotificationEmailProps;

// Verification email props
export interface VerificationEmailProps {
  type: 'verification';
  email: string;
  verificationLink: string;
  userName?: string;
}

// Reset password email props
export interface ResetPasswordEmailProps {
  type: 'resetPassword';
  email: string;
  resetLink: string;
  userName?: string;
}

// Document notification email props
export interface DocumentNotificationEmailProps {
  type: 'documentNotification';
  email: string;
  documentLink: string;
  userName?: string;
  documentName: string;
  notificationType: 'shared' | 'updated' | 'commented' | 'signed';
  senderName: string;
  message?: string;
}

// Send email function
export async function sendEmail(props: EmailTemplateProps) {
  try {
    if (props.type === 'verification') {
      return await sendVerificationEmail(props);
    } else if (props.type === 'resetPassword') {
      return await sendResetPasswordEmail(props);
    } else if (props.type === 'documentNotification') {
      return await sendDocumentNotificationEmail(props);
    }
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

// Send verification email
async function sendVerificationEmail(props: VerificationEmailProps) {
  const { email, verificationLink, userName } = props;

  // Import the React component directly
  const { VerificationEmail } = await import('./templates/verification-email');

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: 'Verify your email address',
    react: VerificationEmail({
      verificationLink,
      userName: userName || email.split('@')[0],
    }),
  });
}

// Send reset password email
async function sendResetPasswordEmail(props: ResetPasswordEmailProps) {
  const { email, resetLink, userName } = props;

  // Import the React component directly
  const { ResetPasswordEmail } = await import(
    './templates/reset-password-email'
  );

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: 'Reset your password',
    react: ResetPasswordEmail({
      resetLink,
      userName: userName || email.split('@')[0],
    }),
  });
}

// Send document notification email
async function sendDocumentNotificationEmail(
  props: DocumentNotificationEmailProps
) {
  const {
    email,
    documentLink,
    userName,
    documentName,
    notificationType,
    senderName,
    message,
  } = props;

  // Generate subject based on notification type
  let subject = '';
  switch (notificationType) {
    case 'shared':
      subject = `${senderName} shared a document with you`;
      break;
    case 'updated':
      subject = `${senderName} updated "${documentName}"`;
      break;
    case 'commented':
      subject = `${senderName} commented on "${documentName}"`;
      break;
    case 'signed':
      subject = `${senderName} signed "${documentName}"`;
      break;
    default:
      subject = `Notification about "${documentName}"`;
  }

  // Import the React component directly
  const { DocumentNotificationEmail } = await import(
    './templates/document-notification-email'
  );

  return await resend.emails.send({
    from: emailConfig.from,
    to: email,
    subject: subject,
    react: DocumentNotificationEmail({
      documentLink,
      userName: userName || email.split('@')[0],
      documentName,
      notificationType,
      senderName,
      message,
    }),
  });
}
