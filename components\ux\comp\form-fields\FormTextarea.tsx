'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import * as Textarea from '@/components/ui/textarea';
import { useFormContext } from 'react-hook-form';

type FormTextareaProps = {
  name: string;
  label?: string;
  labelHelper?: string;
  placeholder?: string;
  maxLength?: number;
} & React.TextareaHTMLAttributes<HTMLTextAreaElement>;

const FormTextarea = ({
  name,
  label,
  labelHelper,
  placeholder,
  ...props
}: FormTextareaProps) => {
  const { control } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel className="ml-2 text-sm text-neutral-400">{`${label}:`}</FormLabel>
          )}
          {labelHelper && <span className="text-xs"> {labelHelper}</span>}
          <div className="flex items-center justify-between gap-5 text-sm">
            <div>
              <FormControl>
                <Textarea.Root
                  {...field}
                  placeholder={placeholder}
                  className="w-[15rem]"
                  {...props}
                >
                  {props.maxLength && (
                    <Textarea.CharCounter
                      current={field.value?.length || 0}
                      max={props.maxLength}
                    />
                  )}
                </Textarea.Root>
              </FormControl>
              <FormMessage />
            </div>
          </div>
        </FormItem>
      )}
    />
  );
};

export default FormTextarea;
