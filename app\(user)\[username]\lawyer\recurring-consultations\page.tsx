'use client';

import { useRouter } from 'next/navigation';
import { RecurringConsultationsList } from '@/components/lawyer/RecurringConsultationsList';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { userStore } from '@/lib/store/user';
import { ArrowLeft, Calendar, Repeat } from 'lucide-react';

export default function RecurringConsultationsPage() {
  const router = useRouter();
  const { profile } = userStore();
  const username = profile?.username;
  
  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold">Recurring Consultations</h1>
          <p className="text-muted-foreground">
            Manage your recurring consultation schedules
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/${username}/lawyer/dashboard`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <Button
            onClick={() => router.push(`/${username}/lawyer/calendar`)}
          >
            <Calendar className="h-4 w-4 mr-2" />
            View Calendar
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="active">
        <TabsList className="mb-6">
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="all">All</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active">
          <RecurringConsultationsList />
        </TabsContent>
        
        <TabsContent value="all">
          <RecurringConsultationsList />
        </TabsContent>
      </Tabs>
      
      <div className="mt-8 bg-muted p-6 rounded-lg">
        <div className="flex items-start gap-4">
          <div className="bg-primary/10 p-3 rounded-full">
            <Repeat className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-medium mb-2">About Recurring Consultations</h3>
            <p className="text-muted-foreground mb-4">
              Recurring consultations allow you to schedule regular meetings with clients without having to create each appointment individually.
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Set up weekly, bi-weekly, or monthly consultations</li>
              <li>Choose the day of the week and time that works best</li>
              <li>Consultations are automatically scheduled in advance</li>
              <li>Clients receive notifications for each scheduled consultation</li>
              <li>You can pause or cancel a recurring schedule at any time</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
