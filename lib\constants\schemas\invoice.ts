import { z } from 'zod';
import { formatNumberWithCommas } from '../helpers';
import { DATE_OPTIONS } from '../variables';

// Helpers

// Variables

// TODO: Refactor some of the validators. Ex: name and zipCode or address and country have same rules
// Field Validators
const fieldValidators = {
  name: z
    .string()
    .min(2, { message: 'Must be at least 2 characters' })
    .max(50, { message: 'Must be at most 50 characters' }),
  address: z
    .string()
    .min(2, { message: 'Must be at least 2 characters' })
    .max(70, { message: 'Must be between 2 and 70 characters' }),
  zipCode: z
    .string()
    .min(2, { message: 'Must be between 2 and 20 characters' })
    .max(20, { message: 'Must be between 2 and 20 characters' }),
  city: z
    .string()
    .min(1, { message: 'Must be between 1 and 50 characters' })
    .max(50, { message: 'Must be between 1 and 50 characters' }),
  country: z
    .string()
    .min(1, { message: 'Must be between 1 and 70 characters' })
    .max(70, { message: 'Must be between 1 and 70 characters' }),
  email: z
    .string()
    .email({ message: 'Email must be a valid email' })
    .min(5, { message: 'Must be between 5 and 30 characters' })
    .max(30, { message: 'Must be between 5 and 30 characters' }),
  phone: z
    .string()
    .min(1, { message: 'Must be between 1 and 50 characters' })
    .max(50, {
      message: 'Must be between 1 and 50 characters',
    }),

  // Dates
  date: z
    .date()
    .transform((date) =>
      new Date(date).toLocaleDateString('en-US', DATE_OPTIONS)
    ),

  // Items
  quantity: z.coerce
    .number()
    .min(1, { message: 'Must be a number greater than 0' }),
  unitPrice: z.coerce
    .number()
    .min(1, { message: 'Must be a number greater than 0' }),

  // Strings
  string: z.string(),
  stringMin1: z.string().min(1, { message: 'Must be at least 1 character' }),
  stringToNumber: z.coerce.number(),

  // Charges
  stringToNumberWithMax: z.coerce.number().max(1000000),

  stringOptional: z.string().optional(),

  nonNegativeNumber: z.coerce.number().nonnegative({
    message: 'Must be a positive number',
  }),
  // ! This is unused
  numWithCommas: z.coerce
    .number()
    .nonnegative({
      message: 'Must be a positive number',
    })
    .transform((value) => {
      return formatNumberWithCommas(value);
    }),
};

const CustomInputSchema = z.object({
  key: z.string(),
  value: z.string(),
});

const InvoiceSenderSchema = z.object({
  name: fieldValidators.name,
  address: fieldValidators.address,
  zipCode: fieldValidators.zipCode,
  city: fieldValidators.city,
  country: fieldValidators.country,
  email: fieldValidators.email,
  phone: fieldValidators.phone,
  customInputs: z.array(CustomInputSchema).optional(),
});

const InvoiceReceiverSchema = z.object({
  name: fieldValidators.name,
  address: fieldValidators.address,
  zipCode: fieldValidators.zipCode,
  city: fieldValidators.city,
  country: fieldValidators.country,
  email: fieldValidators.email,
  phone: fieldValidators.phone,
  customInputs: z.array(CustomInputSchema).optional(),
});

const ItemSchema = z.object({
  name: fieldValidators.stringMin1,
  description: fieldValidators.stringOptional,
  quantity: fieldValidators.quantity,
  unitPrice: fieldValidators.unitPrice,
  total: fieldValidators.stringToNumber,
});

const PaymentInformationSchema = z.object({
  bankName: fieldValidators.stringMin1,
  accountName: fieldValidators.stringMin1,
  accountNumber: fieldValidators.stringMin1,
});

const DiscountDetailsSchema = z.object({
  amount: fieldValidators.stringToNumberWithMax,
  amountType: fieldValidators.string,
});

const TaxDetailsSchema = z.object({
  amount: fieldValidators.stringToNumberWithMax,
  taxID: fieldValidators.string,
  amountType: fieldValidators.string,
});

const ShippingDetailsSchema = z.object({
  cost: fieldValidators.stringToNumberWithMax,
  costType: fieldValidators.string,
});

const SignatureSchema = z.object({
  data: fieldValidators.string,
  fontFamily: fieldValidators.string.optional(),
});

const InvoiceDetailsSchema = z.object({
  invoiceLogo: fieldValidators.stringOptional,
  invoiceNumber: fieldValidators.stringMin1,
  invoiceDate: fieldValidators.date,
  dueDate: fieldValidators.date,
  purchaseOrderNumber: fieldValidators.stringOptional,
  currency: fieldValidators.string,
  language: fieldValidators.string,
  items: z.array(ItemSchema),
  paymentInformation: PaymentInformationSchema.optional(),
  taxDetails: TaxDetailsSchema.optional(),
  discountDetails: DiscountDetailsSchema.optional(),
  shippingDetails: ShippingDetailsSchema.optional(),
  subTotal: fieldValidators.nonNegativeNumber,
  totalAmount: fieldValidators.nonNegativeNumber,
  totalAmountInWords: fieldValidators.string,
  additionalNotes: fieldValidators.stringOptional,
  paymentTerms: fieldValidators.stringMin1,
  signature: SignatureSchema.optional(),
  updatedAt: fieldValidators.stringOptional,
  pdfTemplate: z.number(),
});

const InvoiceSchema = z.object({
  sender: InvoiceSenderSchema,
  receiver: InvoiceReceiverSchema,
  details: InvoiceDetailsSchema,
});

export { InvoiceSchema, ItemSchema };
