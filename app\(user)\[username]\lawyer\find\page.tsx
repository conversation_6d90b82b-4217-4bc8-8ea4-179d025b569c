'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { UserAvatar } from '@/components/ui/user-avatar';
import { useLawyers } from '@/lib/hooks';
import {
  Calendar,
  CheckCircle2,
  Gavel,
  Search,
  SlidersHorizontal,
  Star,
  Video,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

type FindLawyerPageProps = {
  params: Promise<{ username: string }>;
};

export default function FindLawyerPage(props: FindLawyerPageProps) {
  // Unwrap the params using React.use()
  const params = React.use(props.params);
  const username = params.username;
  const { loading, lawyers, fetchAllLawyers } = useLawyers();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecializations, setSelectedSpecializations] = useState<
    string[]
  >([]);
  const [minRating, setMinRating] = useState<number>(0);
  const router = useRouter();

  useEffect(() => {
    // Use fetchAllLawyers instead of fetchLawyers
    fetchAllLawyers().catch((error) => {
      console.error('Error fetching lawyers:', error);
    });
  }, [fetchAllLawyers]);

  const handleBookLawyer = (lawyerId: string) => {
    router.push(`/${username}/lawyer/book?lawyer=${lawyerId}`);
  };

  const filteredLawyers = lawyers.filter((lawyer) => {
    if (searchQuery.trim() === '') return true;

    const query = searchQuery.toLowerCase();
    return (
      lawyer.full_name.toLowerCase().includes(query) ||
      (lawyer.specialization &&
        lawyer.specialization.some((s) => s.toLowerCase().includes(query))) ||
      (lawyer.bio && lawyer.bio.toLowerCase().includes(query))
    );
  });

  // Available specializations from all lawyers
  const allSpecializations = Array.from(
    new Set(lawyers.flatMap((lawyer) => lawyer.specialization || []))
  ).sort();

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center gap-2">
          <Gavel className="size-5 text-neutral-500" />
          <h1 className="text-2xl font-bold">Find a Lawyer</h1>
        </div>
        <p className="text-neutral-500 mt-1">
          Connect with experienced legal professionals for specialized
          assistance
        </p>
      </section>

      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-[280px] flex-shrink-0">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Filters</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 text-sm"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedSpecializations([]);
                    setMinRating(0);
                    fetchAllLawyers().catch((error) => {
                      console.error('Error fetching lawyers:', error);
                    });
                  }}
                >
                  Reset
                </Button>
              </div>

              <div className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      type="search"
                      placeholder="Search lawyers..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Specialization</Label>
                  <div className="space-y-2">
                    {allSpecializations.map((spec) => (
                      <div key={spec} className="flex items-center space-x-2">
                        <Checkbox
                          id={`spec-${spec}`}
                          checked={selectedSpecializations.includes(spec)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedSpecializations([
                                ...selectedSpecializations,
                                spec,
                              ]);
                            } else {
                              setSelectedSpecializations(
                                selectedSpecializations.filter(
                                  (s) => s !== spec
                                )
                              );
                            }
                          }}
                        />
                        <Label
                          htmlFor={`spec-${spec}`}
                          className="text-sm font-normal"
                        >
                          {spec}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label>Minimum Rating</Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <Button
                        key={rating}
                        variant={minRating >= rating ? 'default' : 'outline'}
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() =>
                          setMinRating(rating === minRating ? 0 : rating)
                        }
                      >
                        {rating}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex-1">
          <div className="mb-6 flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium">
                {loading
                  ? 'Loading lawyers...'
                  : `${filteredLawyers.length} Lawyers Available`}
              </h2>
              <p className="text-sm text-neutral-500">
                {selectedSpecializations.length > 0 && (
                  <>Filtered by: {selectedSpecializations.join(', ')}</>
                )}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="h-8">
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                Sort
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      <div className="h-16 w-16 rounded-full bg-gray-200"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                        <div className="h-3 w-1/4 bg-gray-200 rounded"></div>
                        <div className="h-3 w-2/3 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredLawyers.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-medium mb-2">No lawyers found</h3>
                <p className="text-neutral-500">
                  Try adjusting your filters or search query
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredLawyers.map((lawyer) => (
                <Card key={lawyer.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="p-6">
                      <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-shrink-0">
                          <div className="relative">
                            <UserAvatar
                              avatarUrl={
                                lawyer.avatar_url || lawyer.profile_image_url
                              }
                              fallbackText={lawyer.full_name}
                              size="xl"
                              className="h-20 w-20"
                            />
                            {lawyer.is_verified && (
                              <div className="absolute bottom-0 right-0 rounded-full bg-white p-0.5">
                                <CheckCircle2 className="h-4 w-4 text-green-500" />
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-2">
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="text-lg font-medium">
                                  {lawyer.full_name}
                                </h3>
                                {lawyer.is_verified && (
                                  <Badge
                                    variant="outline"
                                    className="bg-green-50 text-green-700 border-green-200"
                                  >
                                    Verified
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-1 text-sm text-neutral-500">
                                <Gavel className="h-3.5 w-3.5" />
                                <span>
                                  {lawyer.specialization?.join(', ') ||
                                    'General Practice'}
                                </span>
                              </div>
                              <div className="flex items-center gap-1 text-sm text-neutral-500 mt-0.5">
                                <Calendar className="h-3.5 w-3.5" />
                                <span>
                                  {lawyer.years_experience || 0} years
                                  experience
                                </span>
                              </div>
                            </div>
                            <div className="flex flex-col items-end">
                              <div className="flex items-center gap-1">
                                <div className="flex">
                                  {Array.from({ length: 5 }).map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`h-4 w-4 ${
                                        i < (lawyer.average_rating || 0)
                                          ? 'text-yellow-400 fill-yellow-400'
                                          : 'text-gray-300'
                                      }`}
                                    />
                                  ))}
                                </div>
                                <span className="text-sm font-medium">
                                  {lawyer.average_rating?.toFixed(1) || 'N/A'}
                                </span>
                              </div>
                              <div className="text-sm text-neutral-500">
                                {lawyer.consultation_count || 0} consultations
                              </div>
                            </div>
                          </div>
                          <p className="mt-2 text-sm text-neutral-600 line-clamp-2">
                            {lawyer.bio || 'No bio available'}
                          </p>
                          <div className="mt-3 flex flex-wrap gap-1">
                            {lawyer.languages?.map((lang) => (
                              <Badge
                                key={lang}
                                variant="outline"
                                className="bg-blue-50 text-blue-700 border-blue-200"
                              >
                                {lang}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                    <Separator />
                    <div className="p-4 bg-neutral-50 flex flex-col sm:flex-row items-center justify-between gap-4">
                      <div className="flex items-center gap-6 text-sm">
                        <div>
                          <span className="font-medium">
                            ${lawyer.hourly_rate || 0}
                          </span>
                          <span className="text-neutral-500">/hour</span>
                        </div>
                        <div>
                          <span className="font-medium">
                            ${lawyer.consultation_fee || 0}
                          </span>
                          <span className="text-neutral-500">
                            /consultation
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2 w-full sm:w-auto">
                        <Button
                          variant="outline"
                          className="flex-1 sm:flex-initial"
                          onClick={() =>
                            router.push(
                              `/${username}/lawyer/profile/${lawyer.id}`
                            )
                          }
                        >
                          View Profile
                        </Button>
                        <Button
                          className="flex-1 sm:flex-initial"
                          onClick={() => handleBookLawyer(lawyer.id)}
                        >
                          <Video className="mr-2 h-4 w-4" />
                          Book Consultation
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Booking is handled on the booking page */}
    </main>
  );
}
