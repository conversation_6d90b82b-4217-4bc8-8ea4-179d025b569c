// Define a custom event for notification updates
export const NOTIFICATION_UPDATED_EVENT = 'notification-updated';

/**
 * Trigger a notification update event
 * This can be called from anywhere in the application to force a refresh of notifications
 */
export function triggerNotificationUpdate(): void {
  if (typeof window !== 'undefined') {
    // Create and dispatch a custom event
    const event = new CustomEvent(NOTIFICATION_UPDATED_EVENT);
    window.dispatchEvent(event);
  }
}

/**
 * Add a listener for notification updates
 * @param callback Function to call when notifications are updated
 * @returns A cleanup function to remove the listener
 */
export function addNotificationUpdateListener(callback: () => void): () => void {
  if (typeof window !== 'undefined') {
    window.addEventListener(NOTIFICATION_UPDATED_EVENT, callback);
    return () => window.removeEventListener(NOTIFICATION_UPDATED_EVENT, callback);
  }
  return () => {}; // No-op for SSR
}
