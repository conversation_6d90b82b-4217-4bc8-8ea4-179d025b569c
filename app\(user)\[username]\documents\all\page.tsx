import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { avatar_male_001 } from '@/lib/imgs';
import { pageProps } from '@/types';
import {
  Clock,
  Copy,
  Download,
  FileText,
  Filter,
  Folder,
  Grid3X3,
  List,
  MoreHorizontal,
  Pen,
  Plus,
  Search,
  Share2,
  Star,
  TagI<PERSON>,
  Trash2,
} from 'lucide-react';
import Image from 'next/image';

export default async function AllDocumentsPage({ params }: pageProps) {
  const username = (await params).username;

  // Mock documents data
  const documents = [
    {
      id: 1,
      title: 'Employee NDA Template',
      type: 'Legal',
      lastModified: '2023-12-05T09:32:45',
      modifiedBy: {
        name: 'You',
        avatar: '/avatars/you.jpg',
      },
      size: '245 KB',
      status: 'Published',
      tags: ['HR', 'Legal', 'Template'],
      starred: true,
    },
    {
      id: 2,
      title: 'Series A Financing Term Sheet',
      type: 'Finance',
      lastModified: '2023-12-02T14:23:10',
      modifiedBy: {
        name: 'Sarah Johnson',
        avatar: '/avatars/sarah-johnson.jpg',
      },
      size: '182 KB',
      status: 'Draft',
      tags: ['Finance', 'Confidential'],
      starred: true,
    },
    {
      id: 3,
      title: 'Website Privacy Policy',
      type: 'Legal',
      lastModified: '2023-11-28T11:15:30',
      modifiedBy: {
        name: 'Michael Chen',
        avatar: '/avatars/michael-chen.jpg',
      },
      size: '156 KB',
      status: 'Published',
      tags: ['Legal', 'Website'],
      starred: false,
    },
    {
      id: 4,
      title: 'Employment Agreement',
      type: 'HR',
      lastModified: '2023-11-25T16:42:22',
      modifiedBy: {
        name: 'You',
        avatar: '/avatars/you.jpg',
      },
      size: '210 KB',
      status: 'Draft',
      tags: ['HR', 'Legal'],
      starred: false,
    },
    {
      id: 5,
      title: 'Software License Agreement',
      type: 'Legal',
      lastModified: '2023-11-20T09:54:37',
      modifiedBy: {
        name: 'Elena Rodriguez',
        avatar: '/avatars/elena-rodriguez.jpg',
      },
      size: '320 KB',
      status: 'Published',
      tags: ['Legal', 'Tech'],
      starred: true,
    },
    {
      id: 6,
      title: 'Monthly Financial Report',
      type: 'Finance',
      lastModified: '2023-11-10T13:07:19',
      modifiedBy: {
        name: 'James Wilson',
        avatar: '/avatars/james-wilson.jpg',
      },
      size: '512 KB',
      status: 'Published',
      tags: ['Finance', 'Report'],
      starred: false,
    },
    {
      id: 7,
      title: 'Product Development Agreement',
      type: 'Contract',
      lastModified: '2023-11-05T10:22:45',
      modifiedBy: {
        name: 'You',
        avatar: '/avatars/you.jpg',
      },
      size: '274 KB',
      status: 'Draft',
      tags: ['Product', 'Contract'],
      starred: false,
    },
  ];

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Published':
        return 'bg-green-100 text-green-800';
      case 'Draft':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  return (
    <main className="p-6">
      <section className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <FileText className="size-5 text-neutral-500" />
              <h1 className="text-2xl font-bold">All Documents</h1>
            </div>
            <p className="text-neutral-500 mt-1">
              Manage all your legal documents in one place
            </p>
          </div>
          <Button className="gap-1">
            <Plus className="size-4" />
            <span>New Document</span>
          </Button>
        </div>
      </section>

      <div className="mb-6">
        <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
          <div className="relative w-full md:max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 size-4" />
            <Input className="pl-10" placeholder="Search documents..." />
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <div className="flex items-center">
              <Button variant="outline" size="sm" className="h-9 gap-1">
                <Filter className="size-4" />
                <span>Filters</span>
              </Button>

              <Select defaultValue="newest">
                <SelectTrigger className="w-[160px] ml-2 h-9">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                  <SelectItem value="name_desc">Name (Z-A)</SelectItem>
                  <SelectItem value="size">Size</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs defaultValue="all" className="ml-2">
              <TabsList className="h-9">
                <TabsTrigger value="all" className="px-3">
                  All
                </TabsTrigger>
                <TabsTrigger value="owned" className="px-3">
                  Owned by me
                </TabsTrigger>
                <TabsTrigger value="shared" className="px-3">
                  Shared with me
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex ml-auto">
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <List className="size-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <Grid3X3 className="size-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <input type="checkbox" className="size-4" />
                </TableHead>
                <TableHead className="w-[40px]"></TableHead>
                <TableHead>Document</TableHead>
                <TableHead className="hidden md:table-cell">Type</TableHead>
                <TableHead className="hidden md:table-cell">
                  Last Modified
                </TableHead>
                <TableHead className="hidden lg:table-cell">
                  Modified By
                </TableHead>
                <TableHead className="hidden lg:table-cell">Size</TableHead>
                <TableHead className="w-[100px] text-center">Status</TableHead>
                <TableHead className="w-[60px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {documents.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell>
                    <input type="checkbox" className="size-4" />
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      {doc.starred ? (
                        <Star className="size-4 fill-amber-400 text-amber-400" />
                      ) : (
                        <Star className="size-4" />
                      )}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="size-10 bg-neutral-100 rounded-md flex items-center justify-center flex-shrink-0">
                        <FileText className="size-5 text-neutral-500" />
                      </div>
                      <div>
                        <div className="font-medium">{doc.title}</div>
                        <div className="flex mt-1 gap-1 flex-wrap">
                          {doc.tags.map((tag, i) => (
                            <Badge
                              key={i}
                              variant="outline"
                              className="px-1.5 py-0 text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                      {doc.type}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="flex items-center gap-1 text-sm text-neutral-500">
                      <Clock className="size-3.5" />
                      <span>{formatDate(doc.lastModified)}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-2">
                      <Image
                        src={avatar_male_001}
                        alt={doc.modifiedBy.name}
                        width={24}
                        height={24}
                        className="size-6 rounded-full"
                      />
                      <span className="text-sm">{doc.modifiedBy.name}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell text-sm text-neutral-500">
                    {doc.size}
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge className={getStatusColor(doc.status)}>
                      {doc.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="size-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem className="gap-2">
                          <Pen className="size-4" />
                          <span>Edit</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <Download className="size-4" />
                          <span>Download</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <Copy className="size-4" />
                          <span>Make a Copy</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="gap-2">
                          <Share2 className="size-4" />
                          <span>Share</span>
                        </DropdownMenuItem>
                        <Separator />
                        <DropdownMenuItem className="gap-2 text-red-600">
                          <Trash2 className="size-4" />
                          <span>Delete</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Document Types</h3>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-blue-100 flex items-center justify-center">
                    <FileText className="size-4 text-blue-600" />
                  </div>
                  <span>Legal</span>
                </div>
                <Badge>3</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-green-100 flex items-center justify-center">
                    <FileText className="size-4 text-green-600" />
                  </div>
                  <span>Finance</span>
                </div>
                <Badge>2</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-purple-100 flex items-center justify-center">
                    <FileText className="size-4 text-purple-600" />
                  </div>
                  <span>HR</span>
                </div>
                <Badge>1</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="size-8 rounded-md bg-amber-100 flex items-center justify-center">
                    <FileText className="size-4 text-amber-600" />
                  </div>
                  <span>Contract</span>
                </div>
                <Badge>1</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Document Status</h3>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-100 text-green-800">
                    Published
                  </Badge>
                  <span>Published Documents</span>
                </div>
                <span className="font-medium">4</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className="bg-amber-100 text-amber-800">Draft</Badge>
                  <span>Draft Documents</span>
                </div>
                <span className="font-medium">3</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className="bg-blue-100 text-blue-800">Review</Badge>
                  <span>Under Review</span>
                </div>
                <span className="font-medium">0</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Quick Access</h3>
              <Button variant="ghost" size="sm">
                <Plus className="size-4 mr-1" />
                <span>Add</span>
              </Button>
            </div>
            <div className="space-y-3">
              <Button variant="outline" className="w-full justify-start gap-2">
                <Folder className="size-4 text-blue-600" />
                <span>Legal Documents</span>
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <TagIcon className="size-4 text-amber-600" />
                <span>HR Templates</span>
              </Button>
              <Button variant="outline" className="w-full justify-start gap-2">
                <Star className="size-4 text-purple-600" />
                <span>Starred Items</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
