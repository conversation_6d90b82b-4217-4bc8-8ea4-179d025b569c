'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { PerformanceSummary } from '@/lib/types/database-modules';
import {
  Calendar,
  Clock,
  FileText,
  Video,
  CheckCircle,
  XCircle,
  AlertCircle,
  Repeat,
} from 'lucide-react';

interface PerformanceSummaryCardProps {
  data: PerformanceSummary | null;
  loading?: boolean;
}

export function PerformanceSummaryCard({
  data,
  loading = false,
}: PerformanceSummaryCardProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-16" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Summary</CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
      </Card>
    );
  }

  const metrics = [
    {
      label: 'Total Consultations',
      value: data.total_consultations,
      icon: Calendar,
      color: 'text-blue-500',
    },
    {
      label: 'Completed',
      value: data.completed_consultations,
      icon: CheckCircle,
      color: 'text-green-500',
    },
    {
      label: 'Cancelled',
      value: data.cancelled_consultations,
      icon: XCircle,
      color: 'text-red-500',
    },
    {
      label: 'Upcoming',
      value: data.upcoming_consultations,
      icon: Calendar,
      color: 'text-amber-500',
    },
    {
      label: 'Video Consultations',
      value: data.video_consultations,
      icon: Video,
      color: 'text-purple-500',
    },
    {
      label: 'Document Reviews',
      value: data.document_consultations,
      icon: FileText,
      color: 'text-indigo-500',
    },
    {
      label: 'Recurring',
      value: data.recurring_consultations,
      icon: Repeat,
      color: 'text-teal-500',
    },
    {
      label: 'Avg. Duration',
      value: `${Math.round(data.avg_duration_minutes)} min`,
      icon: Clock,
      color: 'text-cyan-500',
    },
    {
      label: 'Completion Rate',
      value: `${data.completion_rate}%`,
      icon: CheckCircle,
      color: 'text-emerald-500',
    },
    {
      label: 'Cancellation Rate',
      value: `${data.cancellation_rate}%`,
      icon: XCircle,
      color: 'text-rose-500',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Summary</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {metrics.map((metric, index) => (
          <div key={index} className="flex flex-col">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-1">
              <metric.icon className={`h-4 w-4 ${metric.color}`} />
              <span>{metric.label}</span>
            </div>
            <div className="text-2xl font-bold">{metric.value}</div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
