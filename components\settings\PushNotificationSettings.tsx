'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { usePushNotifications } from '@/lib/hooks';
import { Bell, BellOff, Loader2, Smartphone } from 'lucide-react';
import { useState } from 'react';

export function PushNotificationSettings() {
  const { isSupported, isSubscribed, isLoading, subscribe, unsubscribe } =
    usePushNotifications();

  const [isToggling, setIsToggling] = useState(false);

  // Handle toggle
  const handleToggle = async (enabled: boolean) => {
    setIsToggling(true);

    try {
      if (enabled) {
        await subscribe();
      } else {
        await unsubscribe();
      }
    } finally {
      setIsToggling(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
          <CardDescription>
            Receive notifications on your device when you're not using the app
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
          <CardDescription>
            Receive notifications on your device when you're not using the app
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <BellOff className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Not Supported</h3>
            <p className="text-muted-foreground max-w-md">
              Push notifications are not supported in your current browser.
              Please try using a modern browser like Chrome, Firefox, or Edge.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Push Notifications</CardTitle>
        <CardDescription>
          Receive notifications on your device when you're not using the app
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-primary" />
            <Label htmlFor="push-notifications" className="font-medium">
              Enable Push Notifications
            </Label>
          </div>
          <Switch
            id="push-notifications"
            checked={isSubscribed}
            onCheckedChange={handleToggle}
            disabled={isToggling}
          />
        </div>

        <div className="bg-muted p-4 rounded-lg">
          <div className="flex items-start gap-3">
            <Smartphone className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium mb-1">What you'll receive</h4>
              <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                <li>New consultation bookings</li>
                <li>Consultation reminders</li>
                <li>Document updates and comments</li>
                <li>Messages from lawyers or clients</li>
                <li>Important account notifications</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => handleToggle(!isSubscribed)}
          disabled={isToggling}
        >
          {isToggling ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {isSubscribed ? 'Disabling...' : 'Enabling...'}
            </>
          ) : (
            <>
              {isSubscribed ? (
                <>
                  <BellOff className="h-4 w-4 mr-2" />
                  Disable Notifications
                </>
              ) : (
                <>
                  <Bell className="h-4 w-4 mr-2" />
                  Enable Notifications
                </>
              )}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
