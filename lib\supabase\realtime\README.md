# Supabase Realtime Implementation

This directory contains the implementation of Supabase Realtime for the NotAMess Forms application. The implementation uses the Broadcast from Database approach, which offers better performance, more control over the data sent, and lower latency compared to the Postgres Changes approach.

## Files

- `safe-realtime-client.ts`: A wrapper around the Supabase Realtime client that adds error handling and retry logic.
- `realtime-service.ts`: A centralized service for managing realtime subscriptions using Supabase Broadcast.
- `broadcast-helper.ts`: Helper functions for sending broadcast messages.
- `document-realtime.ts`: Implementation of realtime for documents.

## Database Setup

The database setup for realtime is defined in the migration files:

- `lib/supabase/db/migrations/20250601000000_setup_realtime_broadcast.sql`: Initial setup for realtime broadcasting.
- `lib/supabase/db/migrations/20250602000000_update_realtime_broadcast.sql`: Updates to fix issues and ensure compatibility.

These files set up the necessary database functions and triggers for realtime broadcasting.

## How It Works

Supabase Realtime Broadcast from Database works by:

1. Setting up database triggers that call `realtime.broadcast_changes()` when data changes
2. The triggers send messages to specific channels based on the data that changed
3. Clients subscribe to these channels using the Supabase client
4. When a message is received, the client executes a callback function

This approach is more efficient than Postgres Changes because:

- It only sends the data you specify, not the entire record
- It allows you to target specific users or groups with messages
- It has lower latency and better performance

## Hooks

The following hooks are available for using realtime in the application:

- `useNotifications`: Hook for notifications.
- `useTemplatesRealtime`: Hook for templates.
- `useBookingsRealtime`: Hook for lawyer bookings.
- `useCollaborationRealtime`: Hook for collaboration features.

## Provider

The `RealtimeProvider` component sets up realtime subscriptions for the application. It is placed high in the component tree, in the `Providers` component. It handles:

1. Setting up authentication for Realtime
2. Managing the lifecycle of subscriptions
3. Cleaning up subscriptions when the component unmounts

## Usage

### Using Realtime Hooks

To use realtime in a component, import the appropriate hook from `lib/hooks/realtime`:

```tsx
import { useNotifications } from '@/lib/hooks/realtime';

function NotificationsComponent() {
  const { notifications, unreadCount, markAsRead } = useNotifications();

  return (
    <div>
      <h1>Notifications ({unreadCount})</h1>
      <ul>
        {notifications.map((notification) => (
          <li key={notification.id}>
            {notification.title}
            <button onClick={() => markAsRead(notification.id)}>
              Mark as Read
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}
```

### Sending Broadcast Messages

To send a broadcast message, use the `broadcast-helper.ts` functions:

```tsx
import {
  sendBroadcast,
  sendNotification,
} from '@/lib/supabase/realtime/broadcast-helper';

// Send a broadcast message to a specific channel
await sendBroadcast(
  'document:123', // Channel/topic
  'UPDATE', // Event type
  { message: 'Document was updated' } // Payload
);

// Send a notification to a user
await sendNotification(
  'user-123', // User ID
  'New message', // Title
  'You have a new message', // Content
  'message', // Type
  '/messages/123' // Action URL (optional)
);
```

### Creating Custom Realtime Subscriptions

To create a custom realtime subscription, use the `RealtimeService`:

```tsx
import RealtimeService from '@/lib/supabase/realtime/realtime-service';

// Subscribe to a custom channel
const unsubscribe = RealtimeService.subscribeToCustomChannel(
  'custom-channel',
  (payload) => {
    console.log('Received message:', payload);
    // Handle the message
  }
);

// Clean up the subscription when done
unsubscribe();
```

## Benefits

The Broadcast from Database approach offers several benefits over the Postgres Changes approach:

1. **Better Performance**: The Broadcast from Database approach is more efficient and can handle more concurrent users.
2. **More Control**: You can control which fields are sent in the payload, reducing the amount of data transferred.
3. **Lower Latency**: Messages are sent directly from the database, reducing latency.
4. **Targeted Channels**: You can use SQL to selectively send data to specific channels.

## Implementation Details

The implementation uses the following pattern:

1. **Database Triggers**: Triggers are set up on tables to call functions that broadcast changes.
2. **Broadcast Functions**: Functions are defined to broadcast changes to specific channels.
3. **Client Subscriptions**: The client subscribes to channels to receive updates.
4. **Centralized Service**: A centralized service manages subscriptions and provides a clean API for components.
5. **Hooks**: Hooks provide a React-friendly way to use the realtime service.

## Maintenance

When adding new features that require realtime updates, follow these steps:

1. Add a new function to the `realtime-service.ts` file for the new feature.
2. Add a new trigger and function to the migration file.
3. Create a new hook for the feature.
4. Update the README to document the new feature.

## Troubleshooting

If you encounter issues with Realtime:

1. Check that the user is authenticated
2. Verify that the database triggers are set up correctly
3. Look for errors in the browser console
4. Make sure the channel names match between the client and server
5. Check that the payload is valid JSON

## References

- [Supabase Realtime Documentation](https://supabase.com/docs/guides/realtime)
- [Broadcast from Database](https://supabase.com/docs/guides/realtime/broadcast#broadcast-from-the-database)
- [Realtime: Broadcast from Database Blog Post](https://supabase.com/blog/realtime-broadcast-from-database)
