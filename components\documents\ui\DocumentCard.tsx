import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DocCard } from '@/components/ux/comp/doc-card';
import { useDocuments } from '@/lib/hooks/use-supabase';
import { DocumentSummary } from '@/lib/types/database-modules';
import { formatDistanceToNow } from 'date-fns';
import {
  FileText as FileTextIcon,
  PencilIcon,
  Share2 as ShareIcon,
  TagIcon,
  TrashIcon,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { JSX } from 'react';

interface DocumentCardProps {
  document: DocumentSummary;
  username: string;
  onDelete: (id: string) => void;
  onShare?: (id: string) => void;
  getDocumentTypeIcon: (type: string | null) => JSX.Element;
  getStatusColor: (status: string) => string;
  isSelectionMode?: boolean;
  isSelected?: boolean;
  onToggleSelect?: (checked?: boolean | 'indeterminate') => void;
}

export function DocumentCard({
  document: doc,
  username,
  onDelete,
  onShare,
  getDocumentTypeIcon,
  getStatusColor,
  isSelectionMode = false,
  isSelected = false,
  onToggleSelect,
}: DocumentCardProps) {
  const router = useRouter();
  const { deleteDocument } = useDocuments();

  const handleView = () => {
    router.push(`/${username}/documents/${doc.id}`);
  };

  const handleEdit = () => {
    router.push(`/${username}/documents/${doc.id}/edit`);
  };

  const handleCardClick = () => {
    if (isSelectionMode && onToggleSelect) {
      onToggleSelect(!isSelected);
    } else {
      handleView();
    }
  };

  return (
    <DocCard className="">
      <div
        key={doc.id}
        className={`h-full ${isSelectionMode ? 'cursor-pointer' : ''} ${isSelected ? 'ring-2 ring-accent-300 bg-accent-50/10' : ''}`}
        onClick={isSelectionMode ? handleCardClick : undefined}
      >
        <CardHeader className="">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-3">
              {isSelectionMode && (
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={(checked) =>
                    onToggleSelect && onToggleSelect(checked)
                  }
                  onClick={(e) => e.stopPropagation()}
                  className="mr-2"
                />
              )}
              <div className="p-2 bg-neutral-50 rounded-full">
                {getDocumentTypeIcon(doc.document_type)}
              </div>
              <CardTitle className="text-lg">{doc.title}</CardTitle>
            </div>
            <Badge className={getStatusColor(doc.status!)}>
              <div className="flex items-center gap-1">
                <span className="h-2 w-2 rounded-full bg-current"></span>
                <span className="capitalize">{doc.status}</span>
              </div>
            </Badge>
          </div>
          {doc.is_template && (
            <div className="mt-3 mb-1">
              <Badge variant="outline">
                <FileTextIcon className="mr-1 h-3 w-3" />
                Template
              </Badge>
            </div>
          )}
          <CardDescription className="mt-2">
            Updated{' '}
            {formatDistanceToNow(new Date(doc.updated_at!), {
              addSuffix: true,
            })}
          </CardDescription>
        </CardHeader>
        <CardContent className="px-6 py-4">
          <p className="text-sm text-muted-foreground line-clamp-2">
            {doc.description || 'No description provided'}
          </p>
          {Array.isArray(doc.tags) && doc.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-3">
              {doc.tags.map((tag) => (
                <Badge
                  key={typeof tag === 'string' ? tag : JSON.stringify(tag)}
                  variant="secondary"
                  className="text-xs"
                >
                  <TagIcon className="mr-1 h-3 w-3" />
                  {typeof tag === 'string' ? tag : JSON.stringify(tag)}
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
        {!isSelectionMode && (
          <CardFooter className="flex justify-between pt-4 pb-6 px-6 border-t">
            <Button variant="ghost" size="sm" onClick={handleView}>
              View
            </Button>
            <div className="flex space-x-2">
              {onShare && (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => onShare(doc.id!)}
                >
                  <ShareIcon className="h-4 w-4" />
                  <span className="sr-only">Share</span>
                </Button>
              )}
              <Button size="icon" variant="ghost" onClick={handleEdit}>
                <PencilIcon className="h-4 w-4" />
                <span className="sr-only">Edit</span>
              </Button>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => {
                  if (onDelete) {
                    onDelete(doc.id!);
                  } else {
                    deleteDocument.mutate({ id: doc.id! });
                  }
                }}
              >
                <TrashIcon className="h-4 w-4" />
                <span className="sr-only">Delete</span>
              </Button>
            </div>
          </CardFooter>
        )}
      </div>
    </DocCard>
  );
}
